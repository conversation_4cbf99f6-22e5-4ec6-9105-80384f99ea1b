import 'dart:async';
import 'package:flutter/material.dart';

/// Game timer utility for managing various timing needs in games
class GameTimer {
  Timer? _timer;
  DateTime? _startTime;
  Duration _elapsed = Duration.zero;
  Duration? _pausedAt;
  bool _isRunning = false;
  bool _isPaused = false;
  
  final Duration? _duration;
  final VoidCallback? _onTick;
  final VoidCallback? _onComplete;
  final Duration _tickInterval;

  GameTimer({
    Duration? duration,
    VoidCallback? onTick,
    VoidCallback? onComplete,
    Duration tickInterval = const Duration(milliseconds: 16), // ~60 FPS
  }) : _duration = duration,
       _onTick = onTick,
       _onComplete = onComplete,
       _tickInterval = tickInterval;

  /// Start the timer
  void start() {
    if (_isRunning && !_isPaused) return;
    
    _isRunning = true;
    _isPaused = false;
    _startTime = DateTime.now();
    
    if (_pausedAt != null) {
      // Resume from pause
      _startTime = _startTime!.subtract(_pausedAt!);
      _pausedAt = null;
    }
    
    _timer = Timer.periodic(_tickInterval, _onTimerTick);
  }

  /// Pause the timer
  void pause() {
    if (!_isRunning || _isPaused) return;
    
    _isPaused = true;
    _pausedAt = _elapsed;
    _timer?.cancel();
  }

  /// Resume the timer
  void resume() {
    if (!_isRunning || !_isPaused) return;
    start();
  }

  /// Stop the timer
  void stop() {
    _isRunning = false;
    _isPaused = false;
    _timer?.cancel();
    _elapsed = Duration.zero;
    _startTime = null;
    _pausedAt = null;
  }

  /// Reset the timer
  void reset() {
    stop();
    _elapsed = Duration.zero;
  }

  /// Restart the timer
  void restart() {
    reset();
    start();
  }

  void _onTimerTick(Timer timer) {
    if (_startTime == null) return;
    
    _elapsed = DateTime.now().difference(_startTime!);
    
    // Check if duration is reached
    if (_duration != null && _elapsed >= _duration) {
      _elapsed = _duration;
      stop();
      _onComplete?.call();
      return;
    }
    
    _onTick?.call();
  }

  /// Get current elapsed time
  Duration get elapsed => _elapsed;

  /// Get remaining time (if duration is set)
  Duration get remaining {
    if (_duration == null) return Duration.zero;
    final rem = _duration - _elapsed;
    return rem.isNegative ? Duration.zero : rem;
  }

  /// Check if timer is running
  bool get isRunning => _isRunning && !_isPaused;

  /// Check if timer is paused
  bool get isPaused => _isPaused;

  /// Check if timer is stopped
  bool get isStopped => !_isRunning;

  /// Check if timer is completed (reached duration)
  bool get isCompleted => _duration != null && _elapsed >= _duration;

  /// Get progress as a value between 0.0 and 1.0
  double get progress {
    if (_duration == null) return 0.0;
    return (_elapsed.inMicroseconds / _duration.inMicroseconds).clamp(0.0, 1.0);
  }

  /// Dispose the timer
  void dispose() {
    _timer?.cancel();
  }
}

/// Countdown timer utility
class CountdownTimer extends GameTimer {
  CountdownTimer({
    required Duration super.duration,
    super.onTick,
    super.onComplete,
    super.tickInterval = const Duration(seconds: 1),
  });

  /// Get remaining time formatted as MM:SS
  String get formattedTime {
    final rem = remaining;
    final minutes = rem.inMinutes;
    final seconds = rem.inSeconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }

  /// Get remaining seconds
  int get remainingSeconds => remaining.inSeconds;

  /// Get remaining minutes
  int get remainingMinutes => remaining.inMinutes;

  /// Check if timer is in warning zone (last 30 seconds)
  bool get isWarning => remainingSeconds <= 30 && remainingSeconds > 10;

  /// Check if timer is in critical zone (last 10 seconds)
  bool get isCritical => remainingSeconds <= 10;
}

/// Stopwatch timer utility
class StopwatchTimer extends GameTimer {
  StopwatchTimer({
    super.onTick,
    super.tickInterval = const Duration(milliseconds: 10),
  });

  /// Get elapsed time formatted as MM:SS.MS
  String get formattedTime {
    final minutes = elapsed.inMinutes;
    final seconds = elapsed.inSeconds % 60;
    final milliseconds = (elapsed.inMilliseconds % 1000) ~/ 10;
    return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}.${milliseconds.toString().padLeft(2, '0')}';
  }

  /// Get elapsed seconds
  int get elapsedSeconds => elapsed.inSeconds;

  /// Get elapsed minutes
  int get elapsedMinutes => elapsed.inMinutes;

  /// Get elapsed milliseconds
  int get elapsedMilliseconds => elapsed.inMilliseconds;
}

/// Interval timer for repeating actions
class IntervalTimer {
  Timer? _timer;
  final Duration _interval;
  final VoidCallback _onInterval;
  bool _isRunning = false;

  IntervalTimer({
    required Duration interval,
    required VoidCallback onInterval,
  }) : _interval = interval,
       _onInterval = onInterval;

  /// Start the interval timer
  void start() {
    if (_isRunning) return;
    
    _isRunning = true;
    _timer = Timer.periodic(_interval, (_) => _onInterval());
  }

  /// Stop the interval timer
  void stop() {
    _isRunning = false;
    _timer?.cancel();
  }

  /// Check if timer is running
  bool get isRunning => _isRunning;

  /// Dispose the timer
  void dispose() {
    _timer?.cancel();
  }
}

/// Delay timer for one-time delayed actions
class DelayTimer {
  Timer? _timer;

  /// Execute callback after delay
  void delay(Duration duration, VoidCallback callback) {
    cancel();
    _timer = Timer(duration, callback);
  }

  /// Cancel the delayed action
  void cancel() {
    _timer?.cancel();
  }

  /// Check if timer is active
  bool get isActive => _timer?.isActive ?? false;

  /// Dispose the timer
  void dispose() {
    _timer?.cancel();
  }
}

/// Utility class for managing multiple timers
class TimerManager {
  final Map<String, GameTimer> _timers = {};
  final Map<String, IntervalTimer> _intervalTimers = {};
  final Map<String, DelayTimer> _delayTimers = {};

  /// Add a game timer
  void addTimer(String name, GameTimer timer) {
    _timers[name] = timer;
  }

  /// Add an interval timer
  void addIntervalTimer(String name, IntervalTimer timer) {
    _intervalTimers[name] = timer;
  }

  /// Add a delay timer
  void addDelayTimer(String name, DelayTimer timer) {
    _delayTimers[name] = timer;
  }

  /// Get a timer by name
  GameTimer? getTimer(String name) => _timers[name];

  /// Get an interval timer by name
  IntervalTimer? getIntervalTimer(String name) => _intervalTimers[name];

  /// Get a delay timer by name
  DelayTimer? getDelayTimer(String name) => _delayTimers[name];

  /// Start all timers
  void startAll() {
    for (final timer in _timers.values) {
      timer.start();
    }
    for (final timer in _intervalTimers.values) {
      timer.start();
    }
  }

  /// Pause all timers
  void pauseAll() {
    for (final timer in _timers.values) {
      timer.pause();
    }
    for (final timer in _intervalTimers.values) {
      timer.stop();
    }
  }

  /// Resume all timers
  void resumeAll() {
    for (final timer in _timers.values) {
      timer.resume();
    }
    for (final timer in _intervalTimers.values) {
      timer.start();
    }
  }

  /// Stop all timers
  void stopAll() {
    for (final timer in _timers.values) {
      timer.stop();
    }
    for (final timer in _intervalTimers.values) {
      timer.stop();
    }
    for (final timer in _delayTimers.values) {
      timer.cancel();
    }
  }

  /// Dispose all timers
  void dispose() {
    for (final timer in _timers.values) {
      timer.dispose();
    }
    for (final timer in _intervalTimers.values) {
      timer.dispose();
    }
    for (final timer in _delayTimers.values) {
      timer.dispose();
    }
    
    _timers.clear();
    _intervalTimers.clear();
    _delayTimers.clear();
  }
}
