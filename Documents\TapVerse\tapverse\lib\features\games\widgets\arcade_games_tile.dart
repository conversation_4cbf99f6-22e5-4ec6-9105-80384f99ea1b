import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../../../core/providers/app_providers.dart';
import 'games_popup_dialog.dart';

class ArcadeGamesTile extends ConsumerWidget {
  const ArcadeGamesTile({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final gameNavigation = ref.watch(gameNavigationServiceProvider);
    final soundService = ref.watch(soundServiceProvider);
    final vibrationService = ref.watch(vibrationServiceProvider);
    
    final allGames = gameNavigation.allGames;
    final gameCount = allGames.length;

    return Card(
      elevation: 4,
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20),
      ),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(20),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Colors.blue.withValues(alpha: 0.1),
              Colors.purple.withValues(alpha: 0.1),
            ],
          ),
          border: Border.all(
            color: Colors.blue.withValues(alpha: 0.3),
            width: 1,
          ),
        ),
        child: InkWell(
          onTap: () async {
            await soundService.playButtonClick();
            await vibrationService.onButtonTap();
            if (context.mounted) {
              _showGamesModal(context);
            }
          },
          borderRadius: BorderRadius.circular(20),
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Row(
              children: [
                // Icon section
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.blue.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.blue.withValues(alpha: 0.3),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Icon(
                    Icons.games,
                    size: 32,
                    color: Colors.blue.shade300,
                  ),
                ),
                
                const SizedBox(width: 20),
                
                // Content section
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Arcade Games',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '$gameCount Games Available',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Colors.white.withValues(alpha: 0.7),
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Tap to browse all games',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Colors.blue.shade300,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
                
                // Arrow icon
                Icon(
                  Icons.arrow_forward_ios,
                  color: Colors.blue.shade300,
                  size: 20,
                ),
              ],
            ),
          ),
        ),
      ),
    ).animate()
     .fadeIn(duration: 600.ms, delay: 100.ms)
     .slideX(begin: -0.2, end: 0);
  }

  void _showGamesModal(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => const GamesPopupDialog(),
    );
  }
}
