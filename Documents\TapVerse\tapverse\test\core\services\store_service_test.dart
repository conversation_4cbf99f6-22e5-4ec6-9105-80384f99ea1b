import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:tapverse/core/services/store_service.dart';
import 'package:tapverse/core/services/firestore_service.dart';
import 'package:tapverse/core/services/auth_service.dart';
import 'package:tapverse/core/services/token_service.dart';
import 'package:tapverse/core/models/store_item.dart';
import 'package:firebase_auth/firebase_auth.dart';

// import 'store_service_test.mocks.dart';

@GenerateMocks([FirestoreService, AuthService, TokenService, User])
void main() {
  group('StoreService', () {
    late StoreService storeService;
    late MockFirestoreService mockFirestore;
    late MockAuthService mockAuth;
    late MockTokenService mockTokenService;
    late MockUser mockUser;

    setUp(() {
      mockFirestore = MockFirestoreService();
      mockAuth = MockAuthService();
      mockTokenService = MockTokenService();
      mockUser = MockUser();
      
      storeService = StoreService(mockFirestore, mockAuth, mockTokenService);
    });

    group('purchaseItem', () {
      const itemId = 'test_item_1';
      const userId = 'test_user_123';
      const itemPrice = 100;

      final testItem = StoreItem(
        id: itemId,
        name: 'Test Item',
        type: StoreItemType.skin,
        price: itemPrice,
        assetPath: 'test/path',
        description: 'Test description',
        isAvailable: true,
      );

      setUp(() {
        when(mockAuth.currentUser).thenReturn(mockUser);
        when(mockUser.uid).thenReturn(userId);
      });

      test('should successfully purchase item when user has enough tokens', () async {
        // Arrange
        when(mockFirestore.hasItem(userId, itemId)).thenAnswer((_) async => false);
        when(mockFirestore.getStoreItem(itemId)).thenAnswer((_) async => testItem);
        when(mockTokenService.canAfford(userId, itemPrice)).thenAnswer((_) async => true);
        when(mockFirestore.purchaseItem(userId, itemId, itemPrice)).thenAnswer((_) async {
          return null;
        });

        // Act
        final result = await storeService.purchaseItem(itemId);

        // Assert
        expect(result.success, isTrue);
        expect(result.message, contains('Successfully purchased'));
        verify(mockFirestore.purchaseItem(userId, itemId, itemPrice)).called(1);
      });

      test('should fail when user is not authenticated', () async {
        // Arrange
        when(mockAuth.currentUser).thenReturn(null);

        // Act
        final result = await storeService.purchaseItem(itemId);

        // Assert
        expect(result.success, isFalse);
        expect(result.message, contains('User not authenticated'));
        verifyNever(mockFirestore.purchaseItem(any, any, any));
      });

      test('should fail when user already owns the item', () async {
        // Arrange
        when(mockFirestore.hasItem(userId, itemId)).thenAnswer((_) async => true);

        // Act
        final result = await storeService.purchaseItem(itemId);

        // Assert
        expect(result.success, isFalse);
        expect(result.message, contains('already own this item'));
        verifyNever(mockFirestore.purchaseItem(any, any, any));
      });

      test('should fail when item is not found', () async {
        // Arrange
        when(mockFirestore.hasItem(userId, itemId)).thenAnswer((_) async => false);
        when(mockFirestore.getStoreItem(itemId)).thenAnswer((_) async => null);

        // Act
        final result = await storeService.purchaseItem(itemId);

        // Assert
        expect(result.success, isFalse);
        expect(result.message, contains('Item not found'));
        verifyNever(mockFirestore.purchaseItem(any, any, any));
      });

      test('should fail when item is not available', () async {
        // Arrange
        final unavailableItem = testItem.copyWith(isAvailable: false);
        when(mockFirestore.hasItem(userId, itemId)).thenAnswer((_) async => false);
        when(mockFirestore.getStoreItem(itemId)).thenAnswer((_) async => unavailableItem);

        // Act
        final result = await storeService.purchaseItem(itemId);

        // Assert
        expect(result.success, isFalse);
        expect(result.message, contains('not available for purchase'));
        verifyNever(mockFirestore.purchaseItem(any, any, any));
      });

      test('should fail when user has insufficient tokens', () async {
        // Arrange
        when(mockFirestore.hasItem(userId, itemId)).thenAnswer((_) async => false);
        when(mockFirestore.getStoreItem(itemId)).thenAnswer((_) async => testItem);
        when(mockTokenService.canAfford(userId, itemPrice)).thenAnswer((_) async => false);
        when(mockTokenService.getUserTokens(userId)).thenAnswer((_) async => 50);

        // Act
        final result = await storeService.purchaseItem(itemId);

        // Assert
        expect(result.success, isFalse);
        expect(result.message, contains('Insufficient tokens'));
        verifyNever(mockFirestore.purchaseItem(any, any, any));
      });

      test('should handle exceptions gracefully', () async {
        // Arrange
        when(mockFirestore.hasItem(userId, itemId)).thenThrow(Exception('Network error'));

        // Act
        final result = await storeService.purchaseItem(itemId);

        // Assert
        expect(result.success, isFalse);
        expect(result.message, contains('Purchase failed'));
      });
    });

    group('userOwnsItem', () {
      const itemId = 'test_item_1';
      const userId = 'test_user_123';

      setUp(() {
        when(mockAuth.currentUser).thenReturn(mockUser);
        when(mockUser.uid).thenReturn(userId);
      });

      test('should return true when user owns the item', () async {
        // Arrange
        when(mockFirestore.hasItem(userId, itemId)).thenAnswer((_) async => true);

        // Act
        final result = await storeService.userOwnsItem(itemId);

        // Assert
        expect(result, isTrue);
      });

      test('should return false when user does not own the item', () async {
        // Arrange
        when(mockFirestore.hasItem(userId, itemId)).thenAnswer((_) async => false);

        // Act
        final result = await storeService.userOwnsItem(itemId);

        // Assert
        expect(result, isFalse);
      });

      test('should return false when user is not authenticated', () async {
        // Arrange
        when(mockAuth.currentUser).thenReturn(null);

        // Act
        final result = await storeService.userOwnsItem(itemId);

        // Assert
        expect(result, isFalse);
        verifyNever(mockFirestore.hasItem(any, any));
      });
    });

    group('getStoreItemsByType', () {
      test('should filter items by type correctly', () async {
        // Arrange
        final allItems = [
          StoreItem(
            id: '1',
            name: 'Skin 1',
            type: StoreItemType.skin,
            price: 100,
            assetPath: 'path1',
          ),
          StoreItem(
            id: '2',
            name: 'Effect 1',
            type: StoreItemType.effect,
            price: 200,
            assetPath: 'path2',
          ),
          StoreItem(
            id: '3',
            name: 'Skin 2',
            type: StoreItemType.skin,
            price: 150,
            assetPath: 'path3',
          ),
        ];

        when(mockFirestore.getStoreItems()).thenAnswer((_) => Stream.value(allItems));

        // Act
        final skinItems = await storeService.getStoreItemsByType(StoreItemType.skin).first;

        // Assert
        expect(skinItems.length, equals(2));
        expect(skinItems.every((item) => item.type == StoreItemType.skin), isTrue);
      });
    });

    group('searchItems', () {
      test('should filter items by search query', () async {
        // Arrange
        final allItems = [
          StoreItem(
            id: '1',
            name: 'Golden Ball',
            type: StoreItemType.skin,
            price: 100,
            assetPath: 'path1',
            description: 'A shiny golden ball',
          ),
          StoreItem(
            id: '2',
            name: 'Silver Effect',
            type: StoreItemType.effect,
            price: 200,
            assetPath: 'path2',
            description: 'Sparkly silver effect',
          ),
          StoreItem(
            id: '3',
            name: 'Blue Theme',
            type: StoreItemType.theme,
            price: 150,
            assetPath: 'path3',
            description: 'Ocean blue theme',
          ),
        ];

        when(mockFirestore.getStoreItems()).thenAnswer((_) => Stream.value(allItems));

        // Act
        final searchResults = await storeService.searchItems('golden').first;

        // Assert
        expect(searchResults.length, equals(1));
        expect(searchResults.first.name, equals('Golden Ball'));
      });

      test('should return all items when search query is empty', () async {
        // Arrange
        final allItems = [
          StoreItem(id: '1', name: 'Item 1', type: StoreItemType.skin, price: 100, assetPath: 'path1'),
          StoreItem(id: '2', name: 'Item 2', type: StoreItemType.effect, price: 200, assetPath: 'path2'),
        ];

        when(mockFirestore.getStoreItems()).thenAnswer((_) => Stream.value(allItems));

        // Act
        final searchResults = await storeService.searchItems('').first;

        // Assert
        expect(searchResults.length, equals(2));
      });
    });
  });
}
