import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

// Import all game widgets
import '../swish_shot/swish_shot_game.dart';
import '../tappy_footy/tappy_footy_game.dart';
import '../rebounder/rebounder_game.dart';
import '../spike_loop/spike_loop_game.dart';
import '../flapster/flapster_game.dart';
import '../smash_wall/smash_wall_game.dart';
import '../astro_zap/astro_zap_game.dart';
import '../wriggle/wriggle_game.dart';
import '../hue_drop/hue_drop_game.dart';
import '../dash_rush/dash_rush_game.dart';
import '../beat_tap/beat_tap_game.dart';
import '../boom_sweep/boom_sweep_game.dart';
import '../fruit_snag/fruit_snag_game.dart';
import '../tile_twist/tile_twist_game.dart';
import '../dart_dash/dart_dash_game.dart';
import '../drop_target/drop_target_game.dart';
import '../pop_burst/pop_burst_game.dart';
import '../stackify/stackify_game.dart';

import '../../../core/providers/app_providers.dart';
import '../../../core/providers/game_providers.dart';
import '../../../core/services/game_navigation_service.dart';
import '../../../core/models/game_state.dart';
import '../../../tapverse/integration/tapverse_game_bridge.dart';

class GameScreen extends ConsumerStatefulWidget {
  final String gameId;

  const GameScreen({
    super.key,
    required this.gameId,
  });

  @override
  ConsumerState<GameScreen> createState() => _GameScreenState();
}

class _GameScreenState extends ConsumerState<GameScreen> {
  @override
  Widget build(BuildContext context) {
    final gameNavigationService = ref.read(gameNavigationServiceProvider);
    
    try {
      final gameInfo = gameNavigationService.getGameInfoById(widget.gameId);
      final gameWidget = _buildGameWidget(gameInfo.type);
      
      return Scaffold(
        body: SafeArea(
          child: gameWidget,
        ),
      );
    } catch (e) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('Game Not Found'),
          leading: IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: () => context.pop(),
          ),
        ),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.error_outline,
                size: 64,
                color: Colors.red,
              ),
              const SizedBox(height: 16),
              Text(
                'Game "${widget.gameId}" not found',
                style: Theme.of(context).textTheme.headlineSmall,
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () => context.pop(),
                child: const Text('Go Back'),
              ),
            ],
          ),
        ),
      );
    }
  }

  Widget _buildGameWidget(GameType gameType) {
    // Check if this is a new architecture game
    final gameNavigationService = ref.read(gameNavigationServiceProvider);
    final gameInfo = gameNavigationService.getGameInfo(gameType);

    if (_isNewArchitectureGame(gameType)) {
      return TapVerseGameFactory.createMiniGame(
        gameId: gameInfo?.id ?? 'unknown',
        onGameComplete: _onGameComplete,
        onGameExit: _onGameExit,
      );
    }

    // Legacy games
    switch (gameType) {
      case GameType.swishShot:
        return SwishShotGame(
          onGameComplete: _onGameComplete,
          onGameExit: _onGameExit,
        );
      case GameType.tappyFooty:
        return TappyFootyGame(
          onGameComplete: _onGameComplete,
          onGameExit: _onGameExit,
        );
      case GameType.rebounder:
        return RebounderGame(
          onGameComplete: _onGameComplete,
          onGameExit: _onGameExit,
        );
      case GameType.spikeLoop:
        return SpikeLoopGame(
          onGameComplete: _onGameComplete,
          onGameExit: _onGameExit,
        );
      case GameType.flapster:
        return FlapsterGame(
          onGameComplete: _onGameComplete,
          onGameExit: _onGameExit,
        );
      case GameType.smashWall:
        return SmashWallGame(
          onGameComplete: _onGameComplete,
          onGameExit: _onGameExit,
        );
      case GameType.astroZap:
        return AstroZapGame(
          onGameComplete: _onGameComplete,
          onGameExit: _onGameExit,
        );
      case GameType.wriggle:
        return WriggleGame(
          onGameComplete: _onGameComplete,
          onGameExit: _onGameExit,
        );
      case GameType.hueDrop:
        return HueDropGame(
          onGameComplete: _onGameComplete,
          onGameExit: _onGameExit,
        );
      case GameType.dashRush:
        return DashRushGame(
          onGameComplete: _onGameComplete,
          onGameExit: _onGameExit,
        );
      case GameType.beatTap:
        return BeatTapGame(
          onGameComplete: _onGameComplete,
          onGameExit: _onGameExit,
        );
      case GameType.boomSweep:
        return BoomSweepGame(
          onGameComplete: _onGameComplete,
          onGameExit: _onGameExit,
        );
      case GameType.fruitSnag:
        return FruitSnagGame(
          onGameComplete: _onGameComplete,
          onGameExit: _onGameExit,
        );
      case GameType.tileTwist:
        return TileTwistGame(
          onGameComplete: _onGameComplete,
          onGameExit: _onGameExit,
        );
      case GameType.dartDash:
        return DartDashGame(
          onGameComplete: _onGameComplete,
          onGameExit: _onGameExit,
        );
      case GameType.dropTarget:
        return DropTargetGame(
          onGameComplete: _onGameComplete,
          onGameExit: _onGameExit,
        );
      case GameType.popBurst:
        return PopBurstGame(
          onGameComplete: _onGameComplete,
          onGameExit: _onGameExit,
        );
      case GameType.stackify:
        return StackifyGame(
          onGameComplete: _onGameComplete,
          onGameExit: _onGameExit,
        );
      default:
        throw ArgumentError('Game type $gameType not implemented yet');
    }
  }

  void _onGameComplete() {
    // Game completed successfully - return to home
    _resetGameState();
    if (mounted) {
      context.pop();
    }
  }

  void _onGameExit() {
    // User exited game - return to home
    _resetGameState();
    if (mounted) {
      context.pop();
    }
  }

  void _resetGameState() {
    try {
      final gameNavigationService = ref.read(gameNavigationServiceProvider);
      final gameInfo = gameNavigationService.getGameInfoById(widget.gameId);

      // New architecture games handle their own state management
      if (!_isNewArchitectureGame(gameInfo.type)) {
        final gameConfig = _getGameConfig(gameInfo.type);

        // Reset the game state for legacy games
        final gameNotifier = ref.read(gameStateProvider(gameConfig).notifier);
        gameNotifier.resetGame();
      }
    } catch (e) {
      // Game not found or error resetting - ignore
    }
  }

  GameConfig _getGameConfig(GameType gameType) {
    // New architecture games don't use GameConfig
    if (_isNewArchitectureGame(gameType)) {
      // Return a default config for new games
      return GameConfig(
        gameId: gameType.toString(),
        gameName: gameType.toString(),
        difficulty: GameDifficulty.medium,
        scoreMultiplier: 1,
        hasLives: false,
        hasLevels: false,
        hasTimer: false,
      );
    }

    switch (gameType) {
      case GameType.swishShot:
        return SwishShotGame.gameConfig;
      case GameType.tappyFooty:
        return TappyFootyGame.gameConfig;
      case GameType.rebounder:
        return RebounderGame.gameConfig;
      case GameType.spikeLoop:
        return SpikeLoopGame.gameConfig;
      case GameType.flapster:
        return FlapsterGame.gameConfig;
      case GameType.smashWall:
        return SmashWallGame.gameConfig;
      case GameType.astroZap:
        return AstroZapGame.gameConfig;
      case GameType.wriggle:
        return WriggleGame.gameConfig;
      case GameType.hueDrop:
        return HueDropGame.gameConfig;
      case GameType.dashRush:
        return DashRushGame.gameConfig;
      case GameType.beatTap:
        return BeatTapGame.gameConfig;
      case GameType.boomSweep:
        return BoomSweepGame.gameConfig;
      case GameType.fruitSnag:
        return FruitSnagGame.gameConfig;
      case GameType.tileTwist:
        return TileTwistGame.gameConfig;
      case GameType.dartDash:
        return DartDashGame.gameConfig;
      case GameType.dropTarget:
        return DropTargetGame.gameConfig;
      case GameType.popBurst:
        return PopBurstGame.gameConfig;
      case GameType.stackify:
        return StackifyGame.gameConfig;
      default:
        throw ArgumentError('Game type $gameType not implemented yet');
    }
  }

  bool _isNewArchitectureGame(GameType gameType) {
    // List of games that use the new MiniGameBase architecture
    const newArchitectureGames = {
      GameType.laserJump,
      GameType.coinClimb,
      GameType.flipShot,
      GameType.pixelDive,
      GameType.glowRunner,
      GameType.bubbleBlast,
      GameType.shadowSprint,
      GameType.shapeShift,
      GameType.rocketRider,
      GameType.tugTap,
      GameType.tileTide,
      GameType.zapGrid,
    };

    return newArchitectureGames.contains(gameType);
  }
}
