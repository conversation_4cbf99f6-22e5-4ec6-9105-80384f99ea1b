import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../../core/providers/app_providers.dart';
import '../../core/models/game_state.dart';

class GameOverDialog extends ConsumerStatefulWidget {
  final String gameId;
  final String gameName;
  final int score;
  final int tokensEarned;
  final bool isNewHighScore;
  final int? previousHighScore;
  final VoidCallback? onPlayAgain;
  final VoidCallback? onHome;
  final VoidCallback? onLeaderboard;
  final GameConfig? gameConfig;

  const GameOverDialog({
    super.key,
    required this.gameId,
    required this.gameName,
    required this.score,
    required this.tokensEarned,
    this.isNewHighScore = false,
    this.previousHighScore,
    this.onPlayAgain,
    this.onHome,
    this.onLeaderboard,
    this.gameConfig,
  });

  /// Factory constructor that automatically calculates high scores and tokens
  static Future<GameOverDialog> create({
    required WidgetRef ref,
    required GameConfig config,
    required int score,
    VoidCallback? onPlayAgain,
    VoidCallback? onHome,
    VoidCallback? onLeaderboard,
  }) async {
    final authService = ref.read(authServiceProvider);
    final firestoreService = ref.read(firestoreServiceProvider);
    final tokenService = ref.read(tokenServiceProvider);

    final user = authService.currentUser;
    if (user == null) {
      return GameOverDialog(
        gameId: config.gameId,
        gameName: config.gameName,
        score: score,
        tokensEarned: 0,
        onPlayAgain: onPlayAgain,
        onHome: onHome,
        onLeaderboard: onLeaderboard,
        gameConfig: config,
      );
    }

    // Get user data to check high score
    final userData = await firestoreService.getUser(user.uid);
    final previousHighScore = userData?.getHighScore(config.gameId) ?? 0;
    final isNewHighScore = score > previousHighScore;

    // Calculate token reward
    final tokensEarned = tokenService.calculateTokenReward(score, config.gameId);

    return GameOverDialog(
      gameId: config.gameId,
      gameName: config.gameName,
      score: score,
      tokensEarned: tokensEarned,
      isNewHighScore: isNewHighScore,
      previousHighScore: previousHighScore > 0 ? previousHighScore : null,
      onPlayAgain: onPlayAgain,
      onHome: onHome,
      onLeaderboard: onLeaderboard,
      gameConfig: config,
    );
  }

  @override
  ConsumerState<GameOverDialog> createState() => _GameOverDialogState();
}

class _GameOverDialogState extends ConsumerState<GameOverDialog>
    with TickerProviderStateMixin {
  late AnimationController _controller;
  bool _showDetails = false;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    
    // Start animation and show details after a delay
    _controller.forward();
    Future.delayed(const Duration(milliseconds: 800), () {
      if (mounted) {
        setState(() {
          _showDetails = true;
        });
      }
    });
    
    // Play sound effects
    _playGameOverSounds();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  Future<void> _playGameOverSounds() async {
    final soundService = ref.read(soundServiceProvider);
    final vibrationService = ref.read(vibrationServiceProvider);
    
    if (widget.isNewHighScore) {
      await soundService.playVictory();
      await vibrationService.onScore();
    } else {
      await soundService.playGameOver();
      await vibrationService.onGameOver();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      child: Container(
        constraints: const BoxConstraints(maxWidth: 400),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface,
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.3),
              blurRadius: 20,
              offset: const Offset(0, 10),
            ),
          ],
        ),
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Header with animation
              _buildHeader(),
              
              const SizedBox(height: 20),
              
              // Score display
              _buildScoreSection(),
              
              const SizedBox(height: 20),
              
              // Details section (animated)
              if (_showDetails) _buildDetailsSection(),
              
              const SizedBox(height: 24),
              
              // Action buttons
              _buildActionButtons(),
            ],
          ),
        ),
      ),
    ).animate()
     .scale(
       begin: const Offset(0.8, 0.8),
       end: const Offset(1, 1),
       duration: 500.ms,
       curve: Curves.elasticOut,
     )
     .fadeIn(duration: 300.ms);
  }

  Widget _buildHeader() {
    return Column(
      children: [
        // Icon or Lottie animation
        Container(
          width: 80,
          height: 80,
          decoration: BoxDecoration(
            color: widget.isNewHighScore 
                ? Colors.amber.withOpacity(0.2)
                : Colors.grey.withOpacity(0.2),
            shape: BoxShape.circle,
          ),
          child: Icon(
            widget.isNewHighScore ? Icons.emoji_events : Icons.sports_esports,
            size: 40,
            color: widget.isNewHighScore ? Colors.amber : Colors.grey[600],
          ),
        ),
        
        const SizedBox(height: 16),
        
        // Title
        Text(
          widget.isNewHighScore ? 'New High Score!' : 'Game Over',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
            color: widget.isNewHighScore 
                ? Colors.amber[700]
                : Theme.of(context).colorScheme.onSurface,
          ),
        ),
        
        // Game name
        Text(
          widget.gameName,
          style: Theme.of(context).textTheme.bodyLarge?.copyWith(
            color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
          ),
        ),
      ],
    );
  }

  Widget _buildScoreSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.primaryContainer.withOpacity(0.3),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          Text(
            'Score',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
            ),
          ),
          const SizedBox(height: 4),
          Text(
            widget.score.toString(),
            style: Theme.of(context).textTheme.headlineLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: Theme.of(context).colorScheme.primary,
            ),
          ),
          
          if (widget.isNewHighScore && widget.previousHighScore != null) ...[
            const SizedBox(height: 8),
            Text(
              'Previous best: ${widget.previousHighScore}',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildDetailsSection() {
    return Column(
      children: [
        // Tokens earned
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.amber.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.amber.withOpacity(0.3)),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.monetization_on,
                color: Colors.amber[700],
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                '+${widget.tokensEarned} Tokens Earned',
                style: TextStyle(
                  color: Colors.amber[700],
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ).animate()
         .slideX(begin: -1, end: 0, duration: 600.ms)
         .fadeIn(duration: 600.ms),
        
        const SizedBox(height: 12),
        
        // Achievement message
        if (widget.isNewHighScore)
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.green.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.green.withOpacity(0.3)),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.star,
                  color: Colors.green[600],
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  'Personal Best Achievement!',
                  style: TextStyle(
                    color: Colors.green[600],
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ).animate()
           .slideX(begin: 1, end: 0, duration: 600.ms, delay: 200.ms)
           .fadeIn(duration: 600.ms, delay: 200.ms),
      ],
    );
  }

  Widget _buildActionButtons() {
    return Column(
      children: [
        // Primary actions
        Row(
          children: [
            Expanded(
              child: OutlinedButton.icon(
                onPressed: () {
                  Navigator.of(context).pop();
                  widget.onHome?.call();
                },
                icon: const Icon(Icons.home),
                label: const Text('Home'),
                style: OutlinedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: ElevatedButton.icon(
                onPressed: () {
                  Navigator.of(context).pop();
                  widget.onPlayAgain?.call();
                },
                icon: const Icon(Icons.refresh),
                label: const Text('Play Again'),
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
              ),
            ),
          ],
        ),
        
        const SizedBox(height: 8),
        
        // Secondary action
        TextButton.icon(
          onPressed: () {
            Navigator.of(context).pop();
            widget.onLeaderboard?.call();
          },
          icon: const Icon(Icons.leaderboard),
          label: const Text('View Leaderboard'),
        ),
      ],
    );
  }
}
