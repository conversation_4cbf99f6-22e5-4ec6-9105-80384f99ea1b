import 'dart:math';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/models/game_state.dart';
import '../../../core/providers/game_providers.dart';
import '../../../core/services/game_feedback_service.dart';
import '../../../shared/widgets/base_game_widget.dart';

class SpikeLoopGame extends BaseGameWidget {
  static const GameConfig gameConfig = GameConfig(
    gameId: 'spike_loop',
    gameName: 'SpikeLoop',
    scoreMultiplier: 1,
    hasLives: false,
    hasLevels: false,
    hasTimer: false,
  );

  const SpikeLoopGame({
    super.key,
    super.onGameComplete,
    super.onGameExit,
  }) : super(config: gameConfig);

  @override
  Widget buildGameContent(BuildContext context, WidgetRef ref, GameState gameState) {
    return const _SpikeLoopGameContent();
  }
}

class _SpikeLoopGameContent extends ConsumerStatefulWidget {
  const _SpikeLoopGameContent();

  @override
  ConsumerState<_SpikeLoopGameContent> createState() => _SpikeLoopGameContentState();
}

class _SpikeLoopGameContentState extends ConsumerState<_SpikeLoopGameContent>
    with TickerProviderStateMixin {
  
  // Game state
  late AnimationController _ballController;
  late AnimationController _spikeController;
  
  // Ball physics
  Offset _ballPosition = const Offset(200, 300);
  Offset _ballVelocity = const Offset(0, 200);
  final double _ballSize = 35;
  
  // Game mechanics
  final double _gravity = 800;
  final double _bounceForce = -350;
  final double _groundY = 520;
  bool _showSpikeEffect = false;
  int _consecutiveBounces = 0;
  
  // Constants
  static const double _courtWidth = 400;
  static const double _courtHeight = 600;

  @override
  void initState() {
    super.initState();

    _ballController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );

    _spikeController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _resetGame();
    _startPhysicsLoop();

    // Listen for game state changes to reset when needed
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.listen(gameStateProvider(SpikeLoopGame.gameConfig), (previous, next) {
        if (previous?.status != next.status && next.status == GameStatus.notStarted) {
          _resetGame();
        }
      });
    });
  }

  @override
  void dispose() {
    _ballController.dispose();
    _spikeController.dispose();
    super.dispose();
  }

  void _resetGame() {
    setState(() {
      _ballPosition = const Offset(200, 300);
      _ballVelocity = const Offset(0, 200);
      _showSpikeEffect = false;
      _consecutiveBounces = 0;
    });
  }

  void _startPhysicsLoop() {
    const frameRate = 60;
    const frameDuration = Duration(milliseconds: 1000 ~/ frameRate);
    
    void updatePhysics() {
      if (!mounted) return;
      
      final gameState = ref.read(gameStateProvider(SpikeLoopGame.gameConfig));
      if (!gameState.isPlaying) {
        Future.delayed(frameDuration, updatePhysics);
        return;
      }
      
      setState(() {
        // Apply gravity
        _ballVelocity = Offset(
          _ballVelocity.dx,
          _ballVelocity.dy + _gravity / frameRate,
        );
        
        // Update ball position
        _ballPosition = Offset(
          _ballPosition.dx + _ballVelocity.dx / frameRate,
          _ballPosition.dy + _ballVelocity.dy / frameRate,
        );
        
        // Check side boundaries
        if (_ballPosition.dx <= _ballSize / 2) {
          _ballPosition = Offset(_ballSize / 2, _ballPosition.dy);
          _ballVelocity = Offset(-_ballVelocity.dx * 0.8, _ballVelocity.dy);
        } else if (_ballPosition.dx >= _courtWidth - _ballSize / 2) {
          _ballPosition = Offset(_courtWidth - _ballSize / 2, _ballPosition.dy);
          _ballVelocity = Offset(-_ballVelocity.dx * 0.8, _ballVelocity.dy);
        }
        
        // Check ground collision (game over)
        if (_ballPosition.dy >= _groundY - _ballSize / 2) {
          final gameNotifier = ref.read(gameStateProvider(SpikeLoopGame.gameConfig).notifier);
          gameNotifier.endGame(reason: 'Ball hit the ground');
        }
      });
      
      Future.delayed(frameDuration, updatePhysics);
    }
    
    updatePhysics();
  }

  void _onTap(TapUpDetails details) {
    final gameState = ref.read(gameStateProvider(SpikeLoopGame.gameConfig));
    if (!gameState.isPlaying) return;
    
    final tapPosition = details.localPosition;
    final ballCenter = _ballPosition;
    final distance = sqrt(
      pow(tapPosition.dx - ballCenter.dx, 2) + 
      pow(tapPosition.dy - ballCenter.dy, 2)
    );
    
    // Check if tap is near the ball (within reasonable distance)
    if (distance <= _ballSize * 2 && _ballVelocity.dy > 0) {
      setState(() {
        // Calculate spike direction based on tap position relative to ball
        final direction = tapPosition - ballCenter;
        final normalizedDirection = direction / distance;
        
        // Apply upward force with some horizontal component
        _ballVelocity = Offset(
          normalizedDirection.dx * 100 + _ballVelocity.dx * 0.3,
          _bounceForce,
        );
        
        _consecutiveBounces++;
        _showSpikeEffect = true;
      });
      
      // Add score
      final gameNotifier = ref.read(gameStateProvider(SpikeLoopGame.gameConfig).notifier);
      gameNotifier.addScore(1);
      
      // Trigger feedback with combo intensity
      if (_consecutiveBounces > 1) {
        ref.read(gameFeedbackServiceProvider).triggerComboFeedback(_consecutiveBounces);
      } else {
        ref.read(gameFeedbackServiceProvider).triggerFeedback(GameEvent.scoreIncrease);
      }
      
      // Play animations
      _ballController.forward().then((_) => _ballController.reset());
      _spikeController.forward().then((_) {
        _spikeController.reset();
        setState(() {
          _showSpikeEffect = false;
        });
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final gameState = ref.watch(gameStateProvider(SpikeLoopGame.gameConfig));
    
    return Container(
      width: _courtWidth,
      height: _courtHeight,
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [Color(0xFF87CEEB), Color(0xFFFFE4B5)],
        ),
      ),
      child: GestureDetector(
        onTapUp: _onTap,
        child: Stack(
          children: [
            // Court
            _buildCourt(),
            
            // Volleyball
            _buildBall(),
            
            // Spike effect
            if (_showSpikeEffect) _buildSpikeEffect(),
            
            // Game info
            _buildGameInfo(gameState),
            
            // Instructions
            if (!gameState.isPlaying) _buildInstructions(),
          ],
        ),
      ),
    );
  }

  Widget _buildCourt() {
    return Positioned.fill(
      child: CustomPaint(
        painter: _CourtPainter(),
      ),
    );
  }

  Widget _buildBall() {
    return Positioned(
      left: _ballPosition.dx - _ballSize / 2,
      top: _ballPosition.dy - _ballSize / 2,
      child: ScaleTransition(
        scale: Tween<double>(begin: 1.0, end: 1.2).animate(_ballController),
        child: Container(
          width: _ballSize,
          height: _ballSize,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            gradient: RadialGradient(
              colors: [Colors.white, Colors.blue[300]!],
              stops: const [0.3, 1.0],
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.3),
                blurRadius: 4,
                offset: const Offset(2, 2),
              ),
            ],
          ),
          child: CustomPaint(
            painter: _VolleyballPainter(),
          ),
        ),
      ),
    );
  }

  Widget _buildSpikeEffect() {
    return Positioned(
      left: _ballPosition.dx - 40,
      top: _ballPosition.dy - 20,
      child: ScaleTransition(
        scale: _spikeController,
        child: Container(
          width: 80,
          height: 40,
          decoration: BoxDecoration(
            color: Colors.yellow.withOpacity(0.8),
            borderRadius: BorderRadius.circular(20),
          ),
          child: const Center(
            child: Text(
              'SPIKE!',
              style: TextStyle(
                color: Colors.red,
                fontSize: 14,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildGameInfo(GameState gameState) {
    return Positioned(
      top: 40,
      left: 20,
      right: 20,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.black54,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              children: [
                const Text(
                  'Bounces',
                  style: TextStyle(color: Colors.white, fontSize: 12),
                ),
                Text(
                  '${gameState.score}',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
          if (_consecutiveBounces > 1)
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.orange.withOpacity(0.8),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                children: [
                  const Text(
                    'Combo',
                    style: TextStyle(color: Colors.white, fontSize: 12),
                  ),
                  Text(
                    '${_consecutiveBounces}x',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildInstructions() {
    return Positioned(
      bottom: 100,
      left: 0,
      right: 0,
      child: Center(
        child: Container(
          padding: EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.black54,
            borderRadius: BorderRadius.all(Radius.circular(12)),
          ),
          child: Text(
            'Tap near the volleyball to spike it up!\nKeep it bouncing and build combos.\nDon\'t let it hit the ground!',
            textAlign: TextAlign.center,
            style: TextStyle(
              color: Colors.white,
              fontSize: 16,
            ),
          ),
        ),
      ),
    );
  }
}

class _CourtPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.white
      ..strokeWidth = 3
      ..style = PaintingStyle.stroke;
    
    // Draw court boundaries
    canvas.drawRect(
      Rect.fromLTWH(10, 10, size.width - 20, size.height - 110),
      paint,
    );
    
    // Draw net (center line)
    final netPaint = Paint()
      ..color = Colors.brown
      ..strokeWidth = 4;
    
    canvas.drawLine(
      Offset(size.width / 2, 10),
      Offset(size.width / 2, size.height - 110),
      netPaint,
    );
    
    // Draw ground
    final groundPaint = Paint()
      ..color = Colors.brown[600]!
      ..style = PaintingStyle.fill;
    
    canvas.drawRect(
      Rect.fromLTWH(0, size.height - 100, size.width, 100),
      groundPaint,
    );
    
    // Draw sand texture
    final sandPaint = Paint()
      ..color = Colors.yellow[200]!
      ..style = PaintingStyle.fill;
    
    canvas.drawRect(
      Rect.fromLTWH(0, size.height - 80, size.width, 80),
      sandPaint,
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class _VolleyballPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.blue[600]!
      ..strokeWidth = 2
      ..style = PaintingStyle.stroke;
    
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width / 2;
    
    // Draw volleyball lines
    // Vertical line
    canvas.drawLine(
      Offset(center.dx, 0),
      Offset(center.dx, size.height),
      paint,
    );
    
    // Horizontal line
    canvas.drawLine(
      Offset(0, center.dy),
      Offset(size.width, center.dy),
      paint,
    );
    
    // Curved lines
    canvas.drawArc(
      Rect.fromCircle(center: center, radius: radius * 0.7),
      0,
      pi * 2,
      false,
      paint,
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
