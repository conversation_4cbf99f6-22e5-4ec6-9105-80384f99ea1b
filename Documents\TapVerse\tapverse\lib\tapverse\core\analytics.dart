/// Analytics system for tracking game events and player behavior
/// Provides a simple interface for logging events that can be integrated with various analytics services
library;

import 'package:flame/components.dart';

typedef AnalyticsSink = void Function(String event, Map<String, dynamic> data);

/// Main analytics class for logging game events
class Analytics {
  static AnalyticsSink _sink = _defaultSink;
  
  /// Set the analytics sink to integrate with your analytics service
  static void setSink(AnalyticsSink sink) {
    _sink = sink;
  }
  
  /// Log an analytics event
  static void log(String event, [Map<String, dynamic> data = const {}]) {
    _sink(event, data);
  }
  
  /// Default sink that prints to console (for development)
  static void _defaultSink(String event, Map<String, dynamic> data) {
    print('Analytics: $event - $data');
  }
}

/// Predefined analytics events for consistency across games
class AnalyticsEvents {
  // Game lifecycle events
  static const String gameStart = 'game_start';
  static const String gameEnd = 'game_end';
  static const String gamePause = 'game_pause';
  static const String gameResume = 'game_resume';
  static const String gameRestart = 'game_restart';
  
  // Player actions
  static const String playerTap = 'player_tap';
  static const String playerSwipe = 'player_swipe';
  static const String playerHold = 'player_hold';
  static const String playerDrag = 'player_drag';
  
  // Game events
  static const String scoreIncrease = 'score_increase';
  static const String levelUp = 'level_up';
  static const String lifeLost = 'life_lost';
  static const String powerUpCollected = 'powerup_collected';
  static const String powerUpUsed = 'powerup_used';
  static const String comboAchieved = 'combo_achieved';
  static const String highScore = 'high_score';
  
  // Game-specific events
  static const String ballLaunched = 'ball_launched';
  static const String targetHit = 'target_hit';
  static const String targetMissed = 'target_missed';
  static const String obstacleHit = 'obstacle_hit';
  static const String collectibleGathered = 'collectible_gathered';
  static const String enemyDefeated = 'enemy_defeated';
  static const String brickDestroyed = 'brick_destroyed';
  static const String perfectTiming = 'perfect_timing';
  
  // Performance events
  static const String frameDropDetected = 'frame_drop_detected';
  static const String memoryWarning = 'memory_warning';
  static const String loadTimeExceeded = 'load_time_exceeded';
}

/// Helper class for creating consistent analytics data
class AnalyticsData {
  /// Create data for game start event
  static Map<String, dynamic> gameStart({
    required String gameMode,
    required int sessionSeed,
    String? difficulty,
    Map<String, dynamic>? customData,
  }) {
    return {
      'mode': gameMode,
      'session_seed': sessionSeed,
      'difficulty': difficulty,
      'timestamp': DateTime.now().millisecondsSinceEpoch,
      ...?customData,
    };
  }
  
  /// Create data for game end event
  static Map<String, dynamic> gameEnd({
    required String gameMode,
    required bool success,
    required int score,
    required int durationMs,
    int? level,
    int? livesRemaining,
    String? endReason,
    Map<String, dynamic>? customData,
  }) {
    return {
      'mode': gameMode,
      'success': success,
      'score': score,
      'duration_ms': durationMs,
      'level': level,
      'lives_remaining': livesRemaining,
      'end_reason': endReason,
      'timestamp': DateTime.now().millisecondsSinceEpoch,
      ...?customData,
    };
  }
  
  /// Create data for player action event
  static Map<String, dynamic> playerAction({
    required String action,
    required Vector2 position,
    String? target,
    bool? successful,
    Map<String, dynamic>? customData,
  }) {
    return {
      'action': action,
      'position_x': position.x,
      'position_y': position.y,
      'target': target,
      'successful': successful,
      'timestamp': DateTime.now().millisecondsSinceEpoch,
      ...?customData,
    };
  }
  
  /// Create data for score event
  static Map<String, dynamic> scoreEvent({
    required int oldScore,
    required int newScore,
    required int pointsAdded,
    String? source,
    int? multiplier,
    Map<String, dynamic>? customData,
  }) {
    return {
      'old_score': oldScore,
      'new_score': newScore,
      'points_added': pointsAdded,
      'source': source,
      'multiplier': multiplier,
      'timestamp': DateTime.now().millisecondsSinceEpoch,
      ...?customData,
    };
  }
  
  /// Create data for performance event
  static Map<String, dynamic> performance({
    required String metric,
    required double value,
    String? unit,
    Map<String, dynamic>? customData,
  }) {
    return {
      'metric': metric,
      'value': value,
      'unit': unit,
      'timestamp': DateTime.now().millisecondsSinceEpoch,
      ...?customData,
    };
  }
}

/// Analytics tracker for specific game metrics
class GameAnalytics {
  final String gameMode;
  final int sessionSeed;
  DateTime? _gameStartTime;
  int _currentScore = 0;
  int _currentLevel = 1;
  int _actionCount = 0;
  
  GameAnalytics({required this.gameMode, required this.sessionSeed});
  
  /// Track game start
  void trackGameStart({String? difficulty, Map<String, dynamic>? customData}) {
    _gameStartTime = DateTime.now();
    Analytics.log(
      AnalyticsEvents.gameStart,
      AnalyticsData.gameStart(
        gameMode: gameMode,
        sessionSeed: sessionSeed,
        difficulty: difficulty,
        customData: customData,
      ),
    );
  }
  
  /// Track game end
  void trackGameEnd({
    required bool success,
    required int finalScore,
    int? livesRemaining,
    String? endReason,
    Map<String, dynamic>? customData,
  }) {
    final duration = _gameStartTime != null 
        ? DateTime.now().difference(_gameStartTime!).inMilliseconds
        : 0;
    
    Analytics.log(
      AnalyticsEvents.gameEnd,
      AnalyticsData.gameEnd(
        gameMode: gameMode,
        success: success,
        score: finalScore,
        durationMs: duration,
        level: _currentLevel,
        livesRemaining: livesRemaining,
        endReason: endReason,
        customData: customData,
      ),
    );
  }
  
  /// Track score change
  void trackScoreChange(int newScore, {String? source, int? multiplier}) {
    final pointsAdded = newScore - _currentScore;
    
    Analytics.log(
      AnalyticsEvents.scoreIncrease,
      AnalyticsData.scoreEvent(
        oldScore: _currentScore,
        newScore: newScore,
        pointsAdded: pointsAdded,
        source: source,
        multiplier: multiplier,
      ),
    );
    
    _currentScore = newScore;
  }
  
  /// Track level change
  void trackLevelUp(int newLevel) {
    Analytics.log(AnalyticsEvents.levelUp, {
      'old_level': _currentLevel,
      'new_level': newLevel,
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    });
    
    _currentLevel = newLevel;
  }
  
  /// Track player action
  void trackPlayerAction({
    required String action,
    required Vector2 position,
    String? target,
    bool? successful,
    Map<String, dynamic>? customData,
  }) {
    _actionCount++;
    
    Analytics.log(
      'player_$action',
      AnalyticsData.playerAction(
        action: action,
        position: position,
        target: target,
        successful: successful,
        customData: {
          'action_count': _actionCount,
          ...?customData,
        },
      ),
    );
  }
  
  /// Track custom game event
  void trackCustomEvent(String eventName, Map<String, dynamic> data) {
    Analytics.log(eventName, {
      'game_mode': gameMode,
      'session_seed': sessionSeed,
      'timestamp': DateTime.now().millisecondsSinceEpoch,
      ...data,
    });
  }
}


