import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/models/store_item.dart';
import '../../../core/providers/app_providers.dart';
import '../../../core/services/store_service.dart';

class PurchaseDialog extends ConsumerStatefulWidget {
  final StoreItem item;

  const PurchaseDialog({
    super.key,
    required this.item,
  });

  @override
  ConsumerState<PurchaseDialog> createState() => _PurchaseDialogState();
}

class _PurchaseDialogState extends ConsumerState<PurchaseDialog> {
  bool _isPurchasing = false;
  String? _errorMessage;

  @override
  Widget build(BuildContext context) {
    final userTokens = ref.watch(userTokensProvider);

    return AlertDialog(
      title: Row(
        children: [
          Icon(
            _getItemIcon(),
            color: _getItemColor(),
          ),
          const SizedBox(width: 8),
          const Text('Confirm Purchase'),
        ],
      ),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Item preview
          Container(
            width: double.infinity,
            height: 120,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  _getItemColor().withOpacity(0.3),
                  _getItemColor().withOpacity(0.1),
                ],
              ),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Center(
              child: Icon(
                _getItemIcon(),
                size: 48,
                color: _getItemColor(),
              ),
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Item details
          Text(
            widget.item.name,
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          
          if (widget.item.description != null) ...[
            const SizedBox(height: 8),
            Text(
              widget.item.description!,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[600],
              ),
            ),
          ],
          
          const SizedBox(height: 16),

          // Item-specific details
          if (_getItemDetails().isNotEmpty) ...[
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: _getItemColor().withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: _getItemColor().withOpacity(0.3),
                  width: 1,
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Details',
                    style: Theme.of(context).textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  ..._getItemDetails().map((detail) => Padding(
                    padding: const EdgeInsets.only(bottom: 4),
                    child: Row(
                      children: [
                        Icon(
                          Icons.check_circle,
                          size: 16,
                          color: _getItemColor(),
                        ),
                        const SizedBox(width: 8),
                        Expanded(child: Text(detail)),
                      ],
                    ),
                  )),
                ],
              ),
            ),
            const SizedBox(height: 16),
          ],

          // Price and balance info
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surfaceContainerHighest,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text('Price:'),
                    Row(
                      children: [
                        Icon(
                          widget.item.isFree ? Icons.free_breakfast : Icons.monetization_on,
                          size: 16,
                          color: widget.item.isFree ? Colors.green : Colors.orange,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          widget.item.isFree ? 'FREE' : '${widget.item.price} tokens',
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                      ],
                    ),
                  ],
                ),
                
                if (!widget.item.isFree) ...[
                  const SizedBox(height: 8),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text('Your balance:'),
                      userTokens.when(
                        data: (tokens) {
                          final canAfford = tokens >= widget.item.price;
                          return Row(
                            children: [
                              Icon(
                                Icons.monetization_on,
                                size: 16,
                                color: canAfford ? Colors.green : Colors.red,
                              ),
                              const SizedBox(width: 4),
                              Text(
                                '$tokens tokens',
                                style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  color: canAfford ? Colors.green : Colors.red,
                                ),
                              ),
                            ],
                          );
                        },
                        loading: () => const CircularProgressIndicator(),
                        error: (_, __) => const Text('Error loading balance'),
                      ),
                    ],
                  ),
                  
                  const SizedBox(height: 8),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text('After purchase:'),
                      userTokens.when(
                        data: (tokens) {
                          final remaining = tokens - widget.item.price;
                          return Row(
                            children: [
                              Icon(
                                Icons.monetization_on,
                                size: 16,
                                color: remaining >= 0 ? Colors.blue : Colors.red,
                              ),
                              const SizedBox(width: 4),
                              Text(
                                '$remaining tokens',
                                style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  color: remaining >= 0 ? Colors.blue : Colors.red,
                                ),
                              ),
                            ],
                          );
                        },
                        loading: () => const CircularProgressIndicator(),
                        error: (_, __) => const Text('Error'),
                      ),
                    ],
                  ),
                ],
              ],
            ),
          ),
          
          // Error message
          if (_errorMessage != null) ...[
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.red.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.red.withOpacity(0.3)),
              ),
              child: Row(
                children: [
                  const Icon(Icons.error_outline, color: Colors.red, size: 16),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      _errorMessage!,
                      style: const TextStyle(color: Colors.red, fontSize: 12),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
      actions: [
        TextButton(
          onPressed: _isPurchasing ? null : () => Navigator.of(context).pop(false),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: _isPurchasing ? null : _handlePurchase,
          child: _isPurchasing
              ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : Text(widget.item.isFree ? 'Get Free' : 'Purchase'),
        ),
      ],
    );
  }

  Future<void> _handlePurchase() async {
    setState(() {
      _isPurchasing = true;
      _errorMessage = null;
    });

    try {
      final storeService = ref.read(storeServiceProvider);
      final result = await storeService.purchaseItem(widget.item.id);

      if (mounted) {
        if (result.success) {
          Navigator.of(context).pop(true);
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(result.message),
              backgroundColor: Colors.green,
              behavior: SnackBarBehavior.floating,
            ),
          );
        } else {
          setState(() {
            _errorMessage = result.message;
          });
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = 'Purchase failed: ${e.toString()}';
        });
      }
    } finally {
      if (mounted) {
        setState(() {
          _isPurchasing = false;
        });
      }
    }
  }

  IconData _getItemIcon() {
    switch (widget.item.type) {
      case StoreItemType.cosmetic:
        return _getCosmeticIcon();
      case StoreItemType.gameUnlock:
        return Icons.lock_open;
      case StoreItemType.flair:
        return Icons.star;
      case StoreItemType.mysteryDraw:
        return Icons.card_giftcard;
      case StoreItemType.tokenBoost:
        return Icons.trending_up;
      // Legacy types
      case StoreItemType.skin:
        return Icons.palette;
      case StoreItemType.effect:
        return Icons.auto_awesome;
      case StoreItemType.powerup:
        return Icons.flash_on;
      case StoreItemType.theme:
        return Icons.color_lens;
      case StoreItemType.sound:
        return Icons.volume_up;
    }
  }

  IconData _getCosmeticIcon() {
    switch (widget.item.subtype) {
      case StoreItemSubtype.ballSkin:
        return Icons.sports_basketball;
      case StoreItemSubtype.paddleSkin:
        return Icons.sports_tennis;
      case StoreItemSubtype.background:
        return Icons.wallpaper;
      case StoreItemSubtype.runnerAvatar:
        return Icons.person;
      case StoreItemSubtype.leaderboardFrame:
        return Icons.border_outer;
      default:
        return Icons.palette;
    }
  }

  Color _getItemColor() {
    switch (widget.item.type) {
      case StoreItemType.cosmetic:
        return widget.item.rarityColor;
      case StoreItemType.gameUnlock:
        return Colors.amber;
      case StoreItemType.flair:
        return Colors.pink;
      case StoreItemType.mysteryDraw:
        return Colors.deepPurple;
      case StoreItemType.tokenBoost:
        return Colors.green;
      // Legacy types
      case StoreItemType.skin:
        return Colors.purple;
      case StoreItemType.effect:
        return Colors.orange;
      case StoreItemType.powerup:
        return Colors.red;
      case StoreItemType.theme:
        return Colors.blue;
      case StoreItemType.sound:
        return Colors.green;
    }
  }

  List<String> _getItemDetails() {
    final details = <String>[];

    switch (widget.item.type) {
      case StoreItemType.cosmetic:
        details.add('Customize your ${widget.item.subtypeDisplayName.toLowerCase()}');
        details.add('Rarity: ${widget.item.rarityDisplayName}');
        break;
      case StoreItemType.gameUnlock:
        if (widget.item.gameId != null) {
          details.add('Unlock ${widget.item.gameId} game');
        }
        details.add('Permanent access');
        break;
      case StoreItemType.flair:
        details.add('Show off on leaderboards');
        details.add('Rarity: ${widget.item.rarityDisplayName}');
        break;
      case StoreItemType.mysteryDraw:
        details.add('Random reward inside');
        details.add('Could contain rare items');
        break;
      case StoreItemType.tokenBoost:
        if (widget.item.multiplier != null) {
          details.add('${widget.item.multiplier}x token multiplier');
        }
        if (widget.item.duration != null) {
          details.add('Duration: ${widget.item.duration} hours');
        }
        break;
      default:
        // Legacy types
        details.add('${widget.item.typeDisplayName} item');
        break;
    }

    return details;
  }
}

// Helper function to show the purchase dialog
Future<bool> showPurchaseDialog(BuildContext context, StoreItem item) async {
  return await showDialog<bool>(
    context: context,
    builder: (context) => PurchaseDialog(item: item),
  ) ?? false;
}
