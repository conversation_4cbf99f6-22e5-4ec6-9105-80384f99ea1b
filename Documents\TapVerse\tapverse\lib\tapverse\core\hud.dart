import 'package:flame/components.dart';
import 'package:flutter/material.dart';

/// Base HUD component for displaying game information
abstract class HUDComponent extends PositionComponent {
  HUDComponent({super.position, super.size, super.priority = 1000});
}

/// Text component for displaying score
class ScoreText extends TextComponent {
  int _score = 0;
  final String prefix;
  final TextStyle? textStyle;
  
  ScoreText({
    this.prefix = 'Score: ',
    this.textStyle,
    super.position,
    super.priority = 1000,
  }) : super(
    text: '${prefix}0',
    textRenderer: TextPaint(
      style: textStyle ?? const TextStyle(
        color: Colors.white,
        fontSize: 24,
        fontWeight: FontWeight.bold,
      ),
    ),
  );
  
  void updateScore(int score) {
    _score = score;
    text = '$prefix$_score';
  }
  
  int get score => _score;
}

/// Text component for displaying timer
class TimerText extends TextComponent {
  Duration _time = Duration.zero;
  final String prefix;
  final TextStyle? textStyle;
  final bool showMilliseconds;
  
  TimerText({
    this.prefix = 'Time: ',
    this.textStyle,
    this.showMilliseconds = false,
    super.position,
    super.priority = 1000,
  }) : super(
    text: '${prefix}0:00',
    textRenderer: TextPaint(
      style: textStyle ?? const TextStyle(
        color: Colors.white,
        fontSize: 20,
        fontWeight: FontWeight.bold,
      ),
    ),
  );
  
  void updateTime(Duration duration) {
    _time = duration;
    text = '$prefix${_formatTime(duration)}';
  }
  
  String _formatTime(Duration duration) {
    final minutes = duration.inMinutes;
    final seconds = duration.inSeconds % 60;
    
    if (showMilliseconds) {
      final milliseconds = (duration.inMilliseconds % 1000) ~/ 100;
      return '$minutes:${seconds.toString().padLeft(2, '0')}.$milliseconds';
    } else {
      return '$minutes:${seconds.toString().padLeft(2, '0')}';
    }
  }
  
  Duration get time => _time;
}

/// Text component for displaying lives
class LivesText extends TextComponent {
  int _lives = 0;
  final String prefix;
  final TextStyle? textStyle;
  
  LivesText({
    this.prefix = 'Lives: ',
    this.textStyle,
    super.position,
    super.priority = 1000,
  }) : super(
    text: '${prefix}0',
    textRenderer: TextPaint(
      style: textStyle ?? const TextStyle(
        color: Colors.white,
        fontSize: 20,
        fontWeight: FontWeight.bold,
      ),
    ),
  );
  
  void updateLives(int lives) {
    _lives = lives;
    text = '$prefix$_lives';
  }
  
  int get lives => _lives;
}

/// Text component for displaying level
class LevelText extends TextComponent {
  int _level = 1;
  final String prefix;
  final TextStyle? textStyle;
  
  LevelText({
    this.prefix = 'Level: ',
    this.textStyle,
    super.position,
    super.priority = 1000,
  }) : super(
    text: '${prefix}1',
    textRenderer: TextPaint(
      style: textStyle ?? const TextStyle(
        color: Colors.white,
        fontSize: 20,
        fontWeight: FontWeight.bold,
      ),
    ),
  );
  
  void updateLevel(int level) {
    _level = level;
    text = '$prefix$_level';
  }
  
  int get level => _level;
}

/// Text component for displaying combo/streak
class ComboText extends TextComponent {
  int _combo = 0;
  final String prefix;
  final TextStyle? textStyle;
  final int minDisplayCombo;
  
  ComboText({
    this.prefix = 'Combo: ',
    this.textStyle,
    this.minDisplayCombo = 2,
    super.position,
    super.priority = 1000,
  }) : super(
    text: '',
    textRenderer: TextPaint(
      style: textStyle ?? const TextStyle(
        color: Colors.yellow,
        fontSize: 18,
        fontWeight: FontWeight.bold,
      ),
    ),
  );
  
  void updateCombo(int combo) {
    _combo = combo;
    if (combo >= minDisplayCombo) {
      text = '$prefix$_combo';
    } else {
      text = '';
    }
  }
  
  int get combo => _combo;
}

/// Progress bar component for displaying health, energy, etc.
class ProgressBar extends RectangleComponent {
  double _progress = 1.0;
  final Color backgroundColor;
  final Color foregroundColor;
  final Color borderColor;
  final double borderWidth;
  
  ProgressBar({
    required super.size,
    super.position,
    this.backgroundColor = Colors.red,
    this.foregroundColor = Colors.green,
    this.borderColor = Colors.white,
    this.borderWidth = 2.0,
    super.priority = 1000,
  }) : super(
    paint: Paint()..color = backgroundColor,
  );
  
  @override
  void render(Canvas canvas) {
    // Draw background
    canvas.drawRect(size.toRect(), paint);
    
    // Draw progress
    if (_progress > 0) {
      final progressWidth = size.x * _progress;
      final progressPaint = Paint()..color = foregroundColor;
      canvas.drawRect(
        Rect.fromLTWH(0, 0, progressWidth, size.y),
        progressPaint,
      );
    }
    
    // Draw border
    if (borderWidth > 0) {
      final borderPaint = Paint()
        ..color = borderColor
        ..style = PaintingStyle.stroke
        ..strokeWidth = borderWidth;
      canvas.drawRect(size.toRect(), borderPaint);
    }
  }
  
  void updateProgress(double progress) {
    _progress = progress.clamp(0.0, 1.0);
  }
  
  double get progress => _progress;
}

/// Utility class for creating common HUD layouts
class HUDLayout {
  /// Creates a standard top-left HUD with score and timer
  static List<Component> createTopLeftHUD({
    Vector2? scorePosition,
    Vector2? timerPosition,
    TextStyle? scoreStyle,
    TextStyle? timerStyle,
  }) {
    return [
      ScoreText(
        position: scorePosition ?? Vector2(16, 16),
        textStyle: scoreStyle,
      ),
      TimerText(
        position: timerPosition ?? Vector2(16, 50),
        textStyle: timerStyle,
      ),
    ];
  }
  
  /// Creates a standard top-right HUD with lives and level
  static List<Component> createTopRightHUD({
    Vector2? livesPosition,
    Vector2? levelPosition,
    TextStyle? livesStyle,
    TextStyle? levelStyle,
    required double screenWidth,
  }) {
    return [
      LivesText(
        position: livesPosition ?? Vector2(screenWidth - 120, 16),
        textStyle: livesStyle,
      ),
      LevelText(
        position: levelPosition ?? Vector2(screenWidth - 120, 50),
        textStyle: levelStyle,
      ),
    ];
  }
  
  /// Creates a centered combo display
  static ComboText createCenteredCombo({
    required Vector2 screenSize,
    TextStyle? style,
  }) {
    return ComboText(
      position: Vector2(screenSize.x / 2 - 50, 100),
      textStyle: style,
    );
  }
}
