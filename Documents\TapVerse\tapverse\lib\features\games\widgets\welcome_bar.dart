import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../../core/providers/app_providers.dart';
import '../../../core/services/game_navigation_service.dart';
import '../../auth/widgets/account_upgrade_dialog.dart';

class WelcomeBar extends ConsumerStatefulWidget {
  const WelcomeBar({super.key});

  @override
  ConsumerState<WelcomeBar> createState() => _WelcomeBarState();
}

class _WelcomeBarState extends ConsumerState<WelcomeBar>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _fadeAnimation;
  bool _isVisible = true;
  bool _isInitialized = false;

  static const String _welcomeBarDismissedKey = 'welcome_bar_dismissed';

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, -1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    ));

    _loadDismissedState();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _loadDismissedState() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final isDismissed = prefs.getBool(_welcomeBarDismissedKey) ?? false;
      
      if (mounted) {
        setState(() {
          _isVisible = !isDismissed;
          _isInitialized = true;
        });

        if (_isVisible) {
          // Delay the animation slightly for better UX
          await Future.delayed(const Duration(milliseconds: 300));
          if (mounted) {
            _animationController.forward();
          }
        }
      }
    } catch (e) {
      print('Error loading welcome bar state: $e');
      if (mounted) {
        setState(() {
          _isVisible = true;
          _isInitialized = true;
        });
        _animationController.forward();
      }
    }
  }

  Future<void> _dismissWelcomeBar() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_welcomeBarDismissedKey, true);
      
      if (mounted) {
        await _animationController.reverse();
        setState(() {
          _isVisible = false;
        });
      }
    } catch (e) {
      print('Error dismissing welcome bar: $e');
    }
  }

  void _showUpgradeDialog() {
    showDialog(
      context: context,
      builder: (context) => const AccountUpgradeDialog(),
    );
  }

  @override
  Widget build(BuildContext context) {
    if (!_isInitialized || !_isVisible) {
      return const SizedBox.shrink();
    }

    final authState = ref.watch(authStateProvider);
    final gameNavigation = ref.watch(gameNavigationServiceProvider);

    return authState.when(
      data: (user) {
        if (user == null) return const SizedBox.shrink();

        final isAnonymous = user.isAnonymous;
        final displayName = user.displayName ?? 'Player';

        return SlideTransition(
          position: _slideAnimation,
          child: FadeTransition(
            opacity: _fadeAnimation,
            child: Container(
              margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(16),
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    Colors.white.withValues(alpha: 0.1),
                    Colors.white.withValues(alpha: 0.05),
                  ],
                ),
                border: Border.all(
                  color: Colors.white.withValues(alpha: 0.2),
                  width: 1,
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 20,
                    offset: const Offset(0, 8),
                  ),
                ],
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(16),
                child: BackdropFilter(
                  filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                    child: Row(
                      children: [
                        // Content
                        Expanded(
                          child: isAnonymous
                              ? _buildAnonymousContent()
                              : _buildAuthenticatedContent(displayName, gameNavigation),
                        ),
                        
                        // Dismiss button
                        IconButton(
                          onPressed: _dismissWelcomeBar,
                          icon: Icon(
                            Icons.keyboard_arrow_up,
                            color: Colors.white.withValues(alpha: 0.7),
                          ),
                          tooltip: 'Dismiss',
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
        );
      },
      loading: () => const SizedBox.shrink(),
      error: (_, __) => const SizedBox.shrink(),
    );
  }

  Widget _buildAnonymousContent() {
    return Row(
      children: [
        Icon(
          Icons.person_outline,
          color: Colors.amber.withValues(alpha: 0.8),
          size: 20,
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Text(
            'Sign up to access more features!',
            style: TextStyle(
              color: Colors.white.withValues(alpha: 0.9),
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        const SizedBox(width: 12),
        ElevatedButton(
          onPressed: _showUpgradeDialog,
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.amber,
            foregroundColor: Colors.black,
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(20),
            ),
            elevation: 0,
          ),
          child: const Text(
            'Sign Up',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 12,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildAuthenticatedContent(String displayName, GameNavigationService gameNavigation) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(6),
          decoration: BoxDecoration(
            color: Colors.green.withValues(alpha: 0.2),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Icon(
            Icons.person,
            color: Colors.green.withValues(alpha: 0.8),
            size: 16,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Text(
            'Welcome back, $displayName!',
            style: TextStyle(
              color: Colors.white.withValues(alpha: 0.9),
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        const SizedBox(width: 12),
        
        // Quick action buttons
        _buildQuickActionButton(
          icon: Icons.emoji_events,
          color: Colors.amber,
          onTap: () => gameNavigation.navigateToProfile(context),
          tooltip: 'Achievements',
        ),
        const SizedBox(width: 8),
        _buildQuickActionButton(
          icon: Icons.settings,
          color: Colors.blue,
          onTap: () => gameNavigation.navigateToSettings(context),
          tooltip: 'Profile Settings',
        ),
      ],
    );
  }

  Widget _buildQuickActionButton({
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
    required String tooltip,
  }) {
    return Tooltip(
      message: tooltip,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Container(
          padding: const EdgeInsets.all(6),
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.2),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            icon,
            color: color.withValues(alpha: 0.8),
            size: 16,
          ),
        ),
      ),
    );
  }
}
