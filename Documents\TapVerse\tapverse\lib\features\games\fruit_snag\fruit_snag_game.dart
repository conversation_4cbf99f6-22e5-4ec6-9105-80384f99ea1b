import 'dart:math';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../../../core/models/game_state.dart';
import '../../../core/providers/game_providers.dart';
import '../../../core/services/game_feedback_service.dart';
import '../../../shared/widgets/base_game_widget.dart';

class FruitSnagGame extends BaseGameWidget {
  static const GameConfig gameConfig = GameConfig(
    gameId: 'fruit_snag',
    gameName: 'FruitSnag',
    scoreMultiplier: 1,
    hasLives: false,
    hasLevels: false,
    hasTimer: false,
  );

  const FruitSnagGame({
    super.key,
    super.onGameComplete,
    super.onGameExit,
  }) : super(config: gameConfig);

  @override
  Widget buildGameContent(BuildContext context, WidgetRef ref, GameState gameState) {
    return const _FruitSnagGameContent();
  }
}

class FallingItem {
  Offset position;
  final double speed;
  final String emoji;
  final int points;
  final Color color;
  bool isCaught;
  bool isMissed;

  FallingItem({
    required this.position,
    required this.speed,
    required this.emoji,
    required this.points,
    required this.color,
    this.isCaught = false,
    this.isMissed = false,
  });
}

class _FruitSnagGameContent extends ConsumerStatefulWidget {
  const _FruitSnagGameContent();

  @override
  ConsumerState<_FruitSnagGameContent> createState() => _FruitSnagGameContentState();
}

class _FruitSnagGameContentState extends ConsumerState<_FruitSnagGameContent>
    with TickerProviderStateMixin {
  
  // Game state
  late AnimationController _basketController;
  late AnimationController _catchController;
  late AnimationController _missController;
  
  // Basket state
  double _basketX = 200;
  final double _basketY = 520;
  final double _basketWidth = 80;
  final double _basketHeight = 40;
  
  // Falling items
  final List<FallingItem> _fallingItems = [];
  final Random _random = Random();
  double _spawnTimer = 0;
  double _spawnInterval = 1.5;
  
  // Game mechanics
  int _missedItems = 0;
  final int _maxMisses = 5;
  bool _showCatchEffect = false;
  bool _showMissEffect = false;
  Offset? _catchPosition;
  
  // Item types
  final List<Map<String, dynamic>> _itemTypes = [
    {'emoji': '🍎', 'points': 10, 'color': Colors.red},
    {'emoji': '🍊', 'points': 15, 'color': Colors.orange},
    {'emoji': '🍌', 'points': 20, 'color': Colors.yellow},
    {'emoji': '🍇', 'points': 25, 'color': Colors.purple},
    {'emoji': '🍓', 'points': 30, 'color': Colors.pink},
    {'emoji': '🥝', 'points': 35, 'color': Colors.green},
    {'emoji': '💎', 'points': 50, 'color': Colors.cyan}, // Special item
  ];
  
  // Constants
  static const double _gameWidth = 400;
  static const double _gameHeight = 600;
  static const double _itemSize = 30;

  @override
  void initState() {
    super.initState();
    
    _basketController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );
    
    _catchController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );
    
    _missController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _startGameLoop();
  }

  @override
  void dispose() {
    _basketController.dispose();
    _catchController.dispose();
    _missController.dispose();
    super.dispose();
  }

  void _startGameLoop() {
    const frameRate = 60;
    const frameDuration = Duration(milliseconds: 1000 ~/ frameRate);
    const deltaTime = 1.0 / frameRate;
    
    void gameLoop() {
      if (!mounted) return;
      
      final gameState = ref.read(gameStateProvider(FruitSnagGame.gameConfig));
      if (!gameState.isPlaying) {
        Future.delayed(frameDuration, gameLoop);
        return;
      }
      
      setState(() {
        // Spawn items
        _spawnTimer += deltaTime;
        if (_spawnTimer >= _spawnInterval) {
          _spawnItem();
          _spawnTimer = 0;
          // Gradually increase difficulty
          _spawnInterval = (_spawnInterval * 0.99).clamp(0.8, 2.0);
        }
        
        // Update falling items
        for (final item in _fallingItems) {
          item.position = Offset(
            item.position.dx,
            item.position.dy + item.speed * deltaTime,
          );
          
          // Check basket collision
          if (!item.isCaught && !item.isMissed) {
            _checkBasketCollision(item);
          }
          
          // Check if item missed
          if (!item.isCaught && !item.isMissed && item.position.dy > _gameHeight) {
            item.isMissed = true;
            _handleMiss();
          }
        }
        
        // Remove inactive items
        _fallingItems.removeWhere((item) => item.isCaught || item.isMissed);
      });
      
      Future.delayed(frameDuration, gameLoop);
    }
    
    gameLoop();
  }

  void _spawnItem() {
    final itemData = _itemTypes[_random.nextInt(_itemTypes.length)];
    final x = _random.nextDouble() * (_gameWidth - _itemSize) + _itemSize / 2;
    final speed = 100 + _random.nextDouble() * 100; // 100-200 speed
    
    _fallingItems.add(FallingItem(
      position: Offset(x, -_itemSize),
      speed: speed,
      emoji: itemData['emoji'],
      points: itemData['points'],
      color: itemData['color'],
    ));
  }

  void _checkBasketCollision(FallingItem item) {
    final itemLeft = item.position.dx - _itemSize / 2;
    final itemRight = item.position.dx + _itemSize / 2;
    final itemTop = item.position.dy - _itemSize / 2;
    final itemBottom = item.position.dy + _itemSize / 2;
    
    final basketLeft = _basketX - _basketWidth / 2;
    final basketRight = _basketX + _basketWidth / 2;
    final basketTop = _basketY - _basketHeight / 2;
    final basketBottom = _basketY + _basketHeight / 2;
    
    if (itemRight >= basketLeft &&
        itemLeft <= basketRight &&
        itemBottom >= basketTop &&
        itemTop <= basketBottom) {
      
      // Item caught!
      item.isCaught = true;
      _handleCatch(item);
    }
  }

  void _handleCatch(FallingItem item) {
    // Add score
    final gameNotifier = ref.read(gameStateProvider(FruitSnagGame.gameConfig).notifier);
    gameNotifier.addScore(item.points);
    
    // Show catch effect
    setState(() {
      _showCatchEffect = true;
      _catchPosition = item.position;
    });
    
    // Trigger feedback
    if (item.emoji == '💎') {
      // Special item feedback
      ref.read(gameFeedbackServiceProvider).triggerComboFeedback(3);
    } else {
      ref.read(gameFeedbackServiceProvider).triggerFeedback(GameEvent.scoreIncrease);
    }
    
    // Play animations
    _basketController.forward().then((_) => _basketController.reset());
    _catchController.forward().then((_) {
      _catchController.reset();
      setState(() {
        _showCatchEffect = false;
      });
    });
  }

  void _handleMiss() {
    setState(() {
      _missedItems++;
      _showMissEffect = true;
    });
    
    // Trigger feedback
    ref.read(gameFeedbackServiceProvider).triggerFeedback(GameEvent.obstacleHit);
    
    // Play miss animation
    _missController.forward().then((_) {
      _missController.reset();
      setState(() {
        _showMissEffect = false;
      });
    });
    
    // Check game over
    if (_missedItems >= _maxMisses) {
      final gameNotifier = ref.read(gameStateProvider(FruitSnagGame.gameConfig).notifier);
      gameNotifier.endGame(reason: 'Too many missed items');
    }
  }

  void _onPanUpdate(DragUpdateDetails details) {
    final gameState = ref.read(gameStateProvider(FruitSnagGame.gameConfig));
    if (!gameState.isPlaying) return;
    
    setState(() {
      _basketX = details.localPosition.dx.clamp(
        _basketWidth / 2,
        _gameWidth - _basketWidth / 2,
      );
    });
  }

  @override
  Widget build(BuildContext context) {
    final gameState = ref.watch(gameStateProvider(FruitSnagGame.gameConfig));
    
    return Container(
      width: _gameWidth,
      height: _gameHeight,
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [Color(0xFF87CEEB), Color(0xFF98FB98)],
        ),
      ),
      child: GestureDetector(
        onPanUpdate: _onPanUpdate,
        child: Stack(
          children: [
            // Background
            _buildBackground(),
            
            // Falling items
            ..._fallingItems.map((item) => _buildFallingItem(item)),
            
            // Basket
            _buildBasket(),
            
            // Catch effect
            if (_showCatchEffect && _catchPosition != null) _buildCatchEffect(),
            
            // Miss effect
            if (_showMissEffect) _buildMissEffect(),
            
            // Game info
            _buildGameInfo(gameState),
            
            // Instructions
            if (!gameState.isPlaying) _buildInstructions(),
          ],
        ),
      ),
    );
  }

  Widget _buildBackground() {
    return Positioned.fill(
      child: CustomPaint(
        painter: _BackgroundPainter(),
      ),
    );
  }

  Widget _buildFallingItem(FallingItem item) {
    return Positioned(
      left: item.position.dx - _itemSize / 2,
      top: item.position.dy - _itemSize / 2,
      child: Container(
        width: _itemSize,
        height: _itemSize,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          gradient: RadialGradient(
            colors: [item.color.withOpacity(0.8), item.color],
            stops: const [0.3, 1.0],
          ),
          boxShadow: [
            BoxShadow(
              color: item.color.withOpacity(0.4),
              blurRadius: 6,
              spreadRadius: 2,
            ),
          ],
        ),
        child: Center(
          child: Text(
            item.emoji,
            style: const TextStyle(fontSize: 20),
          ),
        ),
      ),
    );
  }

  Widget _buildBasket() {
    return Positioned(
      left: _basketX - _basketWidth / 2,
      top: _basketY - _basketHeight / 2,
      child: ScaleTransition(
        scale: Tween<double>(begin: 1.0, end: 1.1).animate(_basketController),
        child: Container(
          width: _basketWidth,
          height: _basketHeight,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [Colors.brown[400]!, Colors.brown[600]!],
            ),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.brown[800]!, width: 2),
          ),
          child: CustomPaint(
            painter: _BasketPainter(),
          ),
        ),
      ),
    );
  }

  Widget _buildCatchEffect() {
    return Positioned(
      left: _catchPosition!.dx - 30,
      top: _catchPosition!.dy - 30,
      child: ScaleTransition(
        scale: _catchController,
        child: Container(
          width: 60,
          height: 60,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            gradient: RadialGradient(
              colors: [Colors.yellow, Colors.orange, Colors.transparent],
              stops: const [0.0, 0.5, 1.0],
            ),
          ),
          child: const Center(
            child: Text(
              '✨',
              style: TextStyle(fontSize: 30),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildMissEffect() {
    return Positioned(
      bottom: 50,
      left: 0,
      right: 0,
      child: ScaleTransition(
        scale: _missController,
        child: const Center(
          child: Text(
            'MISSED!',
            style: TextStyle(
              color: Colors.red,
              fontSize: 32,
              fontWeight: FontWeight.bold,
              shadows: [
                Shadow(
                  offset: Offset(2, 2),
                  blurRadius: 4,
                  color: Colors.black54,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildGameInfo(GameState gameState) {
    return Positioned(
      top: 30,
      left: 20,
      right: 20,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.black54,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              children: [
                const Text(
                  'Score',
                  style: TextStyle(color: Colors.white, fontSize: 12),
                ),
                Text(
                  '${gameState.score}',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.black54,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              children: [
                const Text(
                  'Misses',
                  style: TextStyle(color: Colors.white, fontSize: 12),
                ),
                Text(
                  '$_missedItems/$_maxMisses',
                  style: TextStyle(
                    color: _missedItems >= _maxMisses - 1 ? Colors.red : Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInstructions() {
    return Positioned(
      bottom: 100,
      left: 0,
      right: 0,
      child: Center(
        child: Container(
          padding: EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.black54,
            borderRadius: BorderRadius.all(Radius.circular(12)),
          ),
          child: Text(
            'Drag the basket to catch falling fruits!\nDifferent fruits give different points.\n💎 gems are worth the most!\nDon\'t miss too many!',
            textAlign: TextAlign.center,
            style: TextStyle(
              color: Colors.white,
              fontSize: 16,
            ),
          ),
        ),
      ),
    );
  }
}

class _BackgroundPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    // Draw clouds
    final cloudPaint = Paint()
      ..color = Colors.white.withOpacity(0.6)
      ..style = PaintingStyle.fill;
    
    canvas.drawCircle(Offset(80, 60), 20, cloudPaint);
    canvas.drawCircle(Offset(100, 60), 25, cloudPaint);
    canvas.drawCircle(Offset(120, 60), 20, cloudPaint);
    
    canvas.drawCircle(Offset(300, 100), 15, cloudPaint);
    canvas.drawCircle(Offset(315, 100), 20, cloudPaint);
    canvas.drawCircle(Offset(330, 100), 15, cloudPaint);
    
    // Draw ground
    final groundPaint = Paint()
      ..color = Colors.green[600]!
      ..style = PaintingStyle.fill;
    
    canvas.drawRect(
      Rect.fromLTWH(0, size.height - 50, size.width, 50),
      groundPaint,
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class _BasketPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.brown[800]!
      ..strokeWidth = 2
      ..style = PaintingStyle.stroke;
    
    // Draw basket weave pattern
    for (double x = 5; x < size.width - 5; x += 8) {
      canvas.drawLine(
        Offset(x, 5),
        Offset(x, size.height - 5),
        paint,
      );
    }
    
    for (double y = 5; y < size.height - 5; y += 6) {
      canvas.drawLine(
        Offset(5, y),
        Offset(size.width - 5, y),
        paint,
      );
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
