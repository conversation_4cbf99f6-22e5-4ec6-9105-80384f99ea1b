import 'dart:math';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../../../core/models/game_state.dart';
import '../../../core/providers/game_providers.dart';
import '../../../core/services/game_feedback_service.dart';
import '../../../shared/widgets/base_game_widget.dart';

class PopBurstGame extends TapVerseBaseGame {
  static const GameConfig gameConfig = GameConfig(
    gameId: 'pop_burst',
    gameName: 'PopBurst',
    scoreMultiplier: 1,
    hasLives: false,
    hasLevels: false,
    hasTimer: false,
  );

  const PopBurstGame({
    super.key,
    super.onGameComplete,
    super.onGameExit,
  }) : super(config: gameConfig);

  @override
  Widget buildGameContentWithWalls(BuildContext context, WidgetRef ref, GameState gameState) {
    return const _PopBurstGameContent();
  }

  @override
  void startGame(WidgetRef ref) {
    // Game-specific startup logic will be handled by _PopBurstGameContent
  }

  @override
  void resetGame(WidgetRef ref) {
    // Game-specific reset logic will be handled by _PopBurstGameContent
  }

  @override
  void disposeGame(WidgetRef ref) {
    // Clean up any game-specific resources
  }
}

class Balloon {
  Offset position;
  final double speed;
  final Color color;
  final double size;
  bool isPopped;
  bool hasEscaped;

  Balloon({
    required this.position,
    required this.speed,
    required this.color,
    required this.size,
    this.isPopped = false,
    this.hasEscaped = false,
  });
}

class _PopBurstGameContent extends ConsumerStatefulWidget {
  const _PopBurstGameContent();

  @override
  ConsumerState<_PopBurstGameContent> createState() => _PopBurstGameContentState();
}

class _PopBurstGameContentState extends ConsumerState<_PopBurstGameContent>
    with TickerProviderStateMixin {
  
  // Game state
  late AnimationController _popController;
  late AnimationController _spawnController;
  
  // Balloons
  final List<Balloon> _balloons = [];
  final Random _random = Random();
  double _spawnTimer = 0;
  double _spawnInterval = 1.5;
  
  // Game mechanics
  int _balloonsPopped = 0;
  int _balloonsEscaped = 0;
  final int _maxEscaped = 10;
  final List<Offset> _popEffects = [];
  
  // Balloon types
  final List<Map<String, dynamic>> _balloonTypes = [
    {'color': Colors.red, 'points': 10, 'size': 40.0},
    {'color': Colors.blue, 'points': 15, 'size': 35.0},
    {'color': Colors.green, 'points': 20, 'size': 30.0},
    {'color': Colors.yellow, 'points': 25, 'size': 25.0},
    {'color': Colors.purple, 'points': 30, 'size': 20.0},
    {'color': Colors.orange, 'points': 50, 'size': 15.0}, // Special small balloon
  ];
  
  // Game area - will be updated based on actual screen size
  double _gameWidth = 400;
  double _gameHeight = 600;

  @override
  void initState() {
    super.initState();
    
    _popController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _spawnController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    
    _startGameLoop();
  }

  @override
  void dispose() {
    _popController.dispose();
    _spawnController.dispose();
    super.dispose();
  }

  void _startGameLoop() {
    const frameRate = 60;
    const frameDuration = Duration(milliseconds: 1000 ~/ frameRate);
    const deltaTime = 1.0 / frameRate;
    
    void gameLoop() {
      if (!mounted) return;
      
      final gameState = ref.read(gameStateProvider(PopBurstGame.gameConfig));
      if (!gameState.isPlaying) {
        Future.delayed(frameDuration, gameLoop);
        return;
      }
      
      setState(() {
        // Spawn balloons
        _spawnTimer += deltaTime;
        if (_spawnTimer >= _spawnInterval) {
          _spawnBalloon();
          _spawnTimer = 0;
          // Gradually increase difficulty
          _spawnInterval = (_spawnInterval * 0.98).clamp(0.5, 2.0);
        }
        
        // Update balloons
        for (final balloon in _balloons) {
          if (!balloon.isPopped && !balloon.hasEscaped) {
            balloon.position = Offset(
              balloon.position.dx,
              balloon.position.dy - balloon.speed * deltaTime,
            );
            
            // Check if balloon escaped (hit top wall)
            if (balloon.position.dy < TapVerseWallCollision.wallThickness - balloon.size / 2) {
              balloon.hasEscaped = true;
              _handleEscape();
            }
          }
        }
        
        // Remove inactive balloons
        _balloons.removeWhere((balloon) => 
          balloon.isPopped || balloon.hasEscaped);
      });
      
      Future.delayed(frameDuration, gameLoop);
    }
    
    gameLoop();
  }

  void _spawnBalloon() {
    final balloonData = _balloonTypes[_random.nextInt(_balloonTypes.length)];
    final balloonSize = balloonData['size'] as double;

    // Use playable area (excluding walls)
    final playableArea = TapVerseWallCollision.getPlayableArea(Size(_gameWidth, _gameHeight));
    final playableOffset = TapVerseWallCollision.getPlayableAreaOffset();

    // Spawn within playable area
    final x = playableOffset.dx + _random.nextDouble() * (playableArea.width - balloonSize) + balloonSize / 2;
    final speed = 50 + _random.nextDouble() * 100; // 50-150 speed

    _balloons.add(Balloon(
      position: Offset(x, _gameHeight + balloonSize),
      speed: speed,
      color: balloonData['color'],
      size: balloonSize,
    ));

    // Play spawn animation
    _spawnController.forward().then((_) => _spawnController.reset());
  }

  void _handleEscape() {
    setState(() {
      _balloonsEscaped++;
    });
    
    // Check game over
    if (_balloonsEscaped >= _maxEscaped) {
      final gameNotifier = ref.read(gameStateProvider(PopBurstGame.gameConfig).notifier);
      gameNotifier.endGame(reason: 'Too many balloons escaped');
    }
  }

  void _onTapDown(TapDownDetails details) {
    final gameState = ref.read(gameStateProvider(PopBurstGame.gameConfig));
    if (!gameState.isPlaying) return;
    
    final tapPosition = details.localPosition;
    
    // Check if any balloon was tapped
    for (final balloon in _balloons) {
      if (balloon.isPopped || balloon.hasEscaped) continue;
      
      final distance = (balloon.position - tapPosition).distance;
      if (distance <= balloon.size / 2) {
        _popBalloon(balloon, tapPosition);
        break; // Only pop one balloon per tap
      }
    }
  }

  void _popBalloon(Balloon balloon, Offset tapPosition) {
    setState(() {
      balloon.isPopped = true;
      _balloonsPopped++;
      _popEffects.add(tapPosition);
    });
    
    // Calculate points based on balloon type
    final points = _balloonTypes.firstWhere(
      (type) => type['color'] == balloon.color && type['size'] == balloon.size,
      orElse: () => {'points': 10},
    )['points'] as int;
    
    // Add score
    final gameNotifier = ref.read(gameStateProvider(PopBurstGame.gameConfig).notifier);
    gameNotifier.addScore(points);
    
    // Trigger feedback based on balloon size (smaller = better)
    if (balloon.size <= 20) {
      ref.read(gameFeedbackServiceProvider).triggerComboFeedback(2);
    } else {
      ref.read(gameFeedbackServiceProvider).triggerFeedback(GameEvent.scoreIncrease);
    }
    
    // Play pop animation
    _popController.forward().then((_) {
      _popController.reset();
      setState(() {
        _popEffects.clear();
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    final gameState = ref.watch(gameStateProvider(PopBurstGame.gameConfig));

    // Update game dimensions based on screen size
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final size = MediaQuery.of(context).size;
      if (_gameWidth != size.width || _gameHeight != size.height) {
        setState(() {
          _gameWidth = size.width;
          _gameHeight = size.height;
        });
      }
    });

    return Container(
      width: double.infinity,
      height: double.infinity,
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [Color(0xFF87CEEB), Color(0xFFFFE4B5)],
        ),
      ),
      child: GestureDetector(
        onTapDown: _onTapDown,
        child: Stack(
          children: [
            // Background
            _buildBackground(),
            
            // Balloons
            ..._balloons.map((balloon) => _buildBalloon(balloon)),
            
            // Pop effects
            ..._popEffects.map((effect) => _buildPopEffect(effect)),
            
            // Game info
            _buildGameInfo(gameState),
            
            // Instructions
            if (!gameState.isPlaying) _buildInstructions(),
          ],
        ),
      ),
    );
  }

  Widget _buildBackground() {
    return Positioned.fill(
      child: CustomPaint(
        painter: _BackgroundPainter(),
      ),
    );
  }

  Widget _buildBalloon(Balloon balloon) {
    return Positioned(
      left: balloon.position.dx - balloon.size / 2,
      top: balloon.position.dy - balloon.size / 2,
      child: ScaleTransition(
        scale: balloon.isPopped 
            ? const AlwaysStoppedAnimation(0.0)
            : const AlwaysStoppedAnimation(1.0),
        child: Container(
          width: balloon.size,
          height: balloon.size,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            gradient: RadialGradient(
              colors: [
                balloon.color.withOpacity(0.8),
                balloon.color,
                balloon.color.withOpacity(0.6),
              ],
              stops: const [0.0, 0.7, 1.0],
            ),
            boxShadow: [
              BoxShadow(
                color: balloon.color.withOpacity(0.4),
                blurRadius: 8,
                spreadRadius: 2,
              ),
            ],
          ),
          child: Stack(
            children: [
              // Balloon highlight
              Positioned(
                top: balloon.size * 0.2,
                left: balloon.size * 0.3,
                child: Container(
                  width: balloon.size * 0.2,
                  height: balloon.size * 0.3,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(balloon.size * 0.15),
                    color: Colors.white.withOpacity(0.6),
                  ),
                ),
              ),
              // Balloon string
              Positioned(
                bottom: -10,
                left: balloon.size / 2 - 1,
                child: Container(
                  width: 2,
                  height: 15,
                  color: Colors.brown[600],
                ),
              ),
            ],
          ),
        ),
      ).animate(target: balloon.isPopped ? 1 : 0)
       .scale(begin: const Offset(1, 1), end: const Offset(0, 0))
       .fadeOut(),
    );
  }

  Widget _buildPopEffect(Offset position) {
    return Positioned(
      left: position.dx - 30,
      top: position.dy - 30,
      child: ScaleTransition(
        scale: _popController,
        child: Container(
          width: 60,
          height: 60,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            gradient: RadialGradient(
              colors: [
                Colors.yellow,
                Colors.orange,
                Colors.red,
                Colors.transparent,
              ],
              stops: const [0.0, 0.3, 0.6, 1.0],
            ),
          ),
          child: const Center(
            child: Text(
              'POP!',
              style: TextStyle(
                color: Colors.white,
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildGameInfo(GameState gameState) {
    return Positioned(
      top: 30,
      left: 20,
      right: 20,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.black54,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              children: [
                const Text(
                  'Score',
                  style: TextStyle(color: Colors.white, fontSize: 12),
                ),
                Text(
                  '${gameState.score}',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.black54,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              children: [
                const Text(
                  'Popped',
                  style: TextStyle(color: Colors.white, fontSize: 12),
                ),
                Text(
                  '$_balloonsPopped',
                  style: const TextStyle(
                    color: Colors.green,
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.black54,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              children: [
                const Text(
                  'Escaped',
                  style: TextStyle(color: Colors.white, fontSize: 12),
                ),
                Text(
                  '$_balloonsEscaped/$_maxEscaped',
                  style: TextStyle(
                    color: _balloonsEscaped >= _maxEscaped - 2 ? Colors.red : Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInstructions() {
    return Positioned(
      bottom: 100,
      left: 0,
      right: 0,
      child: Center(
        child: Container(
          padding: EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.black54,
            borderRadius: BorderRadius.all(Radius.circular(12)),
          ),
          child: Text(
            'Tap balloons before they float away!\nSmaller balloons = more points.\nDon\'t let too many escape!',
            textAlign: TextAlign.center,
            style: TextStyle(
              color: Colors.white,
              fontSize: 16,
            ),
          ),
        ),
      ),
    );
  }
}

class _BackgroundPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    // Draw clouds
    final cloudPaint = Paint()
      ..color = Colors.white.withOpacity(0.7)
      ..style = PaintingStyle.fill;
    
    // Cloud 1
    canvas.drawCircle(Offset(80, 80), 20, cloudPaint);
    canvas.drawCircle(Offset(100, 80), 25, cloudPaint);
    canvas.drawCircle(Offset(120, 80), 20, cloudPaint);
    
    // Cloud 2
    canvas.drawCircle(Offset(300, 120), 15, cloudPaint);
    canvas.drawCircle(Offset(315, 120), 20, cloudPaint);
    canvas.drawCircle(Offset(330, 120), 15, cloudPaint);
    
    // Cloud 3
    canvas.drawCircle(Offset(150, 200), 18, cloudPaint);
    canvas.drawCircle(Offset(170, 200), 22, cloudPaint);
    canvas.drawCircle(Offset(190, 200), 18, cloudPaint);
    
    // Draw sun
    final sunPaint = Paint()
      ..color = Colors.yellow[300]!
      ..style = PaintingStyle.fill;
    
    canvas.drawCircle(Offset(350, 50), 25, sunPaint);
    
    // Draw sun rays
    final rayPaint = Paint()
      ..color = Colors.yellow[200]!
      ..strokeWidth = 3
      ..style = PaintingStyle.stroke;
    
    for (int i = 0; i < 8; i++) {
      final angle = i * pi / 4;
      final startX = 350 + cos(angle) * 35;
      final startY = 50 + sin(angle) * 35;
      final endX = 350 + cos(angle) * 45;
      final endY = 50 + sin(angle) * 45;
      
      canvas.drawLine(
        Offset(startX, startY),
        Offset(endX, endY),
        rayPaint,
      );
    }
    
    // Draw grass at bottom
    final grassPaint = Paint()
      ..color = Colors.green[400]!
      ..style = PaintingStyle.fill;
    
    canvas.drawRect(
      Rect.fromLTWH(0, size.height - 30, size.width, 30),
      grassPaint,
    );
    
    // Draw grass blades
    final bladePaint = Paint()
      ..color = Colors.green[600]!
      ..strokeWidth = 2;
    
    for (double x = 0; x < size.width; x += 10) {
      canvas.drawLine(
        Offset(x, size.height - 30),
        Offset(x, size.height - 35),
        bladePaint,
      );
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
