import 'dart:async';
import 'dart:collection';
import 'package:flutter/foundation.dart';
import 'package:flutter/scheduler.dart';

/// Service for monitoring and optimizing leaderboard performance
class PerformanceMonitor extends ChangeNotifier {
  static const int _maxMetricsHistory = 100;
  static const Duration _performanceReportInterval = Duration(seconds: 30);

  final Queue<PerformanceMetric> _metrics = Queue<PerformanceMetric>();
  final Map<String, Stopwatch> _activeTimers = {};
  final Map<String, List<Duration>> _operationTimes = {};
  
  Timer? _reportTimer;
  final int _frameDropCount = 0;
  int _totalFrames = 0;
  double _averageFps = 60.0;

  /// Initialize performance monitoring
  void initialize() {
    _startFrameMonitoring();
    _startPerformanceReporting();
  }

  /// Start timing an operation
  void startTimer(String operationName) {
    _activeTimers[operationName] = Stopwatch()..start();
  }

  /// Stop timing an operation and record the result
  void stopTimer(String operationName) {
    final stopwatch = _activeTimers.remove(operationName);
    if (stopwatch != null) {
      stopwatch.stop();
      _recordOperationTime(operationName, stopwatch.elapsed);
    }
  }

  /// Record a performance metric
  void recordMetric(String name, double value, {String? unit, Map<String, dynamic>? metadata}) {
    final metric = PerformanceMetric(
      name: name,
      value: value,
      unit: unit ?? 'ms',
      timestamp: DateTime.now(),
      metadata: metadata ?? {},
    );

    _metrics.add(metric);
    
    // Keep only recent metrics
    while (_metrics.length > _maxMetricsHistory) {
      _metrics.removeFirst();
    }

    notifyListeners();
  }

  /// Record memory usage
  void recordMemoryUsage() {
    // This would integrate with platform-specific memory monitoring
    // For now, we'll use a placeholder
    recordMetric('memory_usage', 45.2, unit: 'MB');
  }

  /// Record network request performance
  void recordNetworkRequest(String endpoint, Duration duration, bool success) {
    recordMetric(
      'network_request',
      duration.inMilliseconds.toDouble(),
      metadata: {
        'endpoint': endpoint,
        'success': success,
        'duration_ms': duration.inMilliseconds,
      },
    );
  }

  /// Record widget build performance
  void recordWidgetBuild(String widgetName, Duration buildTime) {
    recordMetric(
      'widget_build',
      buildTime.inMicroseconds.toDouble(),
      unit: 'μs',
      metadata: {
        'widget': widgetName,
        'build_time_us': buildTime.inMicroseconds,
      },
    );
  }

  /// Record animation performance
  void recordAnimationFrame(String animationName, Duration frameTime) {
    recordMetric(
      'animation_frame',
      frameTime.inMicroseconds.toDouble(),
      unit: 'μs',
      metadata: {
        'animation': animationName,
        'frame_time_us': frameTime.inMicroseconds,
      },
    );
  }

  /// Get performance statistics
  PerformanceStats getPerformanceStats() {
    final now = DateTime.now();
    final recentMetrics = _metrics.where(
      (metric) => now.difference(metric.timestamp) < const Duration(minutes: 5)
    ).toList();

    return PerformanceStats(
      averageFps: _averageFps,
      frameDropCount: _frameDropCount,
      totalFrames: _totalFrames,
      recentMetrics: recentMetrics,
      operationAverages: _calculateOperationAverages(),
      memoryUsage: _getLatestMemoryUsage(),
      networkPerformance: _getNetworkPerformance(),
    );
  }

  /// Get performance recommendations
  List<PerformanceRecommendation> getRecommendations() {
    final recommendations = <PerformanceRecommendation>[];
    final stats = getPerformanceStats();

    // FPS recommendations
    if (stats.averageFps < 55) {
      recommendations.add(PerformanceRecommendation(
        type: RecommendationType.fps,
        severity: RecommendationSeverity.high,
        message: 'Low FPS detected (${stats.averageFps.toStringAsFixed(1)}). Consider reducing animation complexity.',
        action: 'Reduce particle count or disable some animations',
      ));
    }

    // Memory recommendations
    if (stats.memoryUsage > 100) {
      recommendations.add(PerformanceRecommendation(
        type: RecommendationType.memory,
        severity: RecommendationSeverity.medium,
        message: 'High memory usage detected (${stats.memoryUsage.toStringAsFixed(1)} MB).',
        action: 'Clear unused caches or reduce image quality',
      ));
    }

    // Network recommendations
    if (stats.networkPerformance.averageLatency > 2000) {
      recommendations.add(PerformanceRecommendation(
        type: RecommendationType.network,
        severity: RecommendationSeverity.medium,
        message: 'Slow network requests detected (${stats.networkPerformance.averageLatency}ms avg).',
        action: 'Enable caching or reduce request frequency',
      ));
    }

    return recommendations;
  }

  /// Enable or disable performance monitoring
  void setMonitoringEnabled(bool enabled) {
    if (enabled) {
      _startFrameMonitoring();
      _startPerformanceReporting();
    } else {
      _reportTimer?.cancel();
      _reportTimer = null;
    }
  }

  // Private methods

  void _recordOperationTime(String operationName, Duration duration) {
    _operationTimes.putIfAbsent(operationName, () => <Duration>[]);
    _operationTimes[operationName]!.add(duration);
    
    // Keep only recent measurements
    if (_operationTimes[operationName]!.length > 50) {
      _operationTimes[operationName]!.removeAt(0);
    }

    recordMetric(
      operationName,
      duration.inMilliseconds.toDouble(),
      metadata: {'operation': operationName},
    );
  }

  void _startFrameMonitoring() {
    SchedulerBinding.instance.addPersistentFrameCallback((timeStamp) {
      _totalFrames++;
      
      // Simple FPS calculation (this could be more sophisticated)
      if (_totalFrames % 60 == 0) {
        _averageFps = 60.0; // Placeholder - would calculate actual FPS
      }
    });
  }

  void _startPerformanceReporting() {
    _reportTimer = Timer.periodic(_performanceReportInterval, (timer) {
      _generatePerformanceReport();
    });
  }

  void _generatePerformanceReport() {
    final stats = getPerformanceStats();
    final recommendations = getRecommendations();

    if (kDebugMode) {
      debugPrint('=== Performance Report ===');
      debugPrint('FPS: ${stats.averageFps.toStringAsFixed(1)}');
      debugPrint('Frame Drops: ${stats.frameDropCount}');
      debugPrint('Memory: ${stats.memoryUsage.toStringAsFixed(1)} MB');
      debugPrint('Network Avg: ${stats.networkPerformance.averageLatency}ms');
      
      if (recommendations.isNotEmpty) {
        debugPrint('Recommendations:');
        for (final rec in recommendations) {
          debugPrint('  - ${rec.message}');
        }
      }
      debugPrint('========================');
    }
  }

  Map<String, double> _calculateOperationAverages() {
    final averages = <String, double>{};
    
    for (final entry in _operationTimes.entries) {
      if (entry.value.isNotEmpty) {
        final totalMs = entry.value.fold<int>(
          0, (sum, duration) => sum + duration.inMilliseconds
        );
        averages[entry.key] = totalMs / entry.value.length;
      }
    }
    
    return averages;
  }

  double _getLatestMemoryUsage() {
    final memoryMetrics = _metrics.where((m) => m.name == 'memory_usage');
    return memoryMetrics.isNotEmpty ? memoryMetrics.last.value : 0.0;
  }

  NetworkPerformance _getNetworkPerformance() {
    final networkMetrics = _metrics.where((m) => m.name == 'network_request').toList();
    
    if (networkMetrics.isEmpty) {
      return NetworkPerformance(averageLatency: 0, successRate: 1.0, requestCount: 0);
    }

    final totalLatency = networkMetrics.fold<double>(0, (sum, metric) => sum + metric.value);
    final successCount = networkMetrics.where((m) => m.metadata['success'] == true).length;
    
    return NetworkPerformance(
      averageLatency: totalLatency / networkMetrics.length,
      successRate: successCount / networkMetrics.length,
      requestCount: networkMetrics.length,
    );
  }

  @override
  void dispose() {
    _reportTimer?.cancel();
    _activeTimers.clear();
    _operationTimes.clear();
    _metrics.clear();
    super.dispose();
  }
}

/// Performance metric data class
class PerformanceMetric {
  final String name;
  final double value;
  final String unit;
  final DateTime timestamp;
  final Map<String, dynamic> metadata;

  PerformanceMetric({
    required this.name,
    required this.value,
    required this.unit,
    required this.timestamp,
    required this.metadata,
  });
}

/// Performance statistics data class
class PerformanceStats {
  final double averageFps;
  final int frameDropCount;
  final int totalFrames;
  final List<PerformanceMetric> recentMetrics;
  final Map<String, double> operationAverages;
  final double memoryUsage;
  final NetworkPerformance networkPerformance;

  PerformanceStats({
    required this.averageFps,
    required this.frameDropCount,
    required this.totalFrames,
    required this.recentMetrics,
    required this.operationAverages,
    required this.memoryUsage,
    required this.networkPerformance,
  });
}

/// Network performance data class
class NetworkPerformance {
  final double averageLatency;
  final double successRate;
  final int requestCount;

  NetworkPerformance({
    required this.averageLatency,
    required this.successRate,
    required this.requestCount,
  });
}

/// Performance recommendation data class
class PerformanceRecommendation {
  final RecommendationType type;
  final RecommendationSeverity severity;
  final String message;
  final String action;

  PerformanceRecommendation({
    required this.type,
    required this.severity,
    required this.message,
    required this.action,
  });
}

enum RecommendationType { fps, memory, network, animation, widget }
enum RecommendationSeverity { low, medium, high, critical }
