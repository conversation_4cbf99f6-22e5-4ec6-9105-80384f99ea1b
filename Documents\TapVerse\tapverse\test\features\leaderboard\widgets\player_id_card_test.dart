import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tapverse/features/leaderboard/widgets/player_id_card.dart';
import 'package:tapverse/features/leaderboard/providers/leaderboard_providers.dart';
import 'package:tapverse/core/models/user_model.dart';

// Mock providers for testing
final mockUserProfileProvider = StateProvider.family<AsyncValue<UserModel?>, String>((ref, playerId) {
  return AsyncValue.data(UserModel(
    uid: playerId,
    displayName: 'Test Player',
    email: '<EMAIL>',
    tokens: 1000,
    totalScore: 5000,
    gamesPlayed: 10,
    equippedBanner: null,
  ));
});

final mockPerformanceMonitorProvider = Provider<dynamic>((ref) {
  return MockPerformanceMonitor();
});

class MockPerformanceMonitor {
  void recordWidgetBuild(String widgetName, Duration buildTime) {}
}

void main() {
  group('PlayerIDCard Widget Tests', () {
    late Widget testWidget;

    setUp(() {
      testWidget = ProviderScope(
        overrides: [
          userProfileProvider.overrideWith(mockUserProfileProvider.call),
          performanceMonitorProvider.overrideWith(mockPerformanceMonitorProvider),
        ],
        child: MaterialApp(
          home: Scaffold(
            body: PlayerIDCard(
              playerId: 'test_player',
              displayName: 'Test Player',
              score: 1500,
              rank: 1,
              isCurrentUser: false,
              isMiniCard: false,
            ),
          ),
        ),
      );
    });

    group('Basic Rendering', () {
      testWidgets('should render player card with basic information', (WidgetTester tester) async {
        // Act
        await tester.pumpWidget(testWidget);
        await tester.pumpAndSettle();

        // Assert
        expect(find.text('Test Player'), findsOneWidget);
        expect(find.text('1,500'), findsOneWidget); // Formatted score
        expect(find.text('#1'), findsOneWidget); // Rank
      });

      testWidgets('should show loading state initially', (WidgetTester tester) async {
        // Arrange
        final loadingWidget = ProviderScope(
          overrides: [
            userProfileProvider.overrideWith((ref, playerId) => 
              StateProvider.family<AsyncValue<UserModel?>, String>((ref, id) => 
                const AsyncValue.loading())),
            performanceMonitorProvider.overrideWith(mockPerformanceMonitorProvider),
          ],
          child: MaterialApp(
            home: Scaffold(
              body: PlayerIDCard(
                playerId: 'test_player',
                displayName: 'Test Player',
                score: 1500,
                rank: 1,
                isCurrentUser: false,
                isMiniCard: false,
              ),
            ),
          ),
        );

        // Act
        await tester.pumpWidget(loadingWidget);

        // Assert
        expect(find.byType(CircularProgressIndicator), findsOneWidget);
      });

      testWidgets('should handle error state gracefully', (WidgetTester tester) async {
        // Arrange
        final errorWidget = ProviderScope(
          overrides: [
            userProfileProvider.overrideWith((ref, playerId) => 
              StateProvider.family<AsyncValue<UserModel?>, String>((ref, id) => 
                AsyncValue.error('Test error', StackTrace.current))),
            performanceMonitorProvider.overrideWith(mockPerformanceMonitorProvider),
          ],
          child: MaterialApp(
            home: Scaffold(
              body: PlayerIDCard(
                playerId: 'test_player',
                displayName: 'Test Player',
                score: 1500,
                rank: 1,
                isCurrentUser: false,
                isMiniCard: false,
              ),
            ),
          ),
        );

        // Act
        await tester.pumpWidget(errorWidget);
        await tester.pumpAndSettle();

        // Assert - Should still show basic card without profile data
        expect(find.text('Test Player'), findsOneWidget);
        expect(find.text('1,500'), findsOneWidget);
      });
    });

    group('Mini Card Variant', () {
      testWidgets('should render mini card correctly', (WidgetTester tester) async {
        // Arrange
        final miniCardWidget = ProviderScope(
          overrides: [
            userProfileProvider.overrideWith(mockUserProfileProvider.call),
            performanceMonitorProvider.overrideWith(mockPerformanceMonitorProvider),
          ],
          child: MaterialApp(
            home: Scaffold(
              body: PlayerIDCard(
                playerId: 'test_player',
                displayName: 'Test Player',
                score: 1500,
                rank: 5,
                isCurrentUser: false,
                isMiniCard: true,
              ),
            ),
          ),
        );

        // Act
        await tester.pumpWidget(miniCardWidget);
        await tester.pumpAndSettle();

        // Assert
        expect(find.text('Test Player'), findsOneWidget);
        expect(find.text('1,500'), findsOneWidget);
        expect(find.text('5'), findsOneWidget); // Rank without #
      });
    });

    group('Current User Highlighting', () {
      testWidgets('should highlight current user card', (WidgetTester tester) async {
        // Arrange
        final currentUserWidget = ProviderScope(
          overrides: [
            userProfileProvider.overrideWith(mockUserProfileProvider.call),
            performanceMonitorProvider.overrideWith(mockPerformanceMonitorProvider),
          ],
          child: MaterialApp(
            home: Scaffold(
              body: PlayerIDCard(
                playerId: 'test_player',
                displayName: 'Test Player',
                score: 1500,
                rank: 1,
                isCurrentUser: true,
                isMiniCard: false,
              ),
            ),
          ),
        );

        // Act
        await tester.pumpWidget(currentUserWidget);
        await tester.pumpAndSettle();

        // Assert
        expect(find.text('Test Player'), findsOneWidget);
        // Additional assertions for highlighting would depend on implementation
      });
    });

    group('Rank Display', () {
      testWidgets('should display crown for top 3 ranks', (WidgetTester tester) async {
        for (int rank = 1; rank <= 3; rank++) {
          // Arrange
          final crownWidget = ProviderScope(
            overrides: [
              userProfileProvider.overrideWith(mockUserProfileProvider.call),
              performanceMonitorProvider.overrideWith(mockPerformanceMonitorProvider),
            ],
            child: MaterialApp(
              home: Scaffold(
                body: PlayerIDCard(
                  playerId: 'test_player',
                  displayName: 'Test Player',
                  score: 1500,
                  rank: rank,
                  isCurrentUser: false,
                  isMiniCard: false,
                ),
              ),
            ),
          );

          // Act
          await tester.pumpWidget(crownWidget);
          await tester.pumpAndSettle();

          // Assert - Should find crown icon for top 3
          expect(find.text('#$rank'), findsOneWidget);
        }
      });

      testWidgets('should display numeric rank for ranks > 3', (WidgetTester tester) async {
        // Arrange
        final numericRankWidget = ProviderScope(
          overrides: [
            userProfileProvider.overrideWith(mockUserProfileProvider.call),
            performanceMonitorProvider.overrideWith(mockPerformanceMonitorProvider),
          ],
          child: MaterialApp(
            home: Scaffold(
              body: PlayerIDCard(
                playerId: 'test_player',
                displayName: 'Test Player',
                score: 1500,
                rank: 10,
                isCurrentUser: false,
                isMiniCard: false,
              ),
            ),
          ),
        );

        // Act
        await tester.pumpWidget(numericRankWidget);
        await tester.pumpAndSettle();

        // Assert
        expect(find.text('#10'), findsOneWidget);
      });
    });

    group('Score Formatting', () {
      testWidgets('should format large scores correctly', (WidgetTester tester) async {
        final testCases = [
          {'score': 1500, 'expected': '1,500'},
          {'score': 15000, 'expected': '15.0K'},
          {'score': 1500000, 'expected': '1.5M'},
          {'score': **********, 'expected': '1.5B'},
        ];

        for (final testCase in testCases) {
          // Arrange
          final scoreWidget = ProviderScope(
            overrides: [
              userProfileProvider.overrideWith(mockUserProfileProvider.call),
              performanceMonitorProvider.overrideWith(mockPerformanceMonitorProvider),
            ],
            child: MaterialApp(
              home: Scaffold(
                body: PlayerIDCard(
                  playerId: 'test_player',
                  displayName: 'Test Player',
                  score: testCase['score'] as int,
                  rank: 1,
                  isCurrentUser: false,
                  isMiniCard: false,
                ),
              ),
            ),
          );

          // Act
          await tester.pumpWidget(scoreWidget);
          await tester.pumpAndSettle();

          // Assert
          expect(find.text(testCase['expected'] as String), findsOneWidget,
              reason: 'Score ${testCase['score']} should format to ${testCase['expected']}');
        }
      });
    });

    group('Interaction', () {
      testWidgets('should handle tap events', (WidgetTester tester) async {
        // Arrange
        bool tapped = false;
        final interactiveWidget = ProviderScope(
          overrides: [
            userProfileProvider.overrideWith(mockUserProfileProvider.call),
            performanceMonitorProvider.overrideWith(mockPerformanceMonitorProvider),
          ],
          child: MaterialApp(
            home: Scaffold(
              body: PlayerIDCard(
                playerId: 'test_player',
                displayName: 'Test Player',
                score: 1500,
                rank: 1,
                isCurrentUser: false,
                isMiniCard: false,
                onTap: () => tapped = true,
              ),
            ),
          ),
        );

        // Act
        await tester.pumpWidget(interactiveWidget);
        await tester.pumpAndSettle();
        await tester.tap(find.byType(PlayerIDCard));

        // Assert
        expect(tapped, isTrue);
      });

      testWidgets('should handle challenge button tap', (WidgetTester tester) async {
        // Arrange
        bool challenged = false;
        final challengeWidget = ProviderScope(
          overrides: [
            userProfileProvider.overrideWith(mockUserProfileProvider.call),
            performanceMonitorProvider.overrideWith(mockPerformanceMonitorProvider),
          ],
          child: MaterialApp(
            home: Scaffold(
              body: PlayerIDCard(
                playerId: 'test_player',
                displayName: 'Test Player',
                score: 1500,
                rank: 1,
                isCurrentUser: false,
                isMiniCard: false,
                onChallenge: () => challenged = true,
              ),
            ),
          ),
        );

        // Act
        await tester.pumpWidget(challengeWidget);
        await tester.pumpAndSettle();
        
        // Find and tap challenge button if it exists
        final challengeButton = find.text('Challenge');
        if (challengeButton.evaluate().isNotEmpty) {
          await tester.tap(challengeButton);
          expect(challenged, isTrue);
        }
      });
    });

    group('Accessibility', () {
      testWidgets('should have proper accessibility labels', (WidgetTester tester) async {
        // Act
        await tester.pumpWidget(testWidget);
        await tester.pumpAndSettle();

        // Assert - Check for semantic labels
        expect(find.bySemanticsLabel('Player rank 1'), findsOneWidget);
        expect(find.bySemanticsLabel('Score 1,500'), findsOneWidget);
      });

      testWidgets('should support screen readers', (WidgetTester tester) async {
        // Act
        await tester.pumpWidget(testWidget);
        await tester.pumpAndSettle();

        // Assert - Verify semantic structure
        final semantics = tester.getSemantics(find.byType(PlayerIDCard));
        expect(semantics.hasFlag(SemanticsFlag.isButton), isTrue);
      });
    });

    group('Performance', () {
      testWidgets('should render quickly', (WidgetTester tester) async {
        // Arrange
        final stopwatch = Stopwatch()..start();

        // Act
        await tester.pumpWidget(testWidget);
        await tester.pumpAndSettle();
        
        stopwatch.stop();

        // Assert
        expect(stopwatch.elapsedMilliseconds, lessThan(100)); // Should render in under 100ms
      });

      testWidgets('should handle rapid rebuilds efficiently', (WidgetTester tester) async {
        // Act
        await tester.pumpWidget(testWidget);
        
        // Trigger multiple rebuilds
        for (int i = 0; i < 10; i++) {
          await tester.pump();
        }

        // Assert - Should not throw or crash
        expect(find.byType(PlayerIDCard), findsOneWidget);
      });
    });
  });
}
