import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/game_state.dart';
import '../providers/app_providers.dart';
import 'sound_service.dart';
import 'vibration_service.dart';

/// Game event types for feedback
enum GameEvent {
  gameStart,
  gameEnd,
  gamePause,
  gameResume,
  scoreIncrease,
  levelUp,
  lifeGained,
  lifeLost,
  powerUpCollected,
  obstacleHit,
  targetHit,
  perfectAction,
  comboBreak,
  timeWarning,
  newHighScore,
  achievement,
  buttonPress,
  menuNavigation,
}

/// Feedback intensity levels
enum FeedbackIntensity {
  subtle,
  normal,
  strong,
  intense,
}

/// Feedback configuration for different game events
class FeedbackConfig {
  final SoundType? soundType;
  final VibrationType? vibrationType;
  final FeedbackIntensity intensity;
  final Duration? delay;
  final bool enabled;

  const FeedbackConfig({
    this.soundType,
    this.vibrationType,
    this.intensity = FeedbackIntensity.normal,
    this.delay,
    this.enabled = true,
  });

  FeedbackConfig copyWith({
    SoundType? soundType,
    VibrationType? vibrationType,
    FeedbackIntensity? intensity,
    Duration? delay,
    bool? enabled,
  }) {
    return FeedbackConfig(
      soundType: soundType ?? this.soundType,
      vibrationType: vibrationType ?? this.vibrationType,
      intensity: intensity ?? this.intensity,
      delay: delay ?? this.delay,
      enabled: enabled ?? this.enabled,
    );
  }
}

/// Service for managing game feedback (sound and vibration)
class GameFeedbackService {
  final SoundService _soundService;
  final VibrationService _vibrationService;
  
  // Default feedback configurations for different events
  static const Map<GameEvent, FeedbackConfig> _defaultConfigs = {
    GameEvent.gameStart: FeedbackConfig(
      soundType: SoundType.gameStart,
      vibrationType: VibrationType.medium,
      intensity: FeedbackIntensity.normal,
    ),
    GameEvent.gameEnd: FeedbackConfig(
      soundType: SoundType.gameOver,
      vibrationType: VibrationType.heavy,
      intensity: FeedbackIntensity.strong,
    ),
    GameEvent.gamePause: FeedbackConfig(
      soundType: SoundType.buttonClick,
      vibrationType: VibrationType.light,
      intensity: FeedbackIntensity.subtle,
    ),
    GameEvent.gameResume: FeedbackConfig(
      soundType: SoundType.buttonClick,
      vibrationType: VibrationType.light,
      intensity: FeedbackIntensity.subtle,
    ),
    GameEvent.scoreIncrease: FeedbackConfig(
      soundType: SoundType.score,
      vibrationType: VibrationType.success,
      intensity: FeedbackIntensity.normal,
    ),
    GameEvent.levelUp: FeedbackConfig(
      soundType: SoundType.powerUp,
      vibrationType: VibrationType.success,
      intensity: FeedbackIntensity.strong,
    ),
    GameEvent.lifeGained: FeedbackConfig(
      soundType: SoundType.powerUp,
      vibrationType: VibrationType.success,
      intensity: FeedbackIntensity.normal,
    ),
    GameEvent.lifeLost: FeedbackConfig(
      soundType: SoundType.defeat,
      vibrationType: VibrationType.warning,
      intensity: FeedbackIntensity.strong,
    ),
    GameEvent.powerUpCollected: FeedbackConfig(
      soundType: SoundType.powerUp,
      vibrationType: VibrationType.success,
      intensity: FeedbackIntensity.normal,
    ),
    GameEvent.obstacleHit: FeedbackConfig(
      soundType: SoundType.defeat,
      vibrationType: VibrationType.warning,
      intensity: FeedbackIntensity.strong,
    ),
    GameEvent.targetHit: FeedbackConfig(
      soundType: SoundType.score,
      vibrationType: VibrationType.light,
      intensity: FeedbackIntensity.normal,
    ),
    GameEvent.perfectAction: FeedbackConfig(
      soundType: SoundType.victory,
      vibrationType: VibrationType.success,
      intensity: FeedbackIntensity.strong,
    ),
    GameEvent.comboBreak: FeedbackConfig(
      soundType: SoundType.defeat,
      vibrationType: VibrationType.error,
      intensity: FeedbackIntensity.normal,
    ),
    GameEvent.timeWarning: FeedbackConfig(
      soundType: SoundType.buttonClick,
      vibrationType: VibrationType.warning,
      intensity: FeedbackIntensity.normal,
    ),
    GameEvent.newHighScore: FeedbackConfig(
      soundType: SoundType.victory,
      vibrationType: VibrationType.success,
      intensity: FeedbackIntensity.intense,
    ),
    GameEvent.achievement: FeedbackConfig(
      soundType: SoundType.victory,
      vibrationType: VibrationType.success,
      intensity: FeedbackIntensity.strong,
    ),
    GameEvent.buttonPress: FeedbackConfig(
      soundType: SoundType.buttonClick,
      vibrationType: VibrationType.selection,
      intensity: FeedbackIntensity.subtle,
    ),
    GameEvent.menuNavigation: FeedbackConfig(
      soundType: SoundType.buttonClick,
      vibrationType: VibrationType.selection,
      intensity: FeedbackIntensity.subtle,
    ),
  };

  final Map<GameEvent, FeedbackConfig> _customConfigs = {};

  GameFeedbackService({
    required SoundService soundService,
    required VibrationService vibrationService,
  }) : _soundService = soundService,
       _vibrationService = vibrationService;

  /// Trigger feedback for a game event
  Future<void> triggerFeedback(GameEvent event, {FeedbackConfig? customConfig}) async {
    final config = customConfig ?? _customConfigs[event] ?? _defaultConfigs[event];
    
    if (config == null || !config.enabled) return;

    // Apply delay if specified
    if (config.delay != null) {
      await Future.delayed(config.delay!);
    }

    // Trigger sound feedback
    if (config.soundType != null) {
      await _triggerSound(config.soundType!, config.intensity);
    }

    // Trigger vibration feedback
    if (config.vibrationType != null) {
      await _triggerVibration(config.vibrationType!, config.intensity);
    }
  }

  /// Trigger sound with intensity adjustment
  Future<void> _triggerSound(SoundType soundType, FeedbackIntensity intensity) async {
    // Adjust volume based on intensity
    final originalVolume = _soundService.soundVolume;
    double adjustedVolume = originalVolume;

    switch (intensity) {
      case FeedbackIntensity.subtle:
        adjustedVolume = originalVolume * 0.5;
        break;
      case FeedbackIntensity.normal:
        adjustedVolume = originalVolume;
        break;
      case FeedbackIntensity.strong:
        adjustedVolume = originalVolume * 1.2;
        break;
      case FeedbackIntensity.intense:
        adjustedVolume = originalVolume * 1.5;
        break;
    }

    // Temporarily adjust volume
    await _soundService.setSoundVolume(adjustedVolume.clamp(0.0, 1.0));
    await _soundService.playSound(soundType);
    
    // Restore original volume
    await _soundService.setSoundVolume(originalVolume);
  }

  /// Trigger vibration with intensity adjustment
  Future<void> _triggerVibration(VibrationType vibrationType, FeedbackIntensity intensity) async {
    switch (intensity) {
      case FeedbackIntensity.subtle:
        // Use lighter vibration
        if (vibrationType == VibrationType.heavy) {
          await _vibrationService.vibrate(VibrationType.medium);
        } else if (vibrationType == VibrationType.medium) {
          await _vibrationService.vibrate(VibrationType.light);
        } else {
          await _vibrationService.vibrate(vibrationType);
        }
        break;
      case FeedbackIntensity.normal:
        await _vibrationService.vibrate(vibrationType);
        break;
      case FeedbackIntensity.strong:
        // Use stronger vibration
        if (vibrationType == VibrationType.light) {
          await _vibrationService.vibrate(VibrationType.medium);
        } else if (vibrationType == VibrationType.medium) {
          await _vibrationService.vibrate(VibrationType.heavy);
        } else {
          await _vibrationService.vibrate(vibrationType);
        }
        break;
      case FeedbackIntensity.intense:
        // Use custom intense vibration
        await _vibrationService.customVibration(duration: 300, amplitude: 255);
        break;
    }
  }

  /// Set custom feedback configuration for an event
  void setFeedbackConfig(GameEvent event, FeedbackConfig config) {
    _customConfigs[event] = config;
  }

  /// Get feedback configuration for an event
  FeedbackConfig getFeedbackConfig(GameEvent event) {
    return _customConfigs[event] ?? _defaultConfigs[event] ?? const FeedbackConfig(enabled: false);
  }

  /// Reset all custom configurations
  void resetConfigurations() {
    _customConfigs.clear();
  }

  /// Disable feedback for an event
  void disableFeedback(GameEvent event) {
    _customConfigs[event] = const FeedbackConfig(enabled: false);
  }

  /// Enable feedback for an event
  void enableFeedback(GameEvent event) {
    _customConfigs[event] = _defaultConfigs[event] ?? const FeedbackConfig();
  }

  /// Trigger combo feedback (escalating intensity)
  Future<void> triggerComboFeedback(int comboCount) async {
    FeedbackIntensity intensity;
    
    if (comboCount < 5) {
      intensity = FeedbackIntensity.subtle;
    } else if (comboCount < 10) {
      intensity = FeedbackIntensity.normal;
    } else if (comboCount < 20) {
      intensity = FeedbackIntensity.strong;
    } else {
      intensity = FeedbackIntensity.intense;
    }

    await triggerFeedback(
      GameEvent.scoreIncrease,
      customConfig: FeedbackConfig(
        soundType: SoundType.score,
        vibrationType: VibrationType.success,
        intensity: intensity,
      ),
    );
  }

  /// Trigger countdown feedback
  Future<void> triggerCountdownFeedback(int secondsRemaining) async {
    if (secondsRemaining <= 3 && secondsRemaining > 0) {
      final intensity = secondsRemaining == 1 
          ? FeedbackIntensity.intense 
          : FeedbackIntensity.strong;
      
      await triggerFeedback(
        GameEvent.timeWarning,
        customConfig: FeedbackConfig(
          soundType: SoundType.buttonClick,
          vibrationType: VibrationType.warning,
          intensity: intensity,
        ),
      );
    }
  }

  /// Trigger progressive difficulty feedback
  Future<void> triggerDifficultyFeedback(GameDifficulty difficulty) async {
    FeedbackIntensity intensity;
    
    switch (difficulty) {
      case GameDifficulty.easy:
        intensity = FeedbackIntensity.subtle;
        break;
      case GameDifficulty.medium:
        intensity = FeedbackIntensity.normal;
        break;
      case GameDifficulty.hard:
        intensity = FeedbackIntensity.strong;
        break;
      case GameDifficulty.expert:
        intensity = FeedbackIntensity.intense;
        break;
    }

    await triggerFeedback(
      GameEvent.levelUp,
      customConfig: FeedbackConfig(
        soundType: SoundType.powerUp,
        vibrationType: VibrationType.success,
        intensity: intensity,
      ),
    );
  }
}

/// Provider for game feedback service
final gameFeedbackServiceProvider = Provider<GameFeedbackService>((ref) {
  return GameFeedbackService(
    soundService: ref.watch(soundServiceProvider),
    vibrationService: ref.watch(vibrationServiceProvider),
  );
});
