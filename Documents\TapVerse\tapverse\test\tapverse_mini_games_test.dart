import 'package:flutter_test/flutter_test.dart';
import 'package:tapverse/tapverse/registry/game_registry.dart';
import 'package:tapverse/tapverse/core/analytics.dart';
import 'package:tapverse/tapverse/core/difficulty.dart';
import 'package:tapverse/tapverse/core/economy.dart';

void main() {
  group('TapVerse Mini-Game Platform Tests', () {
    setUpAll(() {
      // Register all games for testing
      TapVerseGameRegistration.registerAllGames();
      
      // Set up test analytics sink
      Analytics.setSink((event, data) {
        print('Test Analytics: $event - $data');
      });
    });

    group('Game Registry Tests', () {
      test('should register and create games', () {
        // Test that games are properly registered
        expect(GameRegistry.isRegistered('swish_shot'), isTrue);
        expect(GameRegistry.isRegistered('tappy_footy'), isTrue);
        expect(GameRegistry.isRegistered('rebounder'), isTrue);
        expect(GameRegistry.isRegistered('laser_jump'), isTrue);
        expect(GameRegistry.isRegistered('coin_climb'), isTrue);
        expect(GameRegistry.isRegistered('bubble_blast'), isTrue);
        expect(GameRegistry.isRegistered('flip_shot'), isTrue);
        expect(GameRegistry.isRegistered('pixel_dive'), isTrue);
        expect(GameRegistry.isRegistered('glow_runner'), isTrue);
        expect(GameRegistry.isRegistered('shadow_sprint'), isTrue);
        expect(GameRegistry.isRegistered('shape_shift'), isTrue);
        expect(GameRegistry.isRegistered('rocket_rider'), isTrue);
        expect(GameRegistry.isRegistered('tug_tap'), isTrue);
        expect(GameRegistry.isRegistered('tile_tide'), isTrue);
        expect(GameRegistry.isRegistered('zap_grid'), isTrue);
        
        // Test game creation (simplified test)
        expect(() => GameRegistry.create('swish_shot', seed: 12345), returnsNormally);
      });

      test('should provide game metadata', () {
        final metadata = GameRegistry.getMetadata('swish_shot');
        expect(metadata, isNotNull);
        expect(metadata!.name, equals('SwishShot'));
        expect(metadata.category, equals(GameCategory.sports));
        expect(metadata.difficulty, equals(GameDifficulty.medium));
      });

      test('should filter games by category', () {
        final sportsGames = GameRegistry.getGamesByCategory(GameCategory.sports);
        expect(sportsGames, contains('swish_shot'));
        expect(sportsGames, contains('tappy_footy'));
        
        final platformGames = GameRegistry.getGamesByCategory(GameCategory.platform);
        expect(platformGames, contains('laser_jump'));
        expect(platformGames, contains('coin_climb'));
      });

      test('should get random games', () {
        final randomGame = GameRegistry.getRandomGameId(seed: 42);
        expect(randomGame, isNotNull);
        expect(GameRegistry.isRegistered(randomGame), isTrue);
        
        // Should be deterministic with same seed
        final randomGame2 = GameRegistry.getRandomGameId(seed: 42);
        expect(randomGame2, equals(randomGame));
      });
    });

    group('Difficulty System Tests', () {
      test('should calculate linear curve values', () {
        final curve = CurveParam(start: 100, max: 300, perMinute: 50);
        
        expect(curve.value(Duration.zero), equals(100));
        expect(curve.value(Duration(minutes: 1)), equals(150));
        expect(curve.value(Duration(minutes: 2)), equals(200));
        expect(curve.value(Duration(minutes: 4)), equals(300)); // Capped at max
        expect(curve.value(Duration(minutes: 5)), equals(300)); // Still capped
      });

      test('should manage difficulty parameters', () {
        final manager = DifficultyManager();
        manager.addParameter('speed', CurveParam(start: 100, max: 200, perMinute: 50));
        manager.addParameter('spawn_rate', CurveParam(start: 1.0, max: 0.5, perMinute: -0.25));
        
        manager.start();
        
        expect(manager.getValue('speed'), equals(100));
        expect(manager.getValue('spawn_rate'), equals(1.0));
        
        // Simulate 1 minute elapsed
        manager.pause();
        manager.resume();
        
        // Values should change based on curves (this is a simplified test)
        expect(manager.getValue('speed'), greaterThanOrEqualTo(100));
        expect(manager.getValue('spawn_rate'), lessThanOrEqualTo(1.0));
      });
    });

    group('Economy System Tests', () {
      test('should calculate default rewards', () {
        final policy = DefaultRewardPolicy();
        
        final reward = policy.onFinish(
          score: 100,
          duration: Duration(seconds: 60),
          success: true,
        );
        
        expect(reward.tokens, greaterThan(0));
        expect(reward.xp, equals(100)); // XP equals score by default
      });

      test('should apply arcade reward policy', () {
        final policy = ArcadeRewardPolicy();
        
        final reward = policy.onFinish(
          score: 200,
          duration: Duration(seconds: 120),
          success: true,
          combo: 10,
        );
        
        expect(reward.tokens, greaterThan(0));
        expect(reward.xp, equals(200));
      });

      test('should calculate survival rewards', () {
        final policy = SurvivalRewardPolicy();
        
        final reward = policy.onFinish(
          score: 50,
          duration: Duration(seconds: 90), // 1.5 minutes
          success: true,
        );
        
        expect(reward.tokens, greaterThan(0));
        // Should get milestone bonus for 60+ seconds
        expect(reward.tokens, greaterThan(10));
      });
    });

    group('Analytics System Tests', () {
      test('should log events with data', () {
        var capturedEvent = '';
        var capturedData = <String, dynamic>{};
        
        Analytics.setSink((event, data) {
          capturedEvent = event;
          capturedData = data;
        });
        
        Analytics.log('test_event', {'key': 'value', 'number': 42});
        
        expect(capturedEvent, equals('test_event'));
        expect(capturedData['key'], equals('value'));
        expect(capturedData['number'], equals(42));
      });

      test('should create consistent analytics data', () {
        final gameStartData = AnalyticsData.gameStart(
          gameMode: 'test_game',
          sessionSeed: 12345,
          difficulty: 'medium',
        );
        
        expect(gameStartData['mode'], equals('test_game'));
        expect(gameStartData['session_seed'], equals(12345));
        expect(gameStartData['difficulty'], equals('medium'));
        expect(gameStartData['timestamp'], isA<int>());
      });
    });

    group('Game Session Tests', () {
      test('should create and manage sessions', () {
        final session = SessionManager.createSession('swish_shot');

        expect(session.gameId, equals('swish_shot'));
        expect(session.sessionSeed, isA<int>());
        expect(session.startTime, isA<DateTime>());
        expect(session.hasGameInstance, isFalse);

        // Clean up
        session.dispose();
        expect(session.hasGameInstance, isFalse);
      });

      test('should track active sessions', () {
        final initialCount = SessionManager.activeSessionCount;

        SessionManager.createSession('swish_shot');
        SessionManager.createSession('tappy_footy');

        expect(SessionManager.activeSessionCount, equals(initialCount + 2));

        SessionManager.clearAllSessions();
        expect(SessionManager.activeSessionCount, equals(0));
      });
    });
  });
}
