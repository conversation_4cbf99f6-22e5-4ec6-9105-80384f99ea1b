import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../providers/app_providers.dart';

enum GameType {
  // Implemented Games (Legacy Architecture)
  swishShot,
  tappyFooty,
  rebounder,
  spikeLoop,
  flapster,
  smashWall,
  astroZap,
  wriggle,
  hueDrop,
  dashRush,
  beatTap,
  boomSweep,
  fruitSnag,
  tileTwist,
  dartDash,
  dropTarget,
  popBurst,
  stackify,

  // New Games (MiniGameBase Architecture)
  laserJump,
  coinClimb,
  flipShot,
  pixelDive,
  glowRunner,
  bubbleBlast,
  shadowSprint,
  shapeShift,
  rocketRider,
  tugTap,
  tileTide,
  zapGrid,

  // Future Games (placeholders)
  alienSweep,
  trioMatch,
  skyHop,
  nanoGolf,
  bullseye,
}

enum CasinoGameType {
  spinWheel,
  highOrLow,
  luckyDarts,
  rollNRisk,
  mysteryMultiplier,
}

class GameInfo {
  final GameType type;
  final String id;
  final String name;
  final String description;
  final IconData icon;
  final Color primaryColor;
  final Color secondaryColor;
  final int difficulty; // 1-5 scale
  final List<String> tags;

  const GameInfo({
    required this.type,
    required this.id,
    required this.name,
    required this.description,
    required this.icon,
    required this.primaryColor,
    required this.secondaryColor,
    required this.difficulty,
    required this.tags,
  });
}

class CasinoGameInfo {
  final CasinoGameType type;
  final String id;
  final String name;
  final String description;
  final IconData icon;
  final Color primaryColor;
  final Color secondaryColor;
  final int entryCost; // Token cost to play
  final String riskLevel; // Low, Medium, High
  final List<String> tags;

  const CasinoGameInfo({
    required this.type,
    required this.id,
    required this.name,
    required this.description,
    required this.icon,
    required this.primaryColor,
    required this.secondaryColor,
    required this.entryCost,
    required this.riskLevel,
    required this.tags,
  });
}

class GameNavigationService {
  static final GameNavigationService _instance = GameNavigationService._internal();
  factory GameNavigationService() => _instance;
  GameNavigationService._internal();

  static const Map<GameType, GameInfo> _gameInfoMap = {
    GameType.swishShot: GameInfo(
      type: GameType.swishShot,
      id: 'swish_shot',
      name: 'SwishShot',
      description: 'Flick the basketball into the moving hoop!',
      icon: Icons.sports_basketball,
      primaryColor: Colors.orange,
      secondaryColor: Colors.deepOrange,
      difficulty: 2,
      tags: ['sports', 'arcade', 'physics'],
    ),
    GameType.tappyFooty: GameInfo(
      type: GameType.tappyFooty,
      id: 'tappy_footy',
      name: 'TappyFooty',
      description: 'Keep the soccer ball bouncing!',
      icon: Icons.sports_soccer,
      primaryColor: Colors.green,
      secondaryColor: Colors.lightGreen,
      difficulty: 1,
      tags: ['sports', 'arcade', 'tap'],
    ),
    GameType.rebounder: GameInfo(
      type: GameType.rebounder,
      id: 'rebounder',
      name: 'Rebounder',
      description: 'Paddle ball game with increasing speed',
      icon: Icons.sports_tennis,
      primaryColor: Colors.cyan,
      secondaryColor: Colors.lightBlue,
      difficulty: 2,
      tags: ['classic', 'arcade', 'paddle'],
    ),
    GameType.spikeLoop: GameInfo(
      type: GameType.spikeLoop,
      id: 'spike_loop',
      name: 'SpikeLoop',
      description: 'Volleyball spike and keep it bouncing!',
      icon: Icons.sports_volleyball,
      primaryColor: Colors.blue,
      secondaryColor: Colors.lightBlue,
      difficulty: 2,
      tags: ['sports', 'volleyball', 'tap'],
    ),
    GameType.flapster: GameInfo(
      type: GameType.flapster,
      id: 'flapster',
      name: 'Flapster',
      description: 'Tap to fly through pipe obstacles',
      icon: Icons.flight,
      primaryColor: Colors.yellow,
      secondaryColor: Colors.amber,
      difficulty: 4,
      tags: ['arcade', 'endless', 'challenging'],
    ),
    GameType.smashWall: GameInfo(
      type: GameType.smashWall,
      id: 'smash_wall',
      name: 'SmashWall',
      description: 'Break colorful bricks with the ball',
      icon: Icons.sports_baseball,
      primaryColor: Colors.red,
      secondaryColor: Colors.redAccent,
      difficulty: 2,
      tags: ['classic', 'arcade', 'physics'],
    ),
    GameType.astroZap: GameInfo(
      type: GameType.astroZap,
      id: 'astro_zap',
      name: 'AstroZap',
      description: 'Shoot asteroids in space!',
      icon: Icons.rocket_launch,
      primaryColor: Colors.purple,
      secondaryColor: Colors.deepPurple,
      difficulty: 3,
      tags: ['shooter', 'space', 'arcade'],
    ),
    GameType.wriggle: GameInfo(
      type: GameType.wriggle,
      id: 'wriggle',
      name: 'Wriggle',
      description: 'Snake game - eat and grow longer!',
      icon: Icons.pest_control,
      primaryColor: Colors.green,
      secondaryColor: Colors.lightGreen,
      difficulty: 2,
      tags: ['classic', 'arcade', 'snake'],
    ),
    GameType.hueDrop: GameInfo(
      type: GameType.hueDrop,
      id: 'hue_drop',
      name: 'HueDrop',
      description: 'Match falling balls to color ring',
      icon: Icons.color_lens,
      primaryColor: Colors.pink,
      secondaryColor: Colors.pinkAccent,
      difficulty: 3,
      tags: ['puzzle', 'color', 'matching'],
    ),
    GameType.dashRush: GameInfo(
      type: GameType.dashRush,
      id: 'dash_rush',
      name: 'DashRush',
      description: 'Endless runner - swipe to dodge!',
      icon: Icons.directions_run,
      primaryColor: Colors.orange,
      secondaryColor: Colors.deepOrange,
      difficulty: 3,
      tags: ['endless', 'runner', 'swipe'],
    ),
    GameType.beatTap: GameInfo(
      type: GameType.beatTap,
      id: 'beat_tap',
      name: 'BeatTap',
      description: 'Piano tiles rhythm game',
      icon: Icons.piano,
      primaryColor: Colors.indigo,
      secondaryColor: Colors.deepPurple,
      difficulty: 4,
      tags: ['rhythm', 'music', 'tap'],
    ),
    GameType.boomSweep: GameInfo(
      type: GameType.boomSweep,
      id: 'boom_sweep',
      name: 'BoomSweep',
      description: 'Classic minesweeper puzzle',
      icon: Icons.grid_on,
      primaryColor: Colors.grey,
      secondaryColor: Colors.blueGrey,
      difficulty: 3,
      tags: ['puzzle', 'logic', 'classic'],
    ),
    GameType.fruitSnag: GameInfo(
      type: GameType.fruitSnag,
      id: 'fruit_snag',
      name: 'FruitSnag',
      description: 'Catch falling fruits with basket',
      icon: Icons.shopping_basket,
      primaryColor: Colors.green,
      secondaryColor: Colors.lightGreen,
      difficulty: 2,
      tags: ['catch', 'fruits', 'arcade'],
    ),
    GameType.tileTwist: GameInfo(
      type: GameType.tileTwist,
      id: 'tile_twist',
      name: 'TileTwist',
      description: '2048-style number merging puzzle',
      icon: Icons.view_module,
      primaryColor: Colors.brown,
      secondaryColor: Colors.orange,
      difficulty: 3,
      tags: ['puzzle', 'merge', 'strategy'],
    ),
    GameType.dartDash: GameInfo(
      type: GameType.dartDash,
      id: 'dart_dash',
      name: 'DartDash',
      description: 'Aim and throw darts at target',
      icon: Icons.gps_fixed,
      primaryColor: Colors.red,
      secondaryColor: Colors.redAccent,
      difficulty: 3,
      tags: ['aim', 'precision', 'darts'],
    ),
    GameType.dropTarget: GameInfo(
      type: GameType.dropTarget,
      id: 'drop_target',
      name: 'DropTarget',
      description: 'Drop items into moving targets',
      icon: Icons.arrow_drop_down_circle,
      primaryColor: Colors.blue,
      secondaryColor: Colors.lightBlue,
      difficulty: 3,
      tags: ['timing', 'precision', 'drop'],
    ),
    GameType.popBurst: GameInfo(
      type: GameType.popBurst,
      id: 'pop_burst',
      name: 'PopBurst',
      description: 'Pop balloons before they escape!',
      icon: Icons.bubble_chart,
      primaryColor: Colors.pink,
      secondaryColor: Colors.pinkAccent,
      difficulty: 2,
      tags: ['pop', 'balloons', 'timing'],
    ),
    GameType.stackify: GameInfo(
      type: GameType.stackify,
      id: 'stackify',
      name: 'Stackify',
      description: 'Stack blocks with perfect timing',
      icon: Icons.layers,
      primaryColor: Colors.purple,
      secondaryColor: Colors.deepPurple,
      difficulty: 4,
      tags: ['stack', 'timing', 'precision'],
    ),

    // New Games (MiniGameBase Architecture)
    GameType.laserJump: GameInfo(
      type: GameType.laserJump,
      id: 'laser_jump',
      name: 'LaserJump',
      description: 'Jump between platforms and avoid lasers!',
      icon: Icons.flash_on,
      primaryColor: Colors.blue,
      secondaryColor: Colors.lightBlue,
      difficulty: 3,
      tags: ['jumping', 'platforms', 'lasers'],
    ),
    GameType.coinClimb: GameInfo(
      type: GameType.coinClimb,
      id: 'coin_climb',
      name: 'CoinClimb',
      description: 'Climb walls and collect coins!',
      icon: Icons.terrain,
      primaryColor: Colors.brown,
      secondaryColor: Colors.orange,
      difficulty: 3,
      tags: ['climbing', 'coins', 'walls'],
    ),
    GameType.flipShot: GameInfo(
      type: GameType.flipShot,
      id: 'flip_shot',
      name: 'FlipShot',
      description: 'Ricochet shooting with trajectory preview!',
      icon: Icons.sports_baseball,
      primaryColor: Colors.red,
      secondaryColor: Colors.redAccent,
      difficulty: 4,
      tags: ['shooting', 'ricochet', 'physics'],
    ),
    GameType.pixelDive: GameInfo(
      type: GameType.pixelDive,
      id: 'pixel_dive',
      name: 'PixelDive',
      description: 'Endless falling with obstacle avoidance!',
      icon: Icons.arrow_downward,
      primaryColor: Colors.teal,
      secondaryColor: Colors.cyan,
      difficulty: 3,
      tags: ['falling', 'obstacles', 'steering'],
    ),
    GameType.glowRunner: GameInfo(
      type: GameType.glowRunner,
      id: 'glow_runner',
      name: 'GlowRunner',
      description: 'Neon parkour with light trail mechanics!',
      icon: Icons.directions_run,
      primaryColor: Colors.purple,
      secondaryColor: Colors.deepPurple,
      difficulty: 4,
      tags: ['parkour', 'jumping', 'neon'],
    ),
    GameType.bubbleBlast: GameInfo(
      type: GameType.bubbleBlast,
      id: 'bubble_blast',
      name: 'BubbleBlast',
      description: 'Create chain reactions to pop bubbles!',
      icon: Icons.bubble_chart,
      primaryColor: Colors.pink,
      secondaryColor: Colors.pinkAccent,
      difficulty: 3,
      tags: ['bubbles', 'chain reaction', 'strategy'],
    ),
    GameType.shadowSprint: GameInfo(
      type: GameType.shadowSprint,
      id: 'shadow_sprint',
      name: 'ShadowSprint',
      description: 'Stealth runner with light/shadow mechanics!',
      icon: Icons.visibility_off,
      primaryColor: Colors.grey,
      secondaryColor: Colors.blueGrey,
      difficulty: 4,
      tags: ['stealth', 'shadows', 'running'],
    ),
    GameType.shapeShift: GameInfo(
      type: GameType.shapeShift,
      id: 'shape_shift',
      name: 'ShapeShift',
      description: 'Morphing puzzle with shape matching!',
      icon: Icons.change_circle,
      primaryColor: Colors.indigo,
      secondaryColor: Colors.indigoAccent,
      difficulty: 3,
      tags: ['shapes', 'morphing', 'matching'],
    ),
    GameType.rocketRider: GameInfo(
      type: GameType.rocketRider,
      id: 'rocket_rider',
      name: 'RocketRider',
      description: 'Thrust-based space navigation!',
      icon: Icons.rocket_launch,
      primaryColor: Colors.orange,
      secondaryColor: Colors.deepOrange,
      difficulty: 4,
      tags: ['space', 'thrust', 'navigation'],
    ),
    GameType.tugTap: GameInfo(
      type: GameType.tugTap,
      id: 'tug_tap',
      name: 'TugTap',
      description: 'Rhythm tug-of-war with timing!',
      icon: Icons.music_note,
      primaryColor: Colors.green,
      secondaryColor: Colors.lightGreen,
      difficulty: 2,
      tags: ['rhythm', 'tug-of-war', 'timing'],
    ),
    GameType.tileTide: GameInfo(
      type: GameType.tileTide,
      id: 'tile_tide',
      name: 'TileTide',
      description: 'Sliding tile puzzle with time pressure!',
      icon: Icons.grid_4x4,
      primaryColor: Colors.blue,
      secondaryColor: Colors.lightBlue,
      difficulty: 3,
      tags: ['tiles', 'sliding', 'puzzle'],
    ),
    GameType.zapGrid: GameInfo(
      type: GameType.zapGrid,
      id: 'zap_grid',
      name: 'ZapGrid',
      description: 'Circuit completion puzzle with energy flow!',
      icon: Icons.electrical_services,
      primaryColor: Colors.yellow,
      secondaryColor: Colors.amber,
      difficulty: 4,
      tags: ['circuits', 'energy', 'connections'],
    ),
  };

  static const Map<CasinoGameType, CasinoGameInfo> _casinoGameInfoMap = {
    CasinoGameType.spinWheel: CasinoGameInfo(
      type: CasinoGameType.spinWheel,
      id: 'spin_wheel',
      name: 'Spin Wheel',
      description: 'Spin the wheel of fortune! 8 segments with varying rewards.',
      icon: Icons.casino,
      primaryColor: Colors.amber,
      secondaryColor: Colors.orange,
      entryCost: 50,
      riskLevel: 'Medium',
      tags: ['wheel', 'luck', 'classic'],
    ),
    CasinoGameType.highOrLow: CasinoGameInfo(
      type: CasinoGameType.highOrLow,
      id: 'high_or_low',
      name: 'High or Low',
      description: 'Guess if the next card is higher or lower. Double or nothing!',
      icon: Icons.style,
      primaryColor: Colors.red,
      secondaryColor: Colors.redAccent,
      entryCost: 25,
      riskLevel: 'Low',
      tags: ['cards', 'guessing', 'strategy'],
    ),
    CasinoGameType.luckyDarts: CasinoGameInfo(
      type: CasinoGameType.luckyDarts,
      id: 'lucky_darts',
      name: 'Lucky Darts',
      description: 'Aim for the bullseye! 5 rings with different prizes.',
      icon: Icons.gps_fixed,
      primaryColor: Colors.green,
      secondaryColor: Colors.lightGreen,
      entryCost: 40,
      riskLevel: 'Medium',
      tags: ['darts', 'precision', 'skill'],
    ),
    CasinoGameType.rollNRisk: CasinoGameInfo(
      type: CasinoGameType.rollNRisk,
      id: 'roll_n_risk',
      name: 'Roll \'n\' Risk',
      description: 'Roll 2 dice and see your fate! Jackpot on snake eyes.',
      icon: Icons.casino_outlined,
      primaryColor: Colors.purple,
      secondaryColor: Colors.deepPurple,
      entryCost: 30,
      riskLevel: 'High',
      tags: ['dice', 'luck', 'risk'],
    ),
    CasinoGameType.mysteryMultiplier: CasinoGameInfo(
      type: CasinoGameType.mysteryMultiplier,
      id: 'mystery_multiplier',
      name: 'Mystery Multiplier',
      description: 'Choose a mystery box! Random multipliers up to 5x.',
      icon: Icons.card_giftcard,
      primaryColor: Colors.indigo,
      secondaryColor: Colors.deepPurple,
      entryCost: 100,
      riskLevel: 'High',
      tags: ['mystery', 'multiplier', 'gamble'],
    ),
  };

  List<GameInfo> get allGames => _gameInfoMap.values.toList();
  List<CasinoGameInfo> get allCasinoGames => _casinoGameInfoMap.values.toList();

  GameInfo? getGameInfo(GameType type) => _gameInfoMap[type];
  CasinoGameInfo? getCasinoGameInfo(CasinoGameType type) => _casinoGameInfoMap[type];

  GameInfo getGameInfoById(String id) {
    try {
      return _gameInfoMap.values.firstWhere(
        (game) => game.id == id,
      );
    } catch (e) {
      throw ArgumentError('Game with id "$id" not found');
    }
  }

  List<GameInfo> getGamesByDifficulty(int difficulty) {
    return _gameInfoMap.values
        .where((game) => game.difficulty == difficulty)
        .toList();
  }

  List<GameInfo> getGamesByTag(String tag) {
    return _gameInfoMap.values
        .where((game) => game.tags.contains(tag))
        .toList();
  }

  List<String> getAllTags() {
    final tags = <String>{};
    for (final game in _gameInfoMap.values) {
      tags.addAll(game.tags);
    }
    return tags.toList()..sort();
  }

  void navigateToGame(BuildContext context, GameType gameType) {
    final gameInfo = getGameInfo(gameType);
    if (gameInfo != null) {
      context.push('/game/${gameInfo.id}');
    }
  }

  void navigateToGameById(BuildContext context, String gameId) {
    context.push('/game/$gameId');
  }

  /// Navigate to game with paywall check
  /// This method should be used instead of navigateToGame for paywall-enabled games
  Future<void> navigateToGameWithPaywall(
    BuildContext context,
    GameType gameType,
    {required dynamic ref} // WidgetRef for accessing providers
  ) async {
    final gameInfo = getGameInfo(gameType);
    if (gameInfo == null) return;

    // Import the paywall dialog here to avoid circular dependencies
    final gameAccessControlService = ref.read(gameAccessControlServiceProvider);
    final accessResult = await gameAccessControlService.canPlayGame(gameInfo.id);

    if (accessResult.isAllowed) {
      // If tokens are required, show confirmation dialog
      if (accessResult.requiresTokens) {
        if (context.mounted) {
          await _showTokenSpendConfirmation(
            context,
            gameInfo.name,
            accessResult.tokenCost!,
            () async {
              final startResult = await gameAccessControlService.startGame(gameInfo.id);
              if (startResult.isSuccess) {
                if (context.mounted) {
                  context.push('/game/${gameInfo.id}');
                }
              } else {
                // Handle start failure (shouldn't happen if access was allowed)
                if (context.mounted) {
                  _showErrorDialog(context, 'Failed to start game. Please try again.');
                }
              }
            },
          );
        }
      } else {
        // Free play - just start the game
        final startResult = await gameAccessControlService.startGame(gameInfo.id);
        if (startResult.isSuccess && context.mounted) {
          context.push('/game/${gameInfo.id}');
        }
      }
    } else {
      // Access denied - show paywall dialog
      if (context.mounted) {
        await _showPaywallDialog(context, gameInfo, accessResult, ref);
      }
    }
  }

  /// Show token spend confirmation dialog
  Future<void> _showTokenSpendConfirmation(
    BuildContext context,
    String gameName,
    int tokenCost,
    VoidCallback onConfirm,
  ) async {
    return showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Spend Tokens?'),
        content: Text('Spend $tokenCost tokens to play $gameName?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton.icon(
            onPressed: () {
              Navigator.of(context).pop();
              onConfirm();
            },
            icon: const Icon(Icons.token),
            label: Text('Spend $tokenCost Tokens'),
          ),
        ],
      ),
    );
  }

  /// Show paywall dialog
  Future<void> _showPaywallDialog(
    BuildContext context,
    GameInfo gameInfo,
    dynamic accessResult, // GameAccessResult
    dynamic ref, // WidgetRef
  ) async {
    // This will be implemented with the actual dialog widget
    // For now, show a simple dialog
    return showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Access Restricted'),
        content: Text(accessResult.message ?? 'Cannot access this game'),
        actions: [
          if (accessResult.suggestedAction?.toString() == 'GameAccessSuggestedAction.signUp')
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                context.push('/login');
              },
              child: const Text('Sign Up'),
            ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  /// Show error dialog
  void _showErrorDialog(BuildContext context, String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Error'),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void navigateToLeaderboard(BuildContext context, {String? gameId}) {
    if (gameId != null) {
      context.push('/leaderboard/$gameId');
    } else {
      context.push('/leaderboard');
    }
  }

  void navigateToStore(BuildContext context) {
    context.push('/store');
  }

  void navigateToInventory(BuildContext context) {
    context.push('/inventory');
  }

  void navigateToProfile(BuildContext context) {
    context.push('/profile');
  }

  void navigateToSettings(BuildContext context) {
    context.push('/settings');
  }

  void navigateToCasino(BuildContext context) {
    context.push('/casino');
  }

  void navigateToCasinoGame(BuildContext context, CasinoGameType gameType) {
    final gameInfo = getCasinoGameInfo(gameType);
    if (gameInfo != null) {
      context.push('/casino/${gameInfo.id}');
    }
  }

  void navigateToCasinoGameById(BuildContext context, String gameId) {
    context.push('/casino/$gameId');
  }

  void navigateHome(BuildContext context) {
    context.go('/');
  }

  void showGameOverDialog(
    BuildContext context, {
    required String gameId,
    required int score,
    required int tokensEarned,
    bool isNewHighScore = false,
    VoidCallback? onPlayAgain,
    VoidCallback? onHome,
  }) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: Text(isNewHighScore ? 'New High Score!' : 'Game Over'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('Score: $score'),
            Text('Tokens Earned: $tokensEarned'),
            if (isNewHighScore)
              const Text(
                'Congratulations on your new high score!',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              onHome?.call();
            },
            child: const Text('Home'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              onPlayAgain?.call();
            },
            child: const Text('Play Again'),
          ),
        ],
      ),
    );
  }

  String getGameRoute(String gameId) => '/game/$gameId';
  String getLeaderboardRoute([String? gameId]) => 
      gameId != null ? '/leaderboard/$gameId' : '/leaderboard';
}
