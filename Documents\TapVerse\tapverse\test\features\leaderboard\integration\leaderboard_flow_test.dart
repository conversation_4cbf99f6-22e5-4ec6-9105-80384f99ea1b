import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tapverse/main.dart';
import 'package:tapverse/features/leaderboard/widgets/game_leaderboard_popup.dart';
import 'package:tapverse/features/leaderboard/widgets/player_id_card.dart';

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('Leaderboard Integration Tests', () {
    testWidgets('Complete leaderboard flow test', (WidgetTester tester) async {
      // Arrange - Launch the app
      await tester.pumpWidget(
        ProviderScope(
          child: TapVerseApp(),
        ),
      );
      await tester.pumpAndSettle(Duration(seconds: 3));

      // Act & Assert - Navigate through the leaderboard flow
      await _testMainMenuNavigation(tester);
      await _testLeaderboardPopup(tester);
      await _testPlayerInteractions(tester);
      await _testPerformanceOptimizations(tester);
    });

    testWidgets('Leaderboard caching and performance test', (WidgetTester tester) async {
      // Arrange
      await tester.pumpWidget(
        ProviderScope(
          child: TapVerseApp(),
        ),
      );
      await tester.pumpAndSettle(Duration(seconds: 3));

      // Act & Assert - Test caching behavior
      await _testCachingBehavior(tester);
      await _testOfflineMode(tester);
      await _testDataRefresh(tester);
    });

    testWidgets('Animation and visual effects test', (WidgetTester tester) async {
      // Arrange
      await tester.pumpWidget(
        ProviderScope(
          child: TapVerseApp(),
        ),
      );
      await tester.pumpAndSettle(Duration(seconds: 3));

      // Act & Assert - Test animations
      await _testAnimations(tester);
      await _testParticleEffects(tester);
      await _testTransitions(tester);
    });

    testWidgets('Accessibility and usability test', (WidgetTester tester) async {
      // Arrange
      await tester.pumpWidget(
        ProviderScope(
          child: TapVerseApp(),
        ),
      );
      await tester.pumpAndSettle(Duration(seconds: 3));

      // Act & Assert - Test accessibility
      await _testAccessibility(tester);
      await _testKeyboardNavigation(tester);
      await _testScreenReaderSupport(tester);
    });
  });
}

Future<void> _testMainMenuNavigation(WidgetTester tester) async {
  // Test navigation to leaderboards from main menu
  
  // Look for leaderboard button/tab
  final leaderboardButton = find.text('Leaderboards');
  if (leaderboardButton.evaluate().isNotEmpty) {
    await tester.tap(leaderboardButton);
    await tester.pumpAndSettle();
    
    // Verify leaderboard screen is displayed
    expect(find.text('Leaderboards'), findsAtLeastOneWidget);
  }
  
  // Alternative: Look for game cards that might open leaderboards
  final gameCards = find.byType(Card);
  if (gameCards.evaluate().isNotEmpty) {
    await tester.tap(gameCards.first);
    await tester.pumpAndSettle();
  }
}

Future<void> _testLeaderboardPopup(WidgetTester tester) async {
  // Test leaderboard popup functionality
  
  // Look for leaderboard popup or trigger
  final leaderboardTrigger = find.textContaining('Leaderboard');
  if (leaderboardTrigger.evaluate().isNotEmpty) {
    await tester.tap(leaderboardTrigger.first);
    await tester.pumpAndSettle();
    
    // Verify popup is displayed
    expect(find.byType(GameLeaderboardPopup), findsOneWidget);
    
    // Test top 3 section
    expect(find.text('#1'), findsAtLeastOneWidget);
    
    // Test player list
    expect(find.byType(PlayerIDCard), findsAtLeastOneWidget);
    
    // Test close functionality
    final closeButton = find.byIcon(Icons.close);
    if (closeButton.evaluate().isNotEmpty) {
      await tester.tap(closeButton);
      await tester.pumpAndSettle();
      
      // Verify popup is closed
      expect(find.byType(GameLeaderboardPopup), findsNothing);
    }
  }
}

Future<void> _testPlayerInteractions(WidgetTester tester) async {
  // Test player card interactions
  
  // Open leaderboard again
  final leaderboardTrigger = find.textContaining('Leaderboard');
  if (leaderboardTrigger.evaluate().isNotEmpty) {
    await tester.tap(leaderboardTrigger.first);
    await tester.pumpAndSettle();
    
    // Test player card tap
    final playerCards = find.byType(PlayerIDCard);
    if (playerCards.evaluate().isNotEmpty) {
      await tester.tap(playerCards.first);
      await tester.pumpAndSettle();
      
      // Verify player details are shown
      // This depends on the implementation
    }
    
    // Test challenge functionality if available
    final challengeButton = find.text('Challenge');
    if (challengeButton.evaluate().isNotEmpty) {
      await tester.tap(challengeButton);
      await tester.pumpAndSettle();
    }
    
    // Test "Improve My Score" button
    final improveButton = find.textContaining('Improve');
    if (improveButton.evaluate().isNotEmpty) {
      await tester.tap(improveButton);
      await tester.pumpAndSettle();
    }
  }
}

Future<void> _testPerformanceOptimizations(WidgetTester tester) async {
  // Test performance-related features
  
  // Measure rendering performance
  final stopwatch = Stopwatch()..start();
  
  // Trigger multiple redraws
  for (int i = 0; i < 5; i++) {
    await tester.pump();
  }
  
  stopwatch.stop();
  expect(stopwatch.elapsedMilliseconds, lessThan(500)); // Should be fast
  
  // Test scrolling performance in leaderboard
  final leaderboardTrigger = find.textContaining('Leaderboard');
  if (leaderboardTrigger.evaluate().isNotEmpty) {
    await tester.tap(leaderboardTrigger.first);
    await tester.pumpAndSettle();
    
    // Test smooth scrolling
    final scrollable = find.byType(Scrollable);
    if (scrollable.evaluate().isNotEmpty) {
      await tester.drag(scrollable.first, Offset(0, -200));
      await tester.pumpAndSettle();
      
      await tester.drag(scrollable.first, Offset(0, 200));
      await tester.pumpAndSettle();
    }
  }
}

Future<void> _testCachingBehavior(WidgetTester tester) async {
  // Test caching functionality
  
  // Open leaderboard to trigger caching
  final leaderboardTrigger = find.textContaining('Leaderboard');
  if (leaderboardTrigger.evaluate().isNotEmpty) {
    // First load - should cache data
    await tester.tap(leaderboardTrigger.first);
    await tester.pumpAndSettle();
    
    // Close and reopen - should use cached data
    final closeButton = find.byIcon(Icons.close);
    if (closeButton.evaluate().isNotEmpty) {
      await tester.tap(closeButton);
      await tester.pumpAndSettle();
    }
    
    // Reopen quickly - should load from cache
    final startTime = DateTime.now();
    await tester.tap(leaderboardTrigger.first);
    await tester.pumpAndSettle();
    final loadTime = DateTime.now().difference(startTime);
    
    // Cached load should be faster
    expect(loadTime.inMilliseconds, lessThan(1000));
  }
}

Future<void> _testOfflineMode(WidgetTester tester) async {
  // Test offline functionality (if implemented)
  
  // This would require mocking network conditions
  // For now, just verify the app doesn't crash when offline
  
  final leaderboardTrigger = find.textContaining('Leaderboard');
  if (leaderboardTrigger.evaluate().isNotEmpty) {
    await tester.tap(leaderboardTrigger.first);
    await tester.pumpAndSettle();
    
    // Should show cached data or appropriate offline message
    expect(find.byType(Widget), findsAtLeastOneWidget);
  }
}

Future<void> _testDataRefresh(WidgetTester tester) async {
  // Test data refresh functionality
  
  final leaderboardTrigger = find.textContaining('Leaderboard');
  if (leaderboardTrigger.evaluate().isNotEmpty) {
    await tester.tap(leaderboardTrigger.first);
    await tester.pumpAndSettle();
    
    // Look for refresh button or pull-to-refresh
    final refreshButton = find.byIcon(Icons.refresh);
    if (refreshButton.evaluate().isNotEmpty) {
      await tester.tap(refreshButton);
      await tester.pumpAndSettle();
    } else {
      // Try pull-to-refresh
      final scrollable = find.byType(Scrollable);
      if (scrollable.evaluate().isNotEmpty) {
        await tester.drag(scrollable.first, Offset(0, 300));
        await tester.pumpAndSettle();
      }
    }
  }
}

Future<void> _testAnimations(WidgetTester tester) async {
  // Test animation functionality
  
  final leaderboardTrigger = find.textContaining('Leaderboard');
  if (leaderboardTrigger.evaluate().isNotEmpty) {
    await tester.tap(leaderboardTrigger.first);
    
    // Let animations complete
    await tester.pumpAndSettle(Duration(seconds: 2));
    
    // Verify animations don't cause crashes
    expect(find.byType(Widget), findsAtLeastOneWidget);
    
    // Test floating animations on top 3
    // This would require checking for specific animation widgets
    
    // Test staggered list animations
    final playerCards = find.byType(PlayerIDCard);
    expect(playerCards.evaluate().length, greaterThan(0));
  }
}

Future<void> _testParticleEffects(WidgetTester tester) async {
  // Test particle effects (if visible)
  
  final leaderboardTrigger = find.textContaining('Leaderboard');
  if (leaderboardTrigger.evaluate().isNotEmpty) {
    await tester.tap(leaderboardTrigger.first);
    await tester.pumpAndSettle();
    
    // Look for crown with particle effects
    final crownElements = find.textContaining('#1');
    if (crownElements.evaluate().isNotEmpty) {
      // Let particle animations run
      await tester.pump(Duration(seconds: 1));
      
      // Verify no crashes from particle effects
      expect(find.byType(Widget), findsAtLeastOneWidget);
    }
  }
}

Future<void> _testTransitions(WidgetTester tester) async {
  // Test smooth transitions
  
  final leaderboardTrigger = find.textContaining('Leaderboard');
  if (leaderboardTrigger.evaluate().isNotEmpty) {
    // Test opening transition
    await tester.tap(leaderboardTrigger.first);
    await tester.pump(); // Start animation
    await tester.pump(Duration(milliseconds: 150)); // Mid animation
    await tester.pumpAndSettle(); // Complete animation
    
    // Test closing transition
    final closeButton = find.byIcon(Icons.close);
    if (closeButton.evaluate().isNotEmpty) {
      await tester.tap(closeButton);
      await tester.pump(); // Start animation
      await tester.pump(Duration(milliseconds: 150)); // Mid animation
      await tester.pumpAndSettle(); // Complete animation
    }
  }
}

Future<void> _testAccessibility(WidgetTester tester) async {
  // Test accessibility features
  
  final leaderboardTrigger = find.textContaining('Leaderboard');
  if (leaderboardTrigger.evaluate().isNotEmpty) {
    await tester.tap(leaderboardTrigger.first);
    await tester.pumpAndSettle();
    
    // Check for semantic labels
    final semanticElements = find.bySemanticsLabel(RegExp(r'.*'));
    expect(semanticElements.evaluate().length, greaterThan(0));
    
    // Check for proper button semantics
    final buttons = find.byType(ElevatedButton);
    for (final button in buttons.evaluate()) {
      final semantics = tester.getSemantics(find.byWidget(button.widget));
      expect(semantics.hasFlag(SemanticsFlag.isButton), isTrue);
    }
  }
}

Future<void> _testKeyboardNavigation(WidgetTester tester) async {
  // Test keyboard navigation (if supported)
  
  // This would require testing focus traversal
  // For now, just verify focusable elements exist
  
  final leaderboardTrigger = find.textContaining('Leaderboard');
  if (leaderboardTrigger.evaluate().isNotEmpty) {
    await tester.tap(leaderboardTrigger.first);
    await tester.pumpAndSettle();
    
    // Look for focusable elements
    final focusableElements = find.byType(Focus);
    // Verify focusable elements exist (implementation dependent)
  }
}

Future<void> _testScreenReaderSupport(WidgetTester tester) async {
  // Test screen reader support
  
  final leaderboardTrigger = find.textContaining('Leaderboard');
  if (leaderboardTrigger.evaluate().isNotEmpty) {
    await tester.tap(leaderboardTrigger.first);
    await tester.pumpAndSettle();
    
    // Verify semantic structure
    final playerCards = find.byType(PlayerIDCard);
    if (playerCards.evaluate().isNotEmpty) {
      final firstCard = playerCards.first;
      final semantics = tester.getSemantics(firstCard);
      
      // Should have meaningful semantic information
      expect(semantics.label, isNotNull);
      expect(semantics.label.isNotEmpty, isTrue);
    }
  }
}
