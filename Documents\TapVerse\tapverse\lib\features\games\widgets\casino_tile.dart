import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../../../core/providers/app_providers.dart';
import '../../casino/widgets/casino_effects.dart';

class CasinoTile extends ConsumerWidget {
  const CasinoTile({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final casinoService = ref.watch(casinoServiceProvider);
    final gameNavigation = ref.watch(gameNavigationServiceProvider);
    final soundService = ref.watch(soundServiceProvider);
    final vibrationService = ref.watch(vibrationServiceProvider);
    
    final isAuthenticated = casinoService.isUserAuthenticated;
    final casinoGames = gameNavigation.allCasinoGames;

    return Card(
      elevation: 4,
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20),
      ),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(20),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Colors.amber.withValues(alpha: 0.2),
              Colors.orange.withValues(alpha: 0.3),
              Colors.red.withValues(alpha: 0.2),
            ],
          ),
          border: Border.all(
            color: Colors.amber.withValues(alpha: 0.4),
            width: 1,
          ),
        ),
        child: InkWell(
          onTap: () async {
            await soundService.playButtonClick();
            await vibrationService.onButtonTap();
            if (context.mounted) {
              if (isAuthenticated) {
                gameNavigation.navigateToCasino(context);
              } else {
                _showAuthenticationRequired(context);
              }
            }
          },
          borderRadius: BorderRadius.circular(20),
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Row(
              children: [
                // Icon section with glow effect
                GlowEffect(
                  glowColor: Colors.amber,
                  child: Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.amber.withValues(alpha: 0.3),
                      borderRadius: BorderRadius.circular(16),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.amber.withValues(alpha: 0.4),
                          blurRadius: 12,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Icon(
                      Icons.casino,
                      size: 32,
                      color: Colors.amber.shade300,
                    ),
                  ),
                ),
                
                const SizedBox(width: 20),
                
                // Content section
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Text(
                            'Casino',
                            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                          ),
                          const SizedBox(width: 8),
                          if (!isAuthenticated)
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 8,
                                vertical: 2,
                              ),
                              decoration: BoxDecoration(
                                color: Colors.red.withValues(alpha: 0.2),
                                borderRadius: BorderRadius.circular(8),
                                border: Border.all(
                                  color: Colors.red.withValues(alpha: 0.4),
                                ),
                              ),
                              child: Text(
                                'AUTH REQUIRED',
                                style: TextStyle(
                                  color: Colors.red.shade300,
                                  fontSize: 10,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                        ],
                      ),
                      const SizedBox(height: 4),
                      Text(
                        isAuthenticated 
                          ? '${casinoGames.length} Games Available'
                          : 'Sign up to access casino games',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Colors.white.withValues(alpha: 0.7),
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        isAuthenticated 
                          ? 'Try your luck and win big!'
                          : 'Account required for casino features',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Colors.amber.shade300,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
                
                // Arrow icon or lock icon
                Icon(
                  isAuthenticated ? Icons.arrow_forward_ios : Icons.lock,
                  color: isAuthenticated ? Colors.amber.shade300 : Colors.red.shade300,
                  size: 20,
                ),
              ],
            ),
          ),
        ),
      ),
    ).animate()
     .fadeIn(duration: 600.ms, delay: 400.ms)
     .slideX(begin: 0.2, end: 0);
  }

  void _showAuthenticationRequired(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Authentication Required'),
        content: const Text(
          'You need to create an account to access casino games. '
          'This helps us ensure responsible gaming and secure transactions.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // TODO: Navigate to account creation
            },
            child: const Text('Create Account'),
          ),
        ],
      ),
    );
  }
}
