import 'dart:math' as math;
import 'package:flame/components.dart';
import 'package:flame/collisions.dart';
import 'package:flutter/material.dart';
import '../../core/mini_game_base.dart';
import '../../core/hud.dart';
import '../../core/effects.dart';
import '../../core/analytics.dart';
import '../../core/difficulty.dart';

/// BubbleBlast - Chain reaction bubble popping game
/// Controls: Tap to place a charge; triggers blast radius after short fuse
/// Scoring: Score = bubbles popped * chain depth multiplier
class BubbleBlastGame extends MiniGameBase {
  BubbleBlastGame(int seed) : super(modeId: 'bubble_blast', sessionSeed: seed) {
    rng = math.Random(seed);
  }

  late math.Random rng;
  late ScoreText scoreText;
  late TextComponent chargesText;
  
  // Game state
  int score = 0;
  int chargesRemaining = 3;
  int bubblesPopped = 0;
  int maxChainDepth = 0;
  
  // Game entities
  late List<Bubble> bubbles;
  late List<BlastCharge> charges;
  
  // Difficulty curves (from planning document)
  final bubbleCountCurve = const CurveParam(start: 20, max: 40, perMinute: 10);
  final chainThresholdCurve = const CurveParam(start: 50, max: 100, perMinute: 25);
  
  // Difficulty manager
  late DifficultyManager difficultyManager;
  
  // Level state
  int currentLevel = 1;
  bool levelComplete = false;

  @override
  Future<void> loadAssets() async {
    // Load any required assets here
  }

  @override
  void setupWorld() {
    camera.viewfinder.visibleGameSize = Vector2(720, 1280);
    
    // Initialize difficulty manager
    difficultyManager = DifficultyManager();
    difficultyManager.addParameter('bubble_count', bubbleCountCurve);
    difficultyManager.addParameter('chain_threshold', chainThresholdCurve);
    
    // Initialize collections
    bubbles = [];
    charges = [];
    
    // Create game entities
    _createGameEntities();
  }

  void _createGameEntities() {
    // Generate initial level
    _generateLevel();
  }

  void _generateLevel() {
    final bubbleCount = difficultyManager.getValue('bubble_count').round();
    
    // Clear existing bubbles
    for (final bubble in bubbles) {
      bubble.removeFromParent();
    }
    bubbles.clear();
    
    // Generate new bubbles
    for (int i = 0; i < bubbleCount; i++) {
      _generateBubble();
    }
    
    // Reset charges for new level
    chargesRemaining = 3;
    levelComplete = false;
  }

  void _generateBubble() {
    // Random position ensuring bubbles don't overlap too much
    Vector2 position;
    int attempts = 0;
    
    do {
      position = Vector2(
        50 + rng.nextDouble() * 620,
        100 + rng.nextDouble() * 1000,
      );
      attempts++;
    } while (attempts < 10 && _isTooCloseToOtherBubbles(position));
    
    // Random size
    final size = 20 + rng.nextDouble() * 30; // 20-50 radius
    
    // Random color
    final colors = [Colors.red, Colors.blue, Colors.green, Colors.yellow, Colors.purple];
    final color = colors[rng.nextInt(colors.length)];
    
    final bubble = Bubble(
      position: position,
      radius: size,
      color: color,
      onPopped: _onBubblePopped,
    );
    
    bubbles.add(bubble);
    add(bubble);
  }

  bool _isTooCloseToOtherBubbles(Vector2 position) {
    for (final bubble in bubbles) {
      if ((bubble.position - position).length < 80) {
        return true;
      }
    }
    return false;
  }

  @override
  void setupHUD() {
    // Score display
    scoreText = ScoreText(
      position: Vector2(16, 16),
      textStyle: const TextStyle(
        color: Colors.white,
        fontSize: 24,
        fontWeight: FontWeight.bold,
      ),
    );
    add(scoreText);

    // Charges display
    chargesText = TextComponent(
      text: 'Charges: $chargesRemaining',
      position: Vector2(16, 50),
      textRenderer: TextPaint(
        style: const TextStyle(
          color: Colors.orange,
          fontSize: 20,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
    add(chargesText);
  }

  @override
  void onStart() {
    score = 0;
    chargesRemaining = 3;
    bubblesPopped = 0;
    maxChainDepth = 0;
    currentLevel = 1;
    
    // Start difficulty progression
    difficultyManager.start();
    
    // Start the game
    startGame();
    
    Analytics.log('bubble_blast_start', {'session_seed': sessionSeed});
  }

  @override
  void onGameUpdate(double dt) {
    // Update charges display
    chargesText.text = 'Charges: $chargesRemaining';
    
    // Check level completion
    if (!levelComplete && bubbles.isEmpty && charges.isEmpty) {
      _completeLevel();
    }
    
    // Check game over condition
    if (chargesRemaining <= 0 && bubbles.isNotEmpty && charges.isEmpty) {
      _gameOver();
    }
  }

  // TODO: Implement input handling
  void handleTapDown(Vector2 position) {
    if (isGameEnded || chargesRemaining <= 0) return;

    _placeCharge(position);
  }

  void _placeCharge(Vector2 position) {
    if (chargesRemaining <= 0) return;
    
    chargesRemaining--;
    
    final charge = BlastCharge(
      position: position,
      onExplode: _onChargeExplode,
    );
    
    charges.add(charge);
    add(charge);
    
    Analytics.log('charge_placed', {
      'position_x': position.x,
      'position_y': position.y,
      'charges_remaining': chargesRemaining,
    });
  }

  void _onChargeExplode(Vector2 explosionCenter, double blastRadius) {
    // Remove the charge from tracking
    charges.removeWhere((charge) => 
        (charge.position - explosionCenter).length < 10);
    
    // Find bubbles within blast radius
    final bubblesInRange = bubbles.where((bubble) =>
        (bubble.position - explosionCenter).length <= blastRadius + bubble.radius
    ).toList();
    
    if (bubblesInRange.isNotEmpty) {
      _triggerChainReaction(bubblesInRange, 1);
    }
    
    // Visual effect
    add(Effects.explosion(explosionCenter, particleCount: 30));
  }

  void _triggerChainReaction(List<Bubble> initialBubbles, int chainDepth) {
    if (initialBubbles.isEmpty) return;
    
    maxChainDepth = math.max(maxChainDepth, chainDepth);
    
    // Pop initial bubbles
    final newlyPopped = <Bubble>[];
    for (final bubble in initialBubbles) {
      if (bubbles.contains(bubble)) {
        bubble.pop();
        bubbles.remove(bubble);
        newlyPopped.add(bubble);
        bubblesPopped++;
      }
    }
    
    // Calculate score for this chain level
    final points = newlyPopped.length * chainDepth;
    score += points;
    addScore(points);
    scoreText.updateScore(score);
    
    // Visual effects
    for (final bubble in newlyPopped) {
      add(Effects.burst(bubble.position, color: bubble.color));
      add(Effects.textPopup(
        bubble.position,
        '+$chainDepth',
        color: Colors.white,
      ));
    }
    
    // Check for secondary chain reactions
    Future.delayed(const Duration(milliseconds: 200), () {
      final secondaryBubbles = <Bubble>[];
      
      for (final poppedBubble in newlyPopped) {
        final nearbyBubbles = bubbles.where((bubble) =>
            (bubble.position - poppedBubble.position).length <= poppedBubble.radius * 2
        ).toList();
        
        for (final nearby in nearbyBubbles) {
          if (!secondaryBubbles.contains(nearby)) {
            secondaryBubbles.add(nearby);
          }
        }
      }
      
      if (secondaryBubbles.isNotEmpty) {
        _triggerChainReaction(secondaryBubbles, chainDepth + 1);
      }
    });
    
    Analytics.log('chain_reaction', {
      'chain_depth': chainDepth,
      'bubbles_popped': newlyPopped.length,
      'points': points,
    });
  }

  void _onBubblePopped(Bubble bubble) {
    // This is called when a bubble is popped
    // The actual logic is handled in _triggerChainReaction
  }

  void _completeLevel() {
    levelComplete = true;
    currentLevel++;
    
    // Bonus points for completing level
    final bonusPoints = chargesRemaining * 10;
    score += bonusPoints;
    addScore(bonusPoints);
    scoreText.updateScore(score);
    
    // Visual effects
    add(Effects.sparkle(Vector2(360, 640), colors: [Colors.amber, Colors.yellow]));
    add(Effects.textPopup(
      Vector2(360, 600),
      'LEVEL COMPLETE! +$bonusPoints',
      color: Colors.amber,
    ));
    
    // Generate next level after delay
    Future.delayed(const Duration(seconds: 2), () {
      if (!isGameEnded) {
        _generateLevel();
      }
    });
    
    Analytics.log('level_complete', {
      'level': currentLevel - 1,
      'bonus_points': bonusPoints,
      'max_chain_depth': maxChainDepth,
    });
  }

  void _gameOver() {
    endGame(success: false, score: score);
    
    Analytics.log('game_over', {
      'final_score': score,
      'bubbles_popped': bubblesPopped,
      'max_chain_depth': maxChainDepth,
      'levels_completed': currentLevel - 1,
    });
  }


  @override
  void reportScore(int score) {
    // Integration hook for TapVerse UI
  }

  @override
  void awardTokens(int tokens) {
    // Integration hook for TapVerse token system
  }
}

/// Bubble component
class Bubble extends CircleComponent with HasCollisionDetection {
  Bubble({
    required super.position,
    required super.radius,
    required this.color,
    required this.onPopped,
  });

  final Color color;
  final Function(Bubble) onPopped;

  @override
  Future<void> onLoad() async {
    add(CircleHitbox());
    paint = Paint()..color = color;
  }

  void pop() {
    onPopped(this);
    removeFromParent();
  }
}

/// Blast charge component
class BlastCharge extends CircleComponent {
  BlastCharge({
    required super.position,
    required this.onExplode,
  }) : super(radius: 10);

  final Function(Vector2, double) onExplode;
  
  double fuseTimer = 0.0;
  static const double fuseTime = 1.0; // 1 second fuse
  static const double blastRadius = 80;

  @override
  Future<void> onLoad() async {
    paint = Paint()..color = Colors.orange;
  }

  @override
  void update(double dt) {
    super.update(dt);
    
    fuseTimer += dt;
    
    // Flash effect as fuse burns down
    final flashRate = (fuseTime - fuseTimer) * 5;
    if ((fuseTimer * flashRate).floor() % 2 == 0) {
      paint = Paint()..color = Colors.red;
    } else {
      paint = Paint()..color = Colors.orange;
    }
    
    // Explode when fuse runs out
    if (fuseTimer >= fuseTime) {
      onExplode(position, blastRadius);
      removeFromParent();
    }
  }
}
