import 'package:flutter/material.dart';
import 'dart:math' as math;
import '../../../core/theme/neon_theme.dart';
import 'particle_effects.dart';

/// 3D metallic crown icons for leaderboard rankings
class CrownIcon extends StatefulWidget {
  final int rank; // 1, 2, or 3
  final double size;
  final bool animate;

  const CrownIcon({
    super.key,
    required this.rank,
    this.size = 32,
    this.animate = true,
  });

  @override
  State<CrownIcon> createState() => _CrownIconState();
}

class _CrownIconState extends State<CrownIcon>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _shineAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    );
    
    _shineAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));

    if (widget.animate) {
      _controller.repeat();
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  Color get _crownColor {
    switch (widget.rank) {
      case 1:
        return TapVerseColors.neonGold;
      case 2:
        return TapVerseColors.neonSilver;
      case 3:
        return TapVerseColors.neonBronze;
      default:
        return Colors.grey;
    }
  }

  @override
  Widget build(BuildContext context) {
    Widget crownWidget = AnimatedBuilder(
      animation: _shineAnimation,
      builder: (context, child) {
        return Container(
          width: widget.size,
          height: widget.size,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            gradient: RadialGradient(
              center: Alignment.topLeft,
              colors: [
                _crownColor.withValues(alpha: 0.8),
                _crownColor.withValues(alpha: 0.4),
              ],
            ),
            boxShadow: [
              BoxShadow(
                color: _crownColor.withValues(alpha: 0.6 * _shineAnimation.value),
                blurRadius: 15,
                spreadRadius: 2,
              ),
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.3),
                blurRadius: 8,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Stack(
            alignment: Alignment.center,
            children: [
              // Crown icon
              Icon(
                Icons.workspace_premium,
                size: widget.size * 0.7,
                color: Colors.white,
              ),
              
              // Shine effect
              if (widget.animate)
                Positioned(
                  top: widget.size * 0.1,
                  left: widget.size * 0.1 + (widget.size * 0.6 * _shineAnimation.value),
                  child: Container(
                    width: 2,
                    height: widget.size * 0.8,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          Colors.white.withValues(alpha: 0.0),
                          Colors.white.withValues(alpha: 0.8),
                          Colors.white.withValues(alpha: 0.0),
                        ],
                      ),
                    ),
                  ),
                ),
              
              // Rank number
              Positioned(
                bottom: -2,
                child: Container(
                  width: widget.size * 0.4,
                  height: widget.size * 0.4,
                  decoration: BoxDecoration(
                    color: _crownColor,
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: Colors.white,
                      width: 1,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.3),
                        blurRadius: 4,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Center(
                    child: Text(
                      '${widget.rank}',
                      style: TextStyle(
                        fontSize: widget.size * 0.25,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );

    // Add particle effects for first place
    if (widget.rank == 1 && widget.animate) {
      return ParticleEffect(
        isActive: true,
        particleColor: _crownColor,
        particleCount: 15,
        particleSize: 6.0,
        duration: const Duration(seconds: 4),
        type: ParticleType.sparkle,
        child: crownWidget,
      );
    }

    return crownWidget;
  }
}

/// Animated crown for winners with particle effects
class AnimatedCrown extends StatefulWidget {
  final int rank;
  final double size;

  const AnimatedCrown({
    super.key,
    required this.rank,
    this.size = 48,
  });

  @override
  State<AnimatedCrown> createState() => _AnimatedCrownState();
}

class _AnimatedCrownState extends State<AnimatedCrown>
    with TickerProviderStateMixin {
  late AnimationController _bounceController;
  late AnimationController _particleController;
  late Animation<double> _bounceAnimation;
  late Animation<double> _particleAnimation;

  @override
  void initState() {
    super.initState();
    
    _bounceController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _particleController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );
    
    _bounceAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _bounceController,
      curve: Curves.elasticOut,
    ));
    
    _particleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _particleController,
      curve: Curves.easeOut,
    ));

    // Start animations with delay
    Future.delayed(Duration(milliseconds: widget.rank * 200), () {
      if (mounted) {
        _bounceController.forward();
        _particleController.repeat();
      }
    });
  }

  @override
  void dispose() {
    _bounceController.dispose();
    _particleController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: Listenable.merge([_bounceAnimation, _particleAnimation]),
      builder: (context, child) {
        return Transform.scale(
          scale: _bounceAnimation.value,
          child: Stack(
            alignment: Alignment.center,
            children: [
              // Particle effects
              if (widget.rank == 1) _buildParticles(),
              
              // Crown
              CrownIcon(
                rank: widget.rank,
                size: widget.size,
                animate: true,
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildParticles() {
    return SizedBox(
      width: widget.size * 2,
      height: widget.size * 2,
      child: Stack(
        children: List.generate(8, (index) {
          final angle = (index * 45) * (3.14159 / 180);
          final distance = widget.size * 0.8 * _particleAnimation.value;
          final x = distance * math.cos(angle);
          final y = distance * math.sin(angle);
          
          return Positioned(
            left: widget.size + x,
            top: widget.size + y,
            child: Opacity(
              opacity: 1.0 - _particleAnimation.value,
              child: Container(
                width: 4,
                height: 4,
                decoration: BoxDecoration(
                  color: TapVerseColors.neonGold,
                  shape: BoxShape.circle,
                  boxShadow: [
                    BoxShadow(
                      color: TapVerseColors.neonGold.withValues(alpha: 0.6),
                      blurRadius: 4,
                    ),
                  ],
                ),
              ),
            ),
          );
        }),
      ),
    );
  }
}
