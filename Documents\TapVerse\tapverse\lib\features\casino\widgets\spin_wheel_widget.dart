import 'package:flutter/material.dart';
import 'dart:math' as math;
import '../../../core/models/casino_models.dart';

class SpinWheelWidget extends StatefulWidget {
  final List<SpinWheelSegment> segments;
  final AnimationController controller;
  final bool isSpinning;

  const SpinWheelWidget({
    super.key,
    required this.segments,
    required this.controller,
    required this.isSpinning,
  });

  @override
  State<SpinWheelWidget> createState() => _SpinWheelWidgetState();
}

class _SpinWheelWidgetState extends State<SpinWheelWidget> {
  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: 300,
      height: 300,
      child: Stack(
        alignment: Alignment.center,
        children: [
          // Wheel
          AnimatedBuilder(
            animation: widget.controller,
            builder: (context, child) {
              return Transform.rotate(
                angle: widget.controller.value * 4 * math.pi,
                child: CustomPaint(
                  size: const Size(280, 280),
                  painter: <PERSON><PERSON>ainter(segments: widget.segments),
                ),
              );
            },
          ),

          // Center circle
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: Colors.white,
              shape: BoxShape.circle,
              border: Border.all(color: Colors.black, width: 2),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.3),
                  blurRadius: 8,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: const Icon(
              Icons.casino,
              color: Colors.amber,
              size: 24,
            ),
          ),

          // Pointer
          Positioned(
            top: 10,
            child: Container(
              width: 0,
              height: 0,
              decoration: const BoxDecoration(
                border: Border(
                  left: BorderSide(width: 15, color: Colors.transparent),
                  right: BorderSide(width: 15, color: Colors.transparent),
                  bottom: BorderSide(width: 30, color: Colors.red),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class WheelPainter extends CustomPainter {
  final List<SpinWheelSegment> segments;

  WheelPainter({required this.segments});

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width / 2;
    final segmentAngle = 2 * math.pi / segments.length;

    // Draw outer border
    final borderPaint = Paint()
      ..color = Colors.black
      ..style = PaintingStyle.stroke
      ..strokeWidth = 4;
    canvas.drawCircle(center, radius, borderPaint);

    for (int i = 0; i < segments.length; i++) {
      final segment = segments[i];
      final startAngle = i * segmentAngle - math.pi / 2; // Start from top
      
      // Draw segment
      final segmentPaint = Paint()
        ..color = segment.color
        ..style = PaintingStyle.fill;

      canvas.drawArc(
        Rect.fromCircle(center: center, radius: radius),
        startAngle,
        segmentAngle,
        true,
        segmentPaint,
      );

      // Draw segment border
      final segmentBorderPaint = Paint()
        ..color = Colors.white
        ..style = PaintingStyle.stroke
        ..strokeWidth = 2;

      canvas.drawArc(
        Rect.fromCircle(center: center, radius: radius),
        startAngle,
        segmentAngle,
        true,
        segmentBorderPaint,
      );

      // Draw text
      final textAngle = startAngle + segmentAngle / 2;
      final textRadius = radius * 0.7;
      final textX = center.dx + textRadius * math.cos(textAngle);
      final textY = center.dy + textRadius * math.sin(textAngle);

      final textPainter = TextPainter(
        text: TextSpan(
          text: segment.label,
          style: TextStyle(
            color: _getTextColor(segment.color),
            fontSize: segment.label.length > 3 ? 12 : 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        textDirection: TextDirection.ltr,
      );

      textPainter.layout();

      // Rotate text to be readable
      canvas.save();
      canvas.translate(textX, textY);
      
      // Rotate text based on position
      double textRotation = textAngle;
      if (textAngle > math.pi / 2 && textAngle < 3 * math.pi / 2) {
        textRotation += math.pi; // Flip text if it would be upside down
      }
      
      canvas.rotate(textRotation);
      textPainter.paint(
        canvas,
        Offset(-textPainter.width / 2, -textPainter.height / 2),
      );
      canvas.restore();

      // Draw special indicators for special segments
      if (segment.isSpecial) {
        final starRadius = radius * 0.9;
        final starX = center.dx + starRadius * math.cos(textAngle);
        final starY = center.dy + starRadius * math.sin(textAngle);

        final starPaint = Paint()
          ..color = Colors.yellow
          ..style = PaintingStyle.fill;

        _drawStar(canvas, Offset(starX, starY), 8, starPaint);
      }
    }
  }

  Color _getTextColor(Color backgroundColor) {
    // Calculate luminance to determine if text should be white or black
    final luminance = backgroundColor.computeLuminance();
    return luminance > 0.5 ? Colors.black : Colors.white;
  }

  void _drawStar(Canvas canvas, Offset center, double radius, Paint paint) {
    const int points = 5;
    final double angle = 2 * math.pi / points;
    final Path path = Path();

    for (int i = 0; i < points * 2; i++) {
      final double currentAngle = i * angle / 2 - math.pi / 2;
      final double currentRadius = i.isEven ? radius : radius * 0.5;
      final double x = center.dx + currentRadius * math.cos(currentAngle);
      final double y = center.dy + currentRadius * math.sin(currentAngle);

      if (i == 0) {
        path.moveTo(x, y);
      } else {
        path.lineTo(x, y);
      }
    }

    path.close();
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return true;
  }
}

/// Widget to display wheel segments legend
class WheelLegend extends StatelessWidget {
  final List<SpinWheelSegment> segments;

  const WheelLegend({super.key, required this.segments});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            'Wheel Segments',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          ...segments.map((segment) => Padding(
            padding: const EdgeInsets.symmetric(vertical: 4),
            child: Row(
              children: [
                Container(
                  width: 16,
                  height: 16,
                  decoration: BoxDecoration(
                    color: segment.color,
                    shape: BoxShape.circle,
                    border: Border.all(color: Colors.grey.shade300),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    segment.label == 'LOSE ALL' 
                        ? 'Lose All Tokens'
                        : '${segment.label} tokens',
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                ),
                Text(
                  '${(segment.probability * 100).toInt()}%',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.grey.shade600,
                  ),
                ),
                if (segment.isSpecial)
                  const Padding(
                    padding: EdgeInsets.only(left: 4),
                    child: Icon(
                      Icons.star,
                      color: Colors.amber,
                      size: 16,
                    ),
                  ),
              ],
            ),
          )),
        ],
      ),
    );
  }
}
