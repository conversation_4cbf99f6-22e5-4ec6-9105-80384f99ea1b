import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../../../core/providers/app_providers.dart';

import '../widgets/casino_game_card.dart';

class CasinoScreen extends ConsumerStatefulWidget {
  const CasinoScreen({super.key});

  @override
  ConsumerState<CasinoScreen> createState() => _CasinoScreenState();
}

class _CasinoScreenState extends ConsumerState<CasinoScreen> {
  @override
  Widget build(BuildContext context) {
    final clientCasinoService = ref.watch(clientCasinoServiceProvider);
    final gameNavigation = ref.watch(gameNavigationServiceProvider);
    final authService = ref.watch(authServiceProvider);

    final user = authService.currentUser;
    final isAuthenticated = user != null && !user.isAnonymous;
    final casinoGames = gameNavigation.allCasinoGames;

    // Allow access for testing, show warning for anonymous users

    return Scaffold(
      appBar: AppBar(
        title: const Text(
          '🎰 Casino',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 24,
          ),
        ),
        centerTitle: true,
        backgroundColor: Colors.amber.shade800,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          // Token display
          Flexible(
            child: Padding(
              padding: const EdgeInsets.only(right: 16.0),
              child: ref.watch(userTokensProvider).when(
                data: (tokens) => Container(
                  constraints: const BoxConstraints(maxWidth: 120),
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 6,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.black.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Icon(
                        Icons.toll,
                        color: Colors.amber,
                        size: 16,
                      ),
                      const SizedBox(width: 4),
                      Flexible(
                        child: Text(
                          '$tokens',
                          style: const TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                            fontSize: 14,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                ),
                loading: () => const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                ),
                error: (_, __) => const Icon(Icons.error, size: 20),
              ),
            ),
          ),
        ],
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Colors.amber.shade800,
              Colors.orange.shade700,
              Colors.red.shade600,
            ],
          ),
        ),
        child: Column(
          children: [
            // Welcome section
            Container(
              width: double.infinity,
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
              child: SafeArea(
                bottom: false,
                child: Column(
                  children: [
                    const Icon(
                      Icons.casino,
                      color: Colors.white,
                      size: 48,
                    ),
                    const SizedBox(height: 12),
                    Text(
                      'Welcome to the Casino!',
                      style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                      textAlign: TextAlign.center,
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Test your luck and win big with tokens!',
                      style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                        color: Colors.white.withValues(alpha: 0.9),
                      ),
                      textAlign: TextAlign.center,
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
            ),

            // Warning for anonymous users
            if (!isAuthenticated)
              Container(
                margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.orange.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.orange),
                ),
                child: Row(
                  children: [
                    Icon(Icons.warning, color: Colors.orange, size: 20),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'Testing Mode: Tokens can be manipulated. Create account for secure casino.',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 12,
                        ),
                      ),
                    ),
                  ],
                ),
              ),

            // Games grid
            Expanded(
              child: Container(
                decoration: const BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(24),
                    topRight: Radius.circular(24),
                  ),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Casino Games',
                        style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Choose your game and place your bets',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Colors.grey.shade600,
                        ),
                      ),
                      const SizedBox(height: 20),
                      
                      Expanded(
                        child: LayoutBuilder(
                          builder: (context, constraints) {
                            // Responsive grid based on screen width
                            int crossAxisCount = 1;
                            double childAspectRatio = 2.5;

                            if (constraints.maxWidth > 600) {
                              crossAxisCount = 2;
                              childAspectRatio = 2.0;
                            }

                            return GridView.builder(
                              padding: EdgeInsets.zero,
                              gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                                crossAxisCount: crossAxisCount,
                                childAspectRatio: childAspectRatio,
                                mainAxisSpacing: 16,
                                crossAxisSpacing: 16,
                              ),
                              itemCount: casinoGames.length,
                              itemBuilder: (context, index) {
                                final game = casinoGames[index];

                                return LargeCasinoGameCard(
                                  gameInfo: game,
                                  onTap: () async {
                                    final soundService = ref.read(soundServiceProvider);
                                    final vibrationService = ref.read(vibrationServiceProvider);

                                    await soundService.playButtonClick();
                                    await vibrationService.onButtonTap();

                                    if (context.mounted) {
                                      gameNavigation.navigateToCasinoGame(context, game.type);
                                    }
                                  },
                                ).animate(delay: Duration(milliseconds: index * 150))
                                 .fadeIn(duration: 600.ms)
                                 .slideY(begin: 0.3, end: 0);
                              },
                            );
                          },
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
