import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../../../core/models/user_model.dart';
import '../../../core/theme/neon_theme.dart';
import 'profile_editor_modal.dart';

class ProfileHeader extends StatelessWidget {
  final User user;
  final UserModel userModel;
  final VoidCallback onSignOut;
  final VoidCallback onEditProfile;

  const ProfileHeader({
    super.key,
    required this.user,
    required this.userModel,
    required this.onSignOut,
    required this.onEditProfile,
  });

  @override
  Widget build(BuildContext context) {
    return ProfileIdCard(
      user: user,
      userModel: userModel,
      onTap: onEditProfile,
      onSignOut: onSignOut,
    );
  }
}

class ProfileIdCard extends StatefulWidget {
  final User user;
  final UserModel userModel;
  final VoidCallback onTap;
  final VoidCallback onSignOut;

  const ProfileIdCard({
    super.key,
    required this.user,
    required this.userModel,
    required this.onTap,
    required this.onSignOut,
  });

  @override
  State<ProfileIdCard> createState() => _ProfileIdCardState();
}

class _ProfileIdCardState extends State<ProfileIdCard>
    with TickerProviderStateMixin {
  late AnimationController _glowController;
  late AnimationController _shimmerController;
  late Animation<double> _glowAnimation;
  late Animation<double> _shimmerAnimation;

  @override
  void initState() {
    super.initState();

    // Glow animation for the card border
    _glowController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    );
    _glowAnimation = Tween<double>(
      begin: 0.3,
      end: 0.8,
    ).animate(CurvedAnimation(
      parent: _glowController,
      curve: Curves.easeInOut,
    ));

    // Shimmer animation for tokens
    _shimmerController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );
    _shimmerAnimation = Tween<double>(
      begin: -1.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _shimmerController,
      curve: Curves.easeInOut,
    ));

    _glowController.repeat(reverse: true);
    _shimmerController.repeat();
  }

  @override
  void dispose() {
    _glowController.dispose();
    _shimmerController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final customization = widget.userModel.getProfileCustomization();

    return GestureDetector(
      onTap: () => _openProfileEditor(context),
      child: AnimatedBuilder(
        animation: Listenable.merge([_glowAnimation, _shimmerAnimation]),
        builder: (context, child) {
          return Container(
            margin: const EdgeInsets.symmetric(horizontal: 4, vertical: 8),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(24),
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  customization.glowColor.withValues(alpha: 0.2),
                  customization.glowColor.withValues(alpha: 0.1),
                  NeonTheme.darkBackground.withValues(alpha: 0.9),
                ],
              ),
              border: Border.all(
                color: customization.glowColor.withValues(alpha: _glowAnimation.value),
                width: 2,
              ),
              boxShadow: [
                // Outer neon glow
                BoxShadow(
                  color: customization.glowColor.withValues(alpha: _glowAnimation.value * customization.particleIntensity.intensity),
                  blurRadius: 20,
                  spreadRadius: 2,
                  offset: const Offset(0, 0),
                ),
                // Inner shadow for depth
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.3),
                  blurRadius: 15,
                  offset: const Offset(0, 8),
                ),
                // Subtle top highlight
                BoxShadow(
                  color: Colors.white.withValues(alpha: 0.1),
                  blurRadius: 5,
                  offset: const Offset(0, -2),
                ),
              ],
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(22),
              child: Stack(
                children: [
                  // Background pattern/particles (optional)
                  _buildBackgroundPattern(),

                  // Main content
                  Padding(
                    padding: const EdgeInsets.all(20),
                    child: Column(
                      children: [
                        // Top section with avatar and basic info
                        Row(
                          children: [
                            _buildProfileAvatar(),
                            const SizedBox(width: 16),
                            Expanded(
                              child: _buildUserInfo(context),
                            ),
                            _buildCustomizeButton(),
                          ],
                        ),

                        const SizedBox(height: 16),

                        // Tokens section with shimmer
                        _buildTokensSection(context),

                        const SizedBox(height: 16),

                        // Action buttons
                        _buildActionButtons(context),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
  Widget _buildBackgroundPattern() {
    return Positioned.fill(
      child: CustomPaint(
        painter: ParticlePatternPainter(
          animationValue: _shimmerAnimation.value,
        ),
      ),
    );
  }

  Widget _buildProfileAvatar() {
    final customization = widget.userModel.getProfileCustomization();

    return Container(
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            customization.glowColor,
            customization.glowColor.withValues(alpha: 0.7),
          ],
        ),
        boxShadow: [
          BoxShadow(
            color: customization.glowColor.withValues(alpha: customization.particleIntensity.intensity * 0.6),
            blurRadius: 15,
            spreadRadius: 2,
          ),
        ],
      ),
      child: Container(
        margin: const EdgeInsets.all(3),
        decoration: const BoxDecoration(
          shape: BoxShape.circle,
          color: NeonTheme.darkBackground,
        ),
        child: CircleAvatar(
          radius: 35,
          backgroundColor: Colors.transparent,
          child: Icon(
            _getProfilePictureIcon(customization.profilePicture),
            color: Colors.white,
            size: 32,
          ),
        ),
      ),
    );
  }

  Widget _buildUserInfo(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Player name
        Text(
          widget.userModel.displayName,
          style: NeonTheme.createNeonText(
            color: Colors.white,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
          overflow: TextOverflow.ellipsis,
        ),

        const SizedBox(height: 4),

        // Skill title from customization
        Text(
          widget.userModel.getProfileCustomization().skillTitle,
          style: NeonTheme.createNeonText(
            color: widget.userModel.getProfileCustomization().glowColor,
            fontSize: 14,
            fontWeight: FontWeight.w500,
            hasGlow: true,
          ),
        ),

        const SizedBox(height: 8),

        // Account type badge
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: widget.user.isAnonymous
                  ? [Colors.orange, Colors.deepOrange]
                  : [NeonTheme.neonGreen, NeonTheme.neonCyan],
            ),
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: (widget.user.isAnonymous ? Colors.orange : NeonTheme.neonGreen)
                    .withValues(alpha: 0.4),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Text(
            widget.user.isAnonymous ? 'Guest Player' : 'Registered',
            style: const TextStyle(
              color: Colors.white,
              fontSize: 12,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildCustomizeButton() {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [NeonTheme.neonPurple, NeonTheme.neonPink],
        ),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: NeonTheme.neonPurple.withValues(alpha: 0.4),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(20),
          onTap: () => _openProfileEditor(context),
          child: const Padding(
            padding: EdgeInsets.all(12),
            child: Icon(
              Icons.edit,
              color: Colors.white,
              size: 20,
            ),
          ),
        ),
      ),
    );
  }

  void _openProfileEditor(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => ProfileEditorModal(
        user: widget.user,
        userModel: widget.userModel,
        onSave: () {
          // Refresh the profile or trigger any necessary updates
          widget.onTap();
        },
      ),
    );
  }

  Widget _buildTokensSection(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            NeonTheme.neonAmber.withValues(alpha: 0.2),
            NeonTheme.neonAmber.withValues(alpha: 0.1),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: NeonTheme.neonAmber.withValues(alpha: 0.4),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.monetization_on,
            color: NeonTheme.neonAmber,
            size: 24,
          ),
          const SizedBox(width: 8),
          ShaderMask(
            shaderCallback: (bounds) {
              return LinearGradient(
                begin: Alignment(-1.0 + _shimmerAnimation.value * 2, 0.0),
                end: Alignment(1.0 + _shimmerAnimation.value * 2, 0.0),
                colors: [
                  NeonTheme.neonAmber.withValues(alpha: 0.3),
                  NeonTheme.neonAmber,
                  NeonTheme.neonAmber.withValues(alpha: 0.3),
                ],
              ).createShader(bounds);
            },
            child: Text(
              '${widget.userModel.tokens}',
              style: NeonTheme.createNeonText(
                color: Colors.white,
                fontSize: 24,
                fontWeight: FontWeight.bold,
                hasGlow: true,
              ),
            ),
          ),
          const SizedBox(width: 4),
          Text(
            'Tokens',
            style: NeonTheme.createNeonText(
              color: NeonTheme.neonAmber,
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: Container(
            decoration: NeonTheme.createNeonButton(
              primaryColor: widget.user.isAnonymous
                  ? NeonTheme.neonGreen
                  : Colors.red.withValues(alpha: 0.8),
              secondaryColor: widget.user.isAnonymous
                  ? NeonTheme.neonCyan
                  : Colors.red.withValues(alpha: 0.6),
            ),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                borderRadius: BorderRadius.circular(25),
                onTap: widget.onSignOut,
                child: Padding(
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        widget.user.isAnonymous ? Icons.upgrade : Icons.logout,
                        color: Colors.white,
                        size: 20,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        widget.user.isAnonymous ? 'Upgrade Account' : 'Sign Out',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  String _getInitials(String name) {
    if (name.isEmpty) return '?';

    final words = name.trim().split(' ');
    if (words.length == 1) {
      return words[0][0].toUpperCase();
    } else {
      return '${words[0][0]}${words[1][0]}'.toUpperCase();
    }
  }

  IconData _getProfilePictureIcon(String pictureId) {
    switch (pictureId) {
      case 'default':
        return Icons.person;
      case 'gamer':
        return Icons.games;
      case 'star':
        return Icons.star;
      case 'crown':
        return Icons.emoji_events;
      case 'diamond':
        return Icons.diamond;
      case 'fire':
        return Icons.local_fire_department;
      case 'rocket':
        return Icons.rocket_launch;
      case 'brain':
        return Icons.psychology;
      default:
        return Icons.person;
    }
  }

  String _getSkillTitle() {
    final gamesPlayed = widget.userModel.highScores.length;
    final totalScore = widget.userModel.highScores.values.fold(0, (sum, score) => sum + score);

    if (gamesPlayed == 0) return 'Newcomer';
    if (gamesPlayed < 5) return 'Rookie';
    if (gamesPlayed < 10) return 'Player';
    if (totalScore < 1000) return 'Gamer';
    if (totalScore < 5000) return 'Skilled Player';
    if (totalScore < 10000) return 'Expert';
    return 'Pro Gamer';
  }
}

class ParticlePatternPainter extends CustomPainter {
  final double animationValue;

  ParticlePatternPainter({required this.animationValue});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = NeonTheme.neonBlue.withValues(alpha: 0.1)
      ..style = PaintingStyle.fill;

    // Draw subtle animated particles
    for (int i = 0; i < 20; i++) {
      final x = (size.width * (i / 20)) + (animationValue * 50);
      final y = (size.height * 0.5) + (i % 3 - 1) * 20;

      if (x >= 0 && x <= size.width) {
        canvas.drawCircle(
          Offset(x % size.width, y),
          2 + (i % 3),
          paint,
        );
      }
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
