import 'game_state.dart';

/// Configuration for token reward calculation per game
class RewardConfig {
  final int scoreDivisor;      // s_div - score points per token
  final int timeSecPerToken;   // t_sec - seconds per time token
  final int timeTokenCap;      // t_cap - max time tokens per run
  final int successBonus;      // b_succ - bonus tokens for success
  final double difficultyMult; // m_diff - difficulty multiplier (0.0-0.15)
  final int minToken;          // g_min - minimum guaranteed tokens
  final GameAccessTier accessTier; // Game access tier
  final int tokenCost;         // Cost to play (for premium games)

  const RewardConfig({
    this.scoreDivisor = 150,
    this.timeSecPerToken = 90,
    this.timeTokenCap = 1,
    this.successBonus = 1,
    this.difficultyMult = 0.0,
    this.minToken = 1,
    this.accessTier = GameAccessTier.freeToStart,
    this.tokenCost = 0,
  });

  /// Create config for always free games
  factory RewardConfig.alwaysFree({
    int scoreDivisor = 150,
    int timeSecPerToken = 90,
    int timeTokenCap = 1,
    int successBonus = 1,
    double difficultyMult = 0.0,
    int minToken = 1,
  }) {
    return RewardConfig(
      scoreDivisor: scoreDivisor,
      timeSecPerToken: timeSecPerToken,
      timeTokenCap: timeTokenCap,
      successBonus: successBonus,
      difficultyMult: difficultyMult,
      minToken: minToken,
      accessTier: GameAccessTier.alwaysFree,
      tokenCost: 0,
    );
  }

  /// Create config for free-to-start games
  factory RewardConfig.freeToStart({
    int scoreDivisor = 150,
    int timeSecPerToken = 90,
    int timeTokenCap = 1,
    int successBonus = 1,
    double difficultyMult = 0.05,
    int minToken = 1,
  }) {
    return RewardConfig(
      scoreDivisor: scoreDivisor,
      timeSecPerToken: timeSecPerToken,
      timeTokenCap: timeTokenCap,
      successBonus: successBonus,
      difficultyMult: difficultyMult,
      minToken: minToken,
      accessTier: GameAccessTier.freeToStart,
      tokenCost: 3, // Cost for anonymous users after free plays
    );
  }

  /// Create config for premium arcade games
  factory RewardConfig.premiumArcade({
    int scoreDivisor = 150,
    int timeSecPerToken = 90,
    int timeTokenCap = 1,
    int successBonus = 1,
    double difficultyMult = 0.10,
    int minToken = 1,
    required int tokenCost,
  }) {
    return RewardConfig(
      scoreDivisor: scoreDivisor,
      timeSecPerToken: timeSecPerToken,
      timeTokenCap: timeTokenCap,
      successBonus: successBonus,
      difficultyMult: difficultyMult,
      minToken: minToken,
      accessTier: GameAccessTier.premiumArcade,
      tokenCost: tokenCost,
    );
  }

  /// Get payout multiplier based on access tier
  double get payoutMultiplier {
    switch (accessTier) {
      case GameAccessTier.alwaysFree:
        return 1.0;
      case GameAccessTier.freeToStart:
        return 1.1;
      case GameAccessTier.premiumArcade:
        return 1.2;
    }
  }
}

/// Session bonus configuration
class SessionBonusConfig {
  final double streakMultiplier;    // Daily streak bonus multiplier
  final int varietyBonus;           // Bonus for playing multiple games
  final int pityRescueBonus;        // Bonus for consecutive failures
  final int streakThreshold;        // Days needed for streak bonus
  final int varietyThreshold;       // Games needed for variety bonus
  final int pityThreshold;          // Failures needed for pity rescue

  const SessionBonusConfig({
    this.streakMultiplier = 0.10,
    this.varietyBonus = 2,
    this.pityRescueBonus = 2,
    this.streakThreshold = 3,
    this.varietyThreshold = 2,
    this.pityThreshold = 5,
  });
}

/// Token economy configuration for the entire system
class TokenEconomyConfig {
  final Map<String, RewardConfig> gameConfigs;
  final SessionBonusConfig sessionBonuses;
  final int dailyFreePlayLimit;
  final int maxDailyEarnings;
  final double earningsDampingFactor;
  final int earningsDampingThreshold;

  const TokenEconomyConfig({
    required this.gameConfigs,
    this.sessionBonuses = const SessionBonusConfig(),
    this.dailyFreePlayLimit = 3,
    this.maxDailyEarnings = 200,
    this.earningsDampingFactor = 0.8,
    this.earningsDampingThreshold = 200,
  });

  /// Get reward config for a specific game
  RewardConfig getGameConfig(String gameId) {
    return gameConfigs[gameId] ?? const RewardConfig();
  }

  /// Check if a game is always free
  bool isAlwaysFree(String gameId) {
    return getGameConfig(gameId).accessTier == GameAccessTier.alwaysFree;
  }

  /// Check if a game is free-to-start
  bool isFreeToStart(String gameId) {
    return getGameConfig(gameId).accessTier == GameAccessTier.freeToStart;
  }

  /// Check if a game is premium arcade
  bool isPremiumArcade(String gameId) {
    return getGameConfig(gameId).accessTier == GameAccessTier.premiumArcade;
  }

  /// Get token cost for playing a game
  int getPlayCost(String gameId, bool isAnonymous, int dailyPlayCount) {
    final config = getGameConfig(gameId);

    switch (config.accessTier) {
      case GameAccessTier.alwaysFree:
        return 0;
      case GameAccessTier.freeToStart:
        if (!isAnonymous) return 0; // Unlimited for registered users
        return dailyPlayCount < dailyFreePlayLimit ? 0 : config.tokenCost;
      case GameAccessTier.premiumArcade:
        return config.tokenCost;
    }
  }

  /// Get games by access tier
  List<String> getGamesByTier(GameAccessTier tier) {
    return gameConfigs.entries
        .where((entry) => entry.value.accessTier == tier)
        .map((entry) => entry.key)
        .toList();
  }
}

/// Result of a token reward calculation
class TokenRewardResult {
  final int baseTokens;
  final int timeBonus;
  final int successBonus;
  final int difficultyBonus;
  final int finalTokens;
  final String breakdown;

  const TokenRewardResult({
    required this.baseTokens,
    required this.timeBonus,
    required this.successBonus,
    required this.difficultyBonus,
    required this.finalTokens,
    required this.breakdown,
  });
}

/// Session tracking for bonus calculations
class GameSession {
  final DateTime startTime;
  final List<String> gamesPlayed;
  final List<bool> gameResults;
  final int totalTokensEarned;
  final bool hasStreakBonus;
  final bool hasVarietyBonus;
  final bool hasPityRescue;

  const GameSession({
    required this.startTime,
    required this.gamesPlayed,
    required this.gameResults,
    required this.totalTokensEarned,
    required this.hasStreakBonus,
    required this.hasVarietyBonus,
    required this.hasPityRescue,
  });

  /// Get unique games played in this session
  Set<String> get uniqueGamesPlayed => gamesPlayed.toSet();

  /// Get consecutive failures at the end of results
  int get consecutiveFailures {
    int failures = 0;
    for (int i = gameResults.length - 1; i >= 0; i--) {
      if (gameResults[i]) break; // Found a success, stop counting
      failures++;
    }
    return failures;
  }

  /// Check if variety bonus should be applied
  bool shouldGetVarietyBonus(SessionBonusConfig config) {
    return uniqueGamesPlayed.length >= config.varietyThreshold && !hasVarietyBonus;
  }

  /// Check if pity rescue should be applied
  bool shouldGetPityRescue(SessionBonusConfig config) {
    return consecutiveFailures >= config.pityThreshold && !hasPityRescue;
  }
}

/// Daily play tracking for anonymous users
class DailyPlayTracker {
  final Map<String, int> gamePlayCounts;
  final DateTime lastResetDate;
  final int totalPlaysToday;

  const DailyPlayTracker({
    required this.gamePlayCounts,
    required this.lastResetDate,
    required this.totalPlaysToday,
  });

  /// Get play count for a specific game today
  int getGamePlayCount(String gameId) {
    return gamePlayCounts[gameId] ?? 0;
  }

  /// Check if daily reset is needed
  bool needsReset() {
    final now = DateTime.now();
    final lastReset = lastResetDate;
    return now.day != lastReset.day || 
           now.month != lastReset.month || 
           now.year != lastReset.year;
  }

  /// Create a reset tracker for a new day
  DailyPlayTracker reset() {
    return DailyPlayTracker(
      gamePlayCounts: {},
      lastResetDate: DateTime.now(),
      totalPlaysToday: 0,
    );
  }

  /// Add a play for a specific game
  DailyPlayTracker addPlay(String gameId) {
    final newCounts = Map<String, int>.from(gamePlayCounts);
    newCounts[gameId] = (newCounts[gameId] ?? 0) + 1;
    
    return DailyPlayTracker(
      gamePlayCounts: newCounts,
      lastResetDate: lastResetDate,
      totalPlaysToday: totalPlaysToday + 1,
    );
  }
}
