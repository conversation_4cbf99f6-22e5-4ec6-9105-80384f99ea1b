/// Economy system for managing rewards, tokens, and progression in mini-games
library;

/// Represents a reward given to the player
class Reward {
  final int tokens;
  final int xp;
  final Map<String, dynamic> extras;
  
  const Reward(this.tokens, this.xp, [this.extras = const {}]);
  
  Reward copyWith({
    int? tokens,
    int? xp,
    Map<String, dynamic>? extras,
  }) {
    return Reward(
      tokens ?? this.tokens,
      xp ?? this.xp,
      extras ?? this.extras,
    );
  }
  
  @override
  String toString() => 'Reward(tokens: $tokens, xp: $xp, extras: $extras)';
}

/// Abstract base class for reward calculation policies
abstract class RewardPolicy {
  /// Calculate reward based on game performance
  Reward onFinish({
    required int score,
    required Duration duration,
    required bool success,
    int? level,
    int? combo,
    Map<String, dynamic>? gameSpecificData,
  });
}

/// Default reward policy implementation
class DefaultRewardPolicy implements RewardPolicy {
  final int baseTokens;
  final double scoreMultiplier;
  final int timeBonus;
  final int successBonus;
  final int maxTokens;
  
  const DefaultRewardPolicy({
    this.baseTokens = 1,
    this.scoreMultiplier = 0.1,
    this.timeBonus = 5,
    this.successBonus = 10,
    this.maxTokens = 100,
  });
  
  @override
  Reward onFinish({
    required int score,
    required Duration duration,
    required bool success,
    int? level,
    int? combo,
    Map<String, dynamic>? gameSpecificData,
  }) {
    int tokens = baseTokens;
    
    // Score-based tokens
    tokens += (score * scoreMultiplier).floor();
    
    // Success bonus
    if (success) {
      tokens += successBonus;
    }
    
    // Time bonus (faster completion = more tokens)
    if (success && duration.inSeconds > 0) {
      final timeMultiplier = (60 / duration.inSeconds).clamp(0.1, 2.0);
      tokens += (timeBonus * timeMultiplier).floor();
    }
    
    // Level bonus
    if (level != null && level > 1) {
      tokens += (level - 1) * 2;
    }
    
    // Combo bonus
    if (combo != null && combo > 1) {
      tokens += combo;
    }
    
    // Cap the maximum tokens
    tokens = tokens.clamp(1, maxTokens);
    
    // XP is typically equal to score
    final xp = score;
    
    return Reward(tokens, xp);
  }
}

/// Arcade-style reward policy with emphasis on high scores and combos
class ArcadeRewardPolicy implements RewardPolicy {
  final double scoreMultiplier;
  final int comboBonus;
  final int perfectBonus;
  final int maxTokens;
  
  const ArcadeRewardPolicy({
    this.scoreMultiplier = 0.05,
    this.comboBonus = 2,
    this.perfectBonus = 25,
    this.maxTokens = 150,
  });
  
  @override
  Reward onFinish({
    required int score,
    required Duration duration,
    required bool success,
    int? level,
    int? combo,
    Map<String, dynamic>? gameSpecificData,
  }) {
    int tokens = 1;
    
    // Score-based tokens
    tokens += (score * scoreMultiplier).floor();
    
    // Combo bonus
    if (combo != null && combo > 1) {
      tokens += combo * comboBonus;
    }
    
    // Perfect game bonus
    final isPerfect = gameSpecificData?['perfect'] == true;
    if (isPerfect) {
      tokens += perfectBonus;
    }
    
    // High score bonus
    final isHighScore = gameSpecificData?['high_score'] == true;
    if (isHighScore) {
      tokens += 20;
    }
    
    tokens = tokens.clamp(1, maxTokens);
    
    return Reward(tokens, score);
  }
}

/// Survival-style reward policy for endless games
class SurvivalRewardPolicy implements RewardPolicy {
  final int baseTokens;
  final double timeMultiplier;
  final int milestoneBonus;
  final List<int> milestones;
  final int maxTokens;
  
  const SurvivalRewardPolicy({
    this.baseTokens = 5,
    this.timeMultiplier = 0.1,
    this.milestoneBonus = 10,
    this.milestones = const [30, 60, 120, 300], // seconds
    this.maxTokens = 200,
  });
  
  @override
  Reward onFinish({
    required int score,
    required Duration duration,
    required bool success,
    int? level,
    int? combo,
    Map<String, dynamic>? gameSpecificData,
  }) {
    int tokens = baseTokens;
    
    // Time-based tokens
    tokens += (duration.inSeconds * timeMultiplier).floor();
    
    // Milestone bonuses
    for (final milestone in milestones) {
      if (duration.inSeconds >= milestone) {
        tokens += milestoneBonus;
      }
    }
    
    // Score bonus
    tokens += (score * 0.02).floor();
    
    tokens = tokens.clamp(1, maxTokens);
    
    return Reward(tokens, score);
  }
}

/// Puzzle-style reward policy for games with limited moves/time
class PuzzleRewardPolicy implements RewardPolicy {
  final int baseTokens;
  final int efficiencyBonus;
  final int speedBonus;
  final int maxTokens;
  
  const PuzzleRewardPolicy({
    this.baseTokens = 10,
    this.efficiencyBonus = 15,
    this.speedBonus = 10,
    this.maxTokens = 80,
  });
  
  @override
  Reward onFinish({
    required int score,
    required Duration duration,
    required bool success,
    int? level,
    int? combo,
    Map<String, dynamic>? gameSpecificData,
  }) {
    if (!success) {
      return const Reward(1, 0);
    }
    
    int tokens = baseTokens;
    
    // Efficiency bonus (fewer moves/attempts)
    final movesUsed = gameSpecificData?['moves_used'] as int?;
    final maxMoves = gameSpecificData?['max_moves'] as int?;
    if (movesUsed != null && maxMoves != null) {
      final efficiency = (maxMoves - movesUsed) / maxMoves;
      tokens += (efficiency * efficiencyBonus).floor();
    }
    
    // Speed bonus (faster completion)
    final targetTime = gameSpecificData?['target_time'] as Duration?;
    if (targetTime != null && duration < targetTime) {
      final speedRatio = targetTime.inSeconds / duration.inSeconds;
      tokens += (speedRatio * speedBonus).floor();
    }
    
    tokens = tokens.clamp(1, maxTokens);
    
    return Reward(tokens, score);
  }
}

/// Factory for creating appropriate reward policies
class RewardPolicyFactory {
  static RewardPolicy createForGameType(String gameType) {
    switch (gameType.toLowerCase()) {
      case 'arcade':
      case 'swish_shot':
      case 'tappy_footy':
      case 'rebounder':
        return const ArcadeRewardPolicy();
        
      case 'survival':
      case 'spike_loop':
      case 'flapster':
      case 'dash_rush':
        return const SurvivalRewardPolicy();
        
      case 'puzzle':
      case 'boom_sweep':
      case 'tile_twist':
      case 'shape_shift':
        return const PuzzleRewardPolicy();
        
      default:
        return const DefaultRewardPolicy();
    }
  }
}

/// Token economy manager
class TokenEconomy {
  static RewardPolicy _defaultPolicy = const DefaultRewardPolicy();
  static final Map<String, RewardPolicy> _gamePolicies = {};
  
  /// Set the default reward policy
  static void setDefaultPolicy(RewardPolicy policy) {
    _defaultPolicy = policy;
  }
  
  /// Set a specific policy for a game
  static void setGamePolicy(String gameId, RewardPolicy policy) {
    _gamePolicies[gameId] = policy;
  }
  
  /// Calculate reward for a game
  static Reward calculateReward({
    required String gameId,
    required int score,
    required Duration duration,
    required bool success,
    int? level,
    int? combo,
    Map<String, dynamic>? gameSpecificData,
  }) {
    final policy = _gamePolicies[gameId] ?? _defaultPolicy;
    
    return policy.onFinish(
      score: score,
      duration: duration,
      success: success,
      level: level,
      combo: combo,
      gameSpecificData: gameSpecificData,
    );
  }
}

/// Daily challenge multipliers and bonuses
class DailyChallenge {
  final String challengeId;
  final String gameId;
  final double tokenMultiplier;
  final int bonusTokens;
  final Map<String, dynamic> requirements;
  final DateTime expiresAt;
  
  const DailyChallenge({
    required this.challengeId,
    required this.gameId,
    this.tokenMultiplier = 1.5,
    this.bonusTokens = 20,
    this.requirements = const {},
    required this.expiresAt,
  });
  
  bool get isActive => DateTime.now().isBefore(expiresAt);
  
  bool meetsRequirements(Map<String, dynamic> gameData) {
    for (final entry in requirements.entries) {
      final key = entry.key;
      final requiredValue = entry.value;
      final actualValue = gameData[key];
      
      if (actualValue == null) return false;
      
      if (requiredValue is int && actualValue is int) {
        if (actualValue < requiredValue) return false;
      } else if (requiredValue is double && actualValue is num) {
        if (actualValue < requiredValue) return false;
      } else if (requiredValue != actualValue) {
        return false;
      }
    }
    
    return true;
  }
  
  Reward applyBonus(Reward baseReward, Map<String, dynamic> gameData) {
    if (!isActive || !meetsRequirements(gameData)) {
      return baseReward;
    }
    
    final multipliedTokens = (baseReward.tokens * tokenMultiplier).floor();
    final totalTokens = multipliedTokens + bonusTokens;
    
    return baseReward.copyWith(
      tokens: totalTokens,
      extras: {
        ...baseReward.extras,
        'daily_challenge_bonus': bonusTokens,
        'daily_challenge_multiplier': tokenMultiplier,
      },
    );
  }
}
