import 'dart:math';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../../../core/models/game_state.dart';
import '../../../core/providers/game_providers.dart';
import '../../../core/services/game_feedback_service.dart';
import '../../../shared/widgets/base_game_widget.dart';

class WriggleGame extends BaseGameWidget {
  static const GameConfig gameConfig = GameConfig(
    gameId: 'wriggle',
    gameName: 'Wriggle',
    scoreMultiplier: 1,
    hasLives: false,
    hasLevels: false,
    hasTimer: false,
  );

  const WriggleGame({
    super.key,
    super.onGameComplete,
    super.onGameExit,
  }) : super(config: gameConfig);

  @override
  Widget buildGameContent(BuildContext context, WidgetRef ref, GameState gameState) {
    return const _WriggleGameContent();
  }
}

enum Direction { up, down, left, right }

class Position {
  final int x;
  final int y;

  Position(this.x, this.y);

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Position && other.x == x && other.y == y;
  }

  @override
  int get hashCode => x.hashCode ^ y.hashCode;
}

class _WriggleGameContent extends ConsumerStatefulWidget {
  const _WriggleGameContent();

  @override
  ConsumerState<_WriggleGameContent> createState() => _WriggleGameContentState();
}

class _WriggleGameContentState extends ConsumerState<_WriggleGameContent>
    with TickerProviderStateMixin {
  
  // Game state
  late AnimationController _eatController;
  late AnimationController _growController;
  
  // Snake state
  final List<Position> _snake = [Position(10, 10)];
  Direction _direction = Direction.right;
  Direction? _nextDirection;
  
  // Food state
  Position _food = Position(15, 15);
  bool _showEatEffect = false;
  
  // Game mechanics
  final int _gridWidth = 20;
  final int _gridHeight = 25;
  final double _cellSize = 18;
  double _gameSpeed = 0.3; // seconds per move
  double _moveTimer = 0;
  final Random _random = Random();
  
  // Constants
  static const double _gameWidth = 360; // 20 * 18
  static const double _gameHeight = 450; // 25 * 18

  @override
  void initState() {
    super.initState();
    
    _eatController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _growController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    
    _generateFood();
    _startGameLoop();
  }

  @override
  void dispose() {
    _eatController.dispose();
    _growController.dispose();
    super.dispose();
  }

  void _generateFood() {
    Position newFood;
    do {
      newFood = Position(
        _random.nextInt(_gridWidth),
        _random.nextInt(_gridHeight),
      );
    } while (_snake.contains(newFood));
    
    setState(() {
      _food = newFood;
    });
  }

  void _startGameLoop() {
    const frameRate = 60;
    const frameDuration = Duration(milliseconds: 1000 ~/ frameRate);
    const deltaTime = 1.0 / frameRate;
    
    void gameLoop() {
      if (!mounted) return;
      
      final gameState = ref.read(gameStateProvider(WriggleGame.gameConfig));
      if (!gameState.isPlaying) {
        Future.delayed(frameDuration, gameLoop);
        return;
      }
      
      setState(() {
        _moveTimer += deltaTime;
        
        if (_moveTimer >= _gameSpeed) {
          _moveSnake();
          _moveTimer = 0;
        }
      });
      
      Future.delayed(frameDuration, gameLoop);
    }
    
    gameLoop();
  }

  void _moveSnake() {
    // Update direction if there's a queued direction change
    if (_nextDirection != null) {
      _direction = _nextDirection!;
      _nextDirection = null;
    }
    
    // Calculate new head position
    final head = _snake.first;
    Position newHead;
    
    switch (_direction) {
      case Direction.up:
        newHead = Position(head.x, head.y - 1);
        break;
      case Direction.down:
        newHead = Position(head.x, head.y + 1);
        break;
      case Direction.left:
        newHead = Position(head.x - 1, head.y);
        break;
      case Direction.right:
        newHead = Position(head.x + 1, head.y);
        break;
    }
    
    // Check wall collision
    if (newHead.x < 0 || newHead.x >= _gridWidth ||
        newHead.y < 0 || newHead.y >= _gridHeight) {
      _gameOver('Hit wall');
      return;
    }
    
    // Check self collision
    if (_snake.contains(newHead)) {
      _gameOver('Hit self');
      return;
    }
    
    // Add new head
    _snake.insert(0, newHead);
    
    // Check food collision
    if (newHead == _food) {
      _eatFood();
    } else {
      // Remove tail if no food eaten
      _snake.removeLast();
    }
  }

  void _eatFood() {
    // Add score
    final gameNotifier = ref.read(gameStateProvider(WriggleGame.gameConfig).notifier);
    gameNotifier.addScore(10);
    
    // Increase speed slightly
    _gameSpeed = (_gameSpeed * 0.95).clamp(0.1, 0.5);
    
    // Show eat effect
    setState(() {
      _showEatEffect = true;
    });
    
    // Trigger feedback
    ref.read(gameFeedbackServiceProvider).triggerFeedback(GameEvent.scoreIncrease);
    
    // Play animations
    _eatController.forward().then((_) {
      _eatController.reset();
      setState(() {
        _showEatEffect = false;
      });
    });
    
    _growController.forward().then((_) => _growController.reset());
    
    // Generate new food
    _generateFood();
  }

  void _gameOver(String reason) {
    final gameNotifier = ref.read(gameStateProvider(WriggleGame.gameConfig).notifier);
    gameNotifier.endGame(reason: reason);
  }

  void _onPanEnd(DragEndDetails details) {
    final gameState = ref.read(gameStateProvider(WriggleGame.gameConfig));
    if (!gameState.isPlaying) return;
    
    final velocity = details.velocity.pixelsPerSecond;
    final dx = velocity.dx.abs();
    final dy = velocity.dy.abs();
    
    // Determine swipe direction
    Direction? newDirection;
    
    if (dx > dy) {
      // Horizontal swipe
      if (velocity.dx > 0) {
        newDirection = Direction.right;
      } else {
        newDirection = Direction.left;
      }
    } else {
      // Vertical swipe
      if (velocity.dy > 0) {
        newDirection = Direction.down;
      } else {
        newDirection = Direction.up;
      }
    }
    
    // Prevent reversing into self
    if (!_isOppositeDirection(newDirection, _direction)) {
      _nextDirection = newDirection;
    }
  }

  bool _isOppositeDirection(Direction dir1, Direction dir2) {
    return (dir1 == Direction.up && dir2 == Direction.down) ||
           (dir1 == Direction.down && dir2 == Direction.up) ||
           (dir1 == Direction.left && dir2 == Direction.right) ||
           (dir1 == Direction.right && dir2 == Direction.left);
  }

  @override
  Widget build(BuildContext context) {
    final gameState = ref.watch(gameStateProvider(WriggleGame.gameConfig));
    
    return Container(
      width: _gameWidth,
      height: _gameHeight,
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [Color(0xFF0d4f3c), Color(0xFF1a5f4a)],
        ),
      ),
      child: GestureDetector(
        onPanEnd: _onPanEnd,
        child: Stack(
          children: [
            // Grid background
            _buildGrid(),
            
            // Snake
            _buildSnake(),
            
            // Food
            _buildFood(),
            
            // Eat effect
            if (_showEatEffect) _buildEatEffect(),
            
            // Game info
            _buildGameInfo(gameState),
            
            // Instructions
            if (!gameState.isPlaying) _buildInstructions(),
          ],
        ),
      ),
    );
  }

  Widget _buildGrid() {
    return Positioned.fill(
      child: CustomPaint(
        painter: _GridPainter(_gridWidth, _gridHeight, _cellSize),
      ),
    );
  }

  Widget _buildSnake() {
    return Stack(
      children: _snake.asMap().entries.map((entry) {
        final index = entry.key;
        final segment = entry.value;
        final isHead = index == 0;
        
        return Positioned(
          left: segment.x * _cellSize + 1,
          top: segment.y * _cellSize + 1,
          child: ScaleTransition(
            scale: isHead 
                ? Tween<double>(begin: 1.0, end: 1.1).animate(_growController)
                : const AlwaysStoppedAnimation(1.0),
            child: Container(
              width: _cellSize - 2,
              height: _cellSize - 2,
              decoration: BoxDecoration(
                color: isHead ? Colors.lightGreen[400] : Colors.green[600],
                borderRadius: BorderRadius.circular(isHead ? 8 : 4),
                border: Border.all(
                  color: isHead ? Colors.white : Colors.green[800]!,
                  width: 1,
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.green.withOpacity(0.3),
                    blurRadius: 2,
                    spreadRadius: 1,
                  ),
                ],
              ),
              child: isHead ? const Center(
                child: Text(
                  '👁',
                  style: TextStyle(fontSize: 8),
                ),
              ) : null,
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildFood() {
    return Positioned(
      left: _food.x * _cellSize + 1,
      top: _food.y * _cellSize + 1,
      child: Container(
        width: _cellSize - 2,
        height: _cellSize - 2,
        decoration: BoxDecoration(
          gradient: RadialGradient(
            colors: [Colors.red[400]!, Colors.red[700]!],
          ),
          shape: BoxShape.circle,
          boxShadow: [
            BoxShadow(
              color: Colors.red.withOpacity(0.6),
              blurRadius: 4,
              spreadRadius: 2,
            ),
          ],
        ),
        child: const Center(
          child: Text(
            '🍎',
            style: TextStyle(fontSize: 10),
          ),
        ),
      ).animate(onPlay: (controller) => controller.repeat())
       .shimmer(duration: 2000.ms, color: Colors.white.withOpacity(0.3)),
    );
  }

  Widget _buildEatEffect() {
    return Positioned(
      left: _food.x * _cellSize - 10,
      top: _food.y * _cellSize - 10,
      child: ScaleTransition(
        scale: _eatController,
        child: Container(
          width: _cellSize + 20,
          height: _cellSize + 20,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            gradient: RadialGradient(
              colors: [Colors.yellow, Colors.orange, Colors.transparent],
              stops: const [0.0, 0.5, 1.0],
            ),
          ),
          child: const Center(
            child: Text(
              '+10',
              style: TextStyle(
                color: Colors.white,
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildGameInfo(GameState gameState) {
    return Positioned(
      top: 10,
      left: 10,
      right: 10,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Container(
            padding: const EdgeInsets.all(6),
            decoration: BoxDecoration(
              color: Colors.black54,
              borderRadius: BorderRadius.circular(6),
            ),
            child: Column(
              children: [
                const Text(
                  'Score',
                  style: TextStyle(color: Colors.white, fontSize: 10),
                ),
                Text(
                  '${gameState.score}',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.all(6),
            decoration: BoxDecoration(
              color: Colors.black54,
              borderRadius: BorderRadius.circular(6),
            ),
            child: Column(
              children: [
                const Text(
                  'Length',
                  style: TextStyle(color: Colors.white, fontSize: 10),
                ),
                Text(
                  '${_snake.length}',
                  style: const TextStyle(
                    color: Colors.green,
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInstructions() {
    return Positioned(
      bottom: 50,
      left: 0,
      right: 0,
      child: Center(
        child: Container(
          padding: EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.black54,
            borderRadius: BorderRadius.all(Radius.circular(8)),
          ),
          child: Text(
            'Swipe to change direction!\nEat apples to grow longer.\nDon\'t hit walls or yourself!',
            textAlign: TextAlign.center,
            style: TextStyle(
              color: Colors.white,
              fontSize: 14,
            ),
          ),
        ),
      ),
    );
  }
}

class _GridPainter extends CustomPainter {
  final int gridWidth;
  final int gridHeight;
  final double cellSize;

  _GridPainter(this.gridWidth, this.gridHeight, this.cellSize);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.green.withOpacity(0.1)
      ..strokeWidth = 0.5
      ..style = PaintingStyle.stroke;
    
    // Draw vertical lines
    for (int i = 0; i <= gridWidth; i++) {
      final x = i * cellSize;
      canvas.drawLine(
        Offset(x, 0),
        Offset(x, gridHeight * cellSize),
        paint,
      );
    }
    
    // Draw horizontal lines
    for (int i = 0; i <= gridHeight; i++) {
      final y = i * cellSize;
      canvas.drawLine(
        Offset(0, y),
        Offset(gridWidth * cellSize, y),
        paint,
      );
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
