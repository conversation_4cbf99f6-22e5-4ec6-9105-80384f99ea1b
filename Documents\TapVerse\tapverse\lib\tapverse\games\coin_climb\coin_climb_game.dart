import 'dart:math' as math;
import 'package:flame/components.dart';
import 'package:flame/collisions.dart';
import 'package:flutter/material.dart';
import '../../core/mini_game_base.dart';
import '../../core/hud.dart';
import '../../core/effects.dart';
import '../../core/analytics.dart';
import '../../core/difficulty.dart';

/// CoinClimb - Vertical climbing game with wall jumping
/// Controls: Swipe left/right to leap to next wall/ladder
/// Scoring: +1 per vertical segment; +2 coin
class CoinClimbGame extends MiniGameBase {
  CoinClimbGame(int seed) : super(modeId: 'coin_climb', sessionSeed: seed) {
    rng = math.Random(seed);
  }

  late math.Random rng;
  late ScoreText scoreText;
  
  // Game state
  int score = 0;
  int segmentsClimbed = 0;
  int coinsCollected = 0;
  
  // Game entities
  late Climber climber;
  late List<Wall> walls;
  late List<Coin> coins;
  late List<Trap> traps;
  
  // Difficulty curves (from planning document)
  final gripSpacingCurve = const CurveParam(start: 120, max: 180, perMinute: 30);
  final trapRateCurve = const CurveParam(start: 0.1, max: 0.4, perMinute: 0.15);
  
  // Difficulty manager
  late DifficultyManager difficultyManager;
  
  // Level generation
  double nextWallY = 1000;
  double currentHeight = 0;
  static const double segmentHeight = 150;
  bool pendingMove = false;
  double moveTimer = 0;

  @override
  Future<void> loadAssets() async {
    // Load any required assets here
  }

  @override
  void setupWorld() {
    camera.viewfinder.visibleGameSize = Vector2(720, 1280);
    
    // Initialize difficulty manager
    difficultyManager = DifficultyManager();
    difficultyManager.addParameter('grip_spacing', gripSpacingCurve);
    difficultyManager.addParameter('trap_rate', trapRateCurve);
    
    // Initialize collections
    walls = [];
    coins = [];
    traps = [];
    
    // Create game entities
    _createGameEntities();
  }

  void _createGameEntities() {
    // Create climber
    climber = Climber(
      position: Vector2(360, 1200),
      onGripMissed: _onGripMissed,
      onTrapHit: _onTrapHit,
      onCoinCollected: _onCoinCollected,
    );
    add(climber);

    // Generate initial level
    _generateInitialLevel();
  }

  void _generateInitialLevel() {
    // Generate starting walls
    for (int i = 0; i < 10; i++) {
      _generateWallSegment();
    }
  }

  void _generateWallSegment() {
    final gripSpacing = difficultyManager.getValue('grip_spacing');
    final trapRate = difficultyManager.getValue('trap_rate');
    
    // Create left and right walls
    final leftWall = Wall(
      position: Vector2(100, nextWallY),
      isLeft: true,
      gripSpacing: gripSpacing,
    );
    
    final rightWall = Wall(
      position: Vector2(620, nextWallY),
      isLeft: false,
      gripSpacing: gripSpacing,
    );
    
    walls.addAll([leftWall, rightWall]);
    add(leftWall);
    add(rightWall);
    
    // Add coins randomly
    if (rng.nextDouble() < 0.3) {
      final coin = Coin(
        position: Vector2(360, nextWallY - 50),
      );
      coins.add(coin);
      add(coin);
    }
    
    // Add traps based on difficulty
    if (rng.nextDouble() < trapRate) {
      final trapSide = rng.nextBool();
      final trap = Trap(
        position: Vector2(trapSide ? 150 : 570, nextWallY - 30),
      );
      traps.add(trap);
      add(trap);
    }
    
    nextWallY -= segmentHeight;
  }

  @override
  void setupHUD() {
    // Score display
    scoreText = ScoreText(
      position: Vector2(16, 16),
      textStyle: const TextStyle(
        color: Colors.white,
        fontSize: 24,
        fontWeight: FontWeight.bold,
      ),
    );
    add(scoreText);
  }

  @override
  void onStart() {
    score = 0;
    segmentsClimbed = 0;
    coinsCollected = 0;
    currentHeight = 0;
    
    // Start difficulty progression
    difficultyManager.start();
    
    // Start the game
    startGame();
    
    Analytics.log('coin_climb_start', {'session_seed': sessionSeed});
  }

  @override
  void onGameUpdate(double dt) {
    // Handle pending moves
    if (pendingMove) {
      moveTimer += dt;
      if (moveTimer >= 0.15) { // 150ms window for moves
        pendingMove = false;
        moveTimer = 0;
      }
    }
    
    // Generate new segments as climber progresses
    if (climber.position.y < nextWallY + 1000) {
      _generateWallSegment();
    }
    
    // Clean up old entities
    _cleanupOldEntities();
    
    // Update camera to follow climber
    camera.viewfinder.position = Vector2(360, climber.position.y + 400);
    
    // Check for height progress
    final newHeight = 1200 - climber.position.y;
    if (newHeight > currentHeight + segmentHeight) {
      currentHeight = newHeight;
      _onSegmentClimbed();
    }
  }

  void _cleanupOldEntities() {
    // Remove entities far below climber
    walls.removeWhere((wall) {
      if (wall.position.y > climber.position.y + 800) {
        wall.removeFromParent();
        return true;
      }
      return false;
    });
    
    coins.removeWhere((coin) {
      if (coin.position.y > climber.position.y + 800) {
        coin.removeFromParent();
        return true;
      }
      return false;
    });
    
    traps.removeWhere((trap) {
      if (trap.position.y > climber.position.y + 800) {
        trap.removeFromParent();
        return true;
      }
      return false;
    });
  }

  @override
  bool onSwipeEnd(Swipe swipe) {
    if (isGameEnded || pendingMove) return false;
    
    // Queue move based on swipe direction
    if (swipe.isLeft) {
      _queueMove(true);
    } else if (swipe.isRight) {
      _queueMove(false);
    }
    
    return true;
  }

  void _queueMove(bool moveLeft) {
    pendingMove = true;
    moveTimer = 0;
    climber.queueMove(moveLeft);
    
    Analytics.log('move_queued', {
      'direction': moveLeft ? 'left' : 'right',
      'height': currentHeight,
    });
  }

  void _onSegmentClimbed() {
    segmentsClimbed++;
    score += 1;
    addScore(1);
    scoreText.updateScore(score);
    
    // Visual effects
    add(Effects.textPopup(
      climber.position + Vector2(0, -30),
      '+1',
      color: Colors.green,
    ));
    
    Analytics.log('segment_climbed', {
      'segments_climbed': segmentsClimbed,
      'height': currentHeight,
    });
  }

  void _onCoinCollected() {
    coinsCollected++;
    score += 2;
    addScore(2);
    scoreText.updateScore(score);
    
    // Visual effects
    add(Effects.sparkle(climber.position, colors: [Colors.yellow, Colors.orange]));
    add(Effects.textPopup(
      climber.position + Vector2(0, -30),
      '+2',
      color: Colors.yellow,
    ));
    
    Analytics.log('coin_collected', {
      'coins_collected': coinsCollected,
      'score': score,
    });
  }

  void _onGripMissed() {
    // Climber missed grip - game over
    endGame(success: false, score: score);
    
    Analytics.log('grip_missed', {
      'final_score': score,
      'segments_climbed': segmentsClimbed,
      'coins_collected': coinsCollected,
    });
  }

  void _onTrapHit() {
    // Climber hit trap - game over
    endGame(success: false, score: score);
    
    Analytics.log('trap_hit', {
      'final_score': score,
      'segments_climbed': segmentsClimbed,
    });
  }


  @override
  void reportScore(int score) {
    // Integration hook for TapVerse UI
  }

  @override
  void awardTokens(int tokens) {
    // Integration hook for TapVerse token system
  }
}

/// Climber component with auto-jump mechanics
class Climber extends RectangleComponent with HasCollisionDetection, CollisionCallbacks {
  Climber({
    required super.position,
    required this.onGripMissed,
    required this.onTrapHit,
    required this.onCoinCollected,
  }) : super(size: Vector2(30, 30));

  final VoidCallback onGripMissed;
  final VoidCallback onTrapHit;
  final VoidCallback onCoinCollected;

  bool isOnLeftWall = true;
  bool hasPendingMove = false;
  bool pendingMoveLeft = false;
  Vector2 velocity = Vector2.zero();
  bool isJumping = false;
  static const double jumpSpeed = 300;
  static const double climbSpeed = 100;

  @override
  Future<void> onLoad() async {
    add(RectangleHitbox());
    paint = Paint()..color = Colors.blue;
  }

  @override
  void update(double dt) {
    super.update(dt);
    
    if (isJumping) {
      // Apply jump physics
      position += velocity * dt;
      velocity.y += 800 * dt; // Gravity
      
      // Check if landed on wall
      if (velocity.y > 0) {
        _checkWallLanding();
      }
    } else {
      // Auto-climb upward
      position.y -= climbSpeed * dt;
      
      // Execute pending move
      if (hasPendingMove) {
        _executePendingMove();
      }
    }
  }

  void queueMove(bool moveLeft) {
    hasPendingMove = true;
    pendingMoveLeft = moveLeft;
  }

  void _executePendingMove() {
    hasPendingMove = false;
    
    // Jump to opposite wall
    isJumping = true;
    isOnLeftWall = !pendingMoveLeft;
    
    final targetX = isOnLeftWall ? 100.0 : 620.0;
    final deltaX = targetX - position.x;
    
    velocity = Vector2(deltaX * 2, -jumpSpeed);
  }

  void _checkWallLanding() {
    final targetX = isOnLeftWall ? 100.0 : 620.0;
    
    if ((isOnLeftWall && position.x <= targetX + 20) ||
        (!isOnLeftWall && position.x >= targetX - 20)) {
      // Landed on wall
      position.x = targetX;
      velocity = Vector2.zero();
      isJumping = false;
    }
  }

  @override
  bool onCollisionStart(Set<Vector2> intersectionPoints, PositionComponent other) {
    if (other is Coin) {
      other.removeFromParent();
      onCoinCollected();
      return true;
    } else if (other is Trap) {
      onTrapHit();
      return true;
    }
    return false;
  }
}

/// Wall component with grips
class Wall extends RectangleComponent with HasCollisionDetection {
  Wall({
    required super.position,
    required this.isLeft,
    required this.gripSpacing,
  }) : super(size: Vector2(20, 150));

  final bool isLeft;
  final double gripSpacing;

  @override
  Future<void> onLoad() async {
    add(RectangleHitbox());
    paint = Paint()..color = Colors.brown;
  }
}

/// Coin collectible
class Coin extends CircleComponent with HasCollisionDetection {
  Coin({required super.position}) : super(radius: 15);

  @override
  Future<void> onLoad() async {
    add(CircleHitbox());
    paint = Paint()..color = Colors.yellow;
  }
}

/// Trap obstacle
class Trap extends RectangleComponent with HasCollisionDetection {
  Trap({required super.position}) : super(size: Vector2(40, 20));

  @override
  Future<void> onLoad() async {
    add(RectangleHitbox());
    paint = Paint()..color = Colors.red;
  }
}
