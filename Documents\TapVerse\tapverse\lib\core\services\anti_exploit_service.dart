import 'package:shared_preferences/shared_preferences.dart';

import '../models/token_economy.dart';
import 'auth_service.dart';
import 'token_service.dart';

/// Types of potential exploits to detect and prevent
enum ExploitType {
  rapidPlay,        // Playing games too quickly
  idleFarming,      // Staying in games too long without progress
  tokenManipulation, // Unusual token patterns
  timeManipulation, // System clock manipulation
  excessiveEarnings, // Earning too many tokens too quickly
}

/// Result of an exploit detection check
class ExploitCheckResult {
  final bool isExploit;
  final ExploitType? type;
  final String reason;
  final int severity; // 1-10 scale
  final Map<String, dynamic> metadata;
  final Duration? cooldownPeriod;

  const ExploitCheckResult({
    required this.isExploit,
    this.type,
    required this.reason,
    required this.severity,
    this.metadata = const {},
    this.cooldownPeriod,
  });

  factory ExploitCheckResult.clean(String reason) {
    return ExploitCheckResult(
      isExploit: false,
      reason: reason,
      severity: 0,
    );
  }

  factory ExploitCheckResult.exploit({
    required ExploitType type,
    required String reason,
    required int severity,
    Map<String, dynamic>? metadata,
    Duration? cooldown,
  }) {
    return ExploitCheckResult(
      isExploit: true,
      type: type,
      reason: reason,
      severity: severity,
      metadata: metadata ?? {},
      cooldownPeriod: cooldown,
    );
  }
}

/// Service for detecting and preventing token economy exploits
class AntiExploitService {
  final AuthService _authService;
  final TokenService _tokenService;

  // Configuration constants
  static const int maxTokensPerHour = 120;
  static const int maxTokensPerDay = 400;
  static const int minGameDuration = 10; // seconds
  static const int maxGameDuration = 600; // 10 minutes
  static const int maxGamesPerMinute = 6;
  static const int maxConsecutiveGames = 50;
  static const Duration cooldownPeriod = Duration(minutes: 15);

  AntiExploitService({
    required AuthService authService,
    required TokenService tokenService,
  }) : _authService = authService,
       _tokenService = tokenService;

  /// Check for potential exploits before awarding tokens
  Future<ExploitCheckResult> checkForExploits({
    required String gameId,
    required int score,
    required int durationSec,
    required int tokensToAward,
  }) async {
    try {
      final user = _authService.currentUser;
      if (user == null) {
        return ExploitCheckResult.clean('No user authenticated');
      }

      // Check rapid play exploit
      final rapidPlayCheck = await _checkRapidPlay(gameId);
      if (rapidPlayCheck.isExploit) return rapidPlayCheck;

      // Check idle farming exploit
      final idleFarmingCheck = _checkIdleFarming(durationSec, score);
      if (idleFarmingCheck.isExploit) return idleFarmingCheck;

      // Check excessive earnings
      final earningsCheck = await _checkExcessiveEarnings(tokensToAward);
      if (earningsCheck.isExploit) return earningsCheck;

      // Check time manipulation
      final timeCheck = await _checkTimeManipulation();
      if (timeCheck.isExploit) return timeCheck;

      // Check token manipulation patterns
      final tokenCheck = await _checkTokenManipulation(tokensToAward);
      if (tokenCheck.isExploit) return tokenCheck;

      return ExploitCheckResult.clean('All checks passed');
    } catch (e) {
      print('Error in exploit check: $e');
      return ExploitCheckResult.clean('Error in validation');
    }
  }

  /// Check for rapid play patterns
  Future<ExploitCheckResult> _checkRapidPlay(String gameId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final key = 'game_times_$gameId';
      final gameTimesStr = prefs.getString(key) ?? '';
      
      final now = DateTime.now();
      final recentTimes = <DateTime>[];
      
      // Parse existing game times
      if (gameTimesStr.isNotEmpty) {
        for (final timeStr in gameTimesStr.split(',')) {
          try {
            final time = DateTime.parse(timeStr);
            // Only keep times from the last hour
            if (now.difference(time).inHours < 1) {
              recentTimes.add(time);
            }
          } catch (e) {
            // Skip invalid timestamps
          }
        }
      }

      // Add current time
      recentTimes.add(now);

      // Check for too many games in a short period
      final gamesInLastMinute = recentTimes
          .where((time) => now.difference(time).inMinutes < 1)
          .length;

      if (gamesInLastMinute > maxGamesPerMinute) {
        return ExploitCheckResult.exploit(
          type: ExploitType.rapidPlay,
          reason: 'Too many games played in the last minute: $gamesInLastMinute',
          severity: 7,
          metadata: {'gamesInLastMinute': gamesInLastMinute},
          cooldown: cooldownPeriod,
        );
      }

      // Check for excessive consecutive games
      if (recentTimes.length > maxConsecutiveGames) {
        return ExploitCheckResult.exploit(
          type: ExploitType.rapidPlay,
          reason: 'Too many consecutive games: ${recentTimes.length}',
          severity: 8,
          metadata: {'consecutiveGames': recentTimes.length},
          cooldown: Duration(hours: 1),
        );
      }

      // Save updated game times
      final updatedTimesStr = recentTimes
          .map((time) => time.toIso8601String())
          .join(',');
      await prefs.setString(key, updatedTimesStr);

      return ExploitCheckResult.clean('Rapid play check passed');
    } catch (e) {
      print('Error checking rapid play: $e');
      return ExploitCheckResult.clean('Rapid play check error');
    }
  }

  /// Check for idle farming (staying in game too long without progress)
  ExploitCheckResult _checkIdleFarming(int durationSec, int score) {
    // Game too short (likely automated)
    if (durationSec < minGameDuration) {
      return ExploitCheckResult.exploit(
        type: ExploitType.idleFarming,
        reason: 'Game duration too short: ${durationSec}s',
        severity: 6,
        metadata: {'duration': durationSec, 'minRequired': minGameDuration},
      );
    }

    // Game too long (likely idle farming)
    if (durationSec > maxGameDuration) {
      return ExploitCheckResult.exploit(
        type: ExploitType.idleFarming,
        reason: 'Game duration too long: ${durationSec}s',
        severity: 5,
        metadata: {'duration': durationSec, 'maxAllowed': maxGameDuration},
      );
    }

    // Very low score for long duration (idle farming)
    final scorePerSecond = score / durationSec;
    if (durationSec > 60 && scorePerSecond < 0.1) {
      return ExploitCheckResult.exploit(
        type: ExploitType.idleFarming,
        reason: 'Score too low for duration: $scorePerSecond points/sec',
        severity: 4,
        metadata: {
          'scorePerSecond': scorePerSecond,
          'duration': durationSec,
          'score': score,
        },
      );
    }

    return ExploitCheckResult.clean('Idle farming check passed');
  }

  /// Check for excessive token earnings
  Future<ExploitCheckResult> _checkExcessiveEarnings(int tokensToAward) async {
    try {
      final user = _authService.currentUser;
      if (user == null) return ExploitCheckResult.clean('No user');

      final prefs = await SharedPreferences.getInstance();
      final now = DateTime.now();
      
      // Check hourly earnings
      final hourlyKey = 'hourly_earnings_${now.hour}_${now.day}_${now.month}';
      final hourlyEarnings = prefs.getInt(hourlyKey) ?? 0;
      
      if (hourlyEarnings + tokensToAward > maxTokensPerHour) {
        return ExploitCheckResult.exploit(
          type: ExploitType.excessiveEarnings,
          reason: 'Hourly token limit exceeded: ${hourlyEarnings + tokensToAward}',
          severity: 6,
          metadata: {
            'currentHourly': hourlyEarnings,
            'tokensToAward': tokensToAward,
            'limit': maxTokensPerHour,
          },
          cooldown: Duration(minutes: 60 - now.minute),
        );
      }

      // Check daily earnings
      final dailyKey = 'daily_earnings_${now.day}_${now.month}_${now.year}';
      final dailyEarnings = prefs.getInt(dailyKey) ?? 0;
      
      if (dailyEarnings + tokensToAward > maxTokensPerDay) {
        return ExploitCheckResult.exploit(
          type: ExploitType.excessiveEarnings,
          reason: 'Daily token limit exceeded: ${dailyEarnings + tokensToAward}',
          severity: 8,
          metadata: {
            'currentDaily': dailyEarnings,
            'tokensToAward': tokensToAward,
            'limit': maxTokensPerDay,
          },
          cooldown: Duration(hours: 24 - now.hour),
        );
      }

      // Update earnings tracking
      await prefs.setInt(hourlyKey, hourlyEarnings + tokensToAward);
      await prefs.setInt(dailyKey, dailyEarnings + tokensToAward);

      return ExploitCheckResult.clean('Earnings check passed');
    } catch (e) {
      print('Error checking excessive earnings: $e');
      return ExploitCheckResult.clean('Earnings check error');
    }
  }

  /// Check for time manipulation (system clock changes)
  Future<ExploitCheckResult> _checkTimeManipulation() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final now = DateTime.now();
      final lastCheckStr = prefs.getString('last_time_check');
      
      if (lastCheckStr != null) {
        final lastCheck = DateTime.parse(lastCheckStr);
        final timeDiff = now.difference(lastCheck);
        
        // Check for time going backwards (clock manipulation)
        if (timeDiff.isNegative) {
          return ExploitCheckResult.exploit(
            type: ExploitType.timeManipulation,
            reason: 'System time went backwards',
            severity: 9,
            metadata: {'timeDifference': timeDiff.inSeconds},
            cooldown: Duration(hours: 24),
          );
        }
        
        // Check for unrealistic time jumps forward
        if (timeDiff.inHours > 25) {
          return ExploitCheckResult.exploit(
            type: ExploitType.timeManipulation,
            reason: 'Unrealistic time jump forward: ${timeDiff.inHours} hours',
            severity: 7,
            metadata: {'timeDifference': timeDiff.inHours},
            cooldown: Duration(hours: 1),
          );
        }
      }

      await prefs.setString('last_time_check', now.toIso8601String());
      return ExploitCheckResult.clean('Time manipulation check passed');
    } catch (e) {
      print('Error checking time manipulation: $e');
      return ExploitCheckResult.clean('Time check error');
    }
  }

  /// Check for token manipulation patterns
  Future<ExploitCheckResult> _checkTokenManipulation(int tokensToAward) async {
    try {
      // Check for unrealistic token amounts
      if (tokensToAward > 50) {
        return ExploitCheckResult.exploit(
          type: ExploitType.tokenManipulation,
          reason: 'Token amount too high: $tokensToAward',
          severity: 9,
          metadata: {'tokensToAward': tokensToAward},
        );
      }

      if (tokensToAward < 0) {
        return ExploitCheckResult.exploit(
          type: ExploitType.tokenManipulation,
          reason: 'Negative token amount: $tokensToAward',
          severity: 10,
          metadata: {'tokensToAward': tokensToAward},
        );
      }

      return ExploitCheckResult.clean('Token manipulation check passed');
    } catch (e) {
      print('Error checking token manipulation: $e');
      return ExploitCheckResult.clean('Token manipulation check error');
    }
  }

  /// Apply soft cap to daily earnings
  Future<int> applySoftCap(int tokensToAward) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final now = DateTime.now();
      final dailyKey = 'daily_earnings_${now.day}_${now.month}_${now.year}';
      final dailyEarnings = prefs.getInt(dailyKey) ?? 0;

      // Apply damping after threshold
      const dampingThreshold = 200;
      const dampingFactor = 0.5;

      if (dailyEarnings > dampingThreshold) {
        final dampedTokens = (tokensToAward * dampingFactor).round();
        return dampedTokens;
      }

      return tokensToAward;
    } catch (e) {
      print('Error applying soft cap: $e');
      return tokensToAward;
    }
  }

  /// Check if user is currently in cooldown
  Future<bool> isInCooldown() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cooldownEndStr = prefs.getString('cooldown_end');
      
      if (cooldownEndStr == null) return false;
      
      final cooldownEnd = DateTime.parse(cooldownEndStr);
      return DateTime.now().isBefore(cooldownEnd);
    } catch (e) {
      print('Error checking cooldown: $e');
      return false;
    }
  }

  /// Set cooldown period
  Future<void> setCooldown(Duration duration) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cooldownEnd = DateTime.now().add(duration);
      await prefs.setString('cooldown_end', cooldownEnd.toIso8601String());
    } catch (e) {
      print('Error setting cooldown: $e');
    }
  }

  /// Get remaining cooldown time
  Future<Duration?> getRemainingCooldown() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cooldownEndStr = prefs.getString('cooldown_end');
      
      if (cooldownEndStr == null) return null;
      
      final cooldownEnd = DateTime.parse(cooldownEndStr);
      final now = DateTime.now();
      
      if (now.isBefore(cooldownEnd)) {
        return cooldownEnd.difference(now);
      }
      
      return null;
    } catch (e) {
      print('Error getting remaining cooldown: $e');
      return null;
    }
  }

  /// Clear all anti-exploit data (for testing or admin reset)
  Future<void> clearAntiExploitData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final keys = prefs.getKeys().where((key) => 
        key.startsWith('game_times_') ||
        key.startsWith('hourly_earnings_') ||
        key.startsWith('daily_earnings_') ||
        key == 'last_time_check' ||
        key == 'cooldown_end'
      ).toList();
      
      for (final key in keys) {
        await prefs.remove(key);
      }
    } catch (e) {
      print('Error clearing anti-exploit data: $e');
    }
  }
}

// Provider is defined in app_providers.dart to avoid circular dependencies
