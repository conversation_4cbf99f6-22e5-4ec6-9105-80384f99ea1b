import 'dart:math';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../../../core/models/game_state.dart';
import '../../../core/providers/game_providers.dart';
import '../../../core/services/game_feedback_service.dart';
import '../../../shared/widgets/base_game_widget.dart';

class BeatTapGame extends BaseGameWidget {
  static const GameConfig gameConfig = GameConfig(
    gameId: 'beat_tap',
    gameName: 'BeatTap',
    scoreMultiplier: 1,
    hasLives: false,
    hasLevels: false,
    hasTimer: false,
  );

  const BeatTapGame({
    super.key,
    super.onGameComplete,
    super.onGameExit,
  }) : super(config: gameConfig);

  @override
  Widget buildGameContent(BuildContext context, WidgetRef ref, GameState gameState) {
    return const _BeatTapGameContent();
  }
}

class Tile {
  double y;
  final int column;
  bool isBlack;
  bool isTapped;
  bool isMissed;

  Tile({
    required this.y,
    required this.column,
    required this.isBlack,
    this.isTapped = false,
    this.isMissed = false,
  });
}

class _BeatTapGameContent extends ConsumerStatefulWidget {
  const _BeatTapGameContent();

  @override
  ConsumerState<_BeatTapGameContent> createState() => _BeatTapGameContentState();
}

class _BeatTapGameContentState extends ConsumerState<_BeatTapGameContent>
    with TickerProviderStateMixin {
  
  // Game state
  late AnimationController _tapController;
  late AnimationController _missController;
  
  // Tiles
  final List<Tile> _tiles = [];
  final int _columns = 4;
  final double _tileHeight = 120;
  double _gameSpeed = 200;
  final Random _random = Random();
  
  // Game mechanics
  double _spawnTimer = 0;
  final double _spawnInterval = 0.6; // seconds
  bool _showMissEffect = false;
  int _tappedColumn = -1;
  
  // Constants
  static const double _gameWidth = 400;
  static const double _gameHeight = 600;

  @override
  void initState() {
    super.initState();
    
    _tapController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );
    
    _missController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );
    
    _initializeGame();
    _startGameLoop();
  }

  @override
  void dispose() {
    _tapController.dispose();
    _missController.dispose();
    super.dispose();
  }

  void _initializeGame() {
    // Create initial tiles
    for (int i = 0; i < 5; i++) {
      _spawnTile(-i * _tileHeight);
    }
  }

  void _spawnTile(double y) {
    final blackColumn = _random.nextInt(_columns);
    
    for (int col = 0; col < _columns; col++) {
      _tiles.add(Tile(
        y: y,
        column: col,
        isBlack: col == blackColumn,
      ));
    }
  }

  void _startGameLoop() {
    const frameRate = 60;
    const frameDuration = Duration(milliseconds: 1000 ~/ frameRate);
    const deltaTime = 1.0 / frameRate;
    
    void gameLoop() {
      if (!mounted) return;
      
      final gameState = ref.read(gameStateProvider(BeatTapGame.gameConfig));
      if (!gameState.isPlaying) {
        Future.delayed(frameDuration, gameLoop);
        return;
      }
      
      setState(() {
        // Update tiles
        for (final tile in _tiles) {
          tile.y += _gameSpeed * deltaTime;
        }
        
        // Check for missed black tiles
        for (final tile in _tiles) {
          if (tile.isBlack && !tile.isTapped && !tile.isMissed && tile.y > _gameHeight) {
            tile.isMissed = true;
            _handleMiss();
            break;
          }
        }
        
        // Spawn new tiles
        _spawnTimer += deltaTime;
        if (_spawnTimer >= _spawnInterval) {
          final lastTileY = _tiles.isEmpty ? 0 : _tiles.map((t) => t.y).reduce(min);
          _spawnTile(lastTileY - _tileHeight);
          _spawnTimer = 0;
          
          // Increase speed gradually
          _gameSpeed += 2;
        }
        
        // Remove off-screen tiles
        _tiles.removeWhere((tile) => tile.y > _gameHeight + 100);
      });
      
      Future.delayed(frameDuration, gameLoop);
    }
    
    gameLoop();
  }

  void _handleTap(int column) {
    final gameState = ref.read(gameStateProvider(BeatTapGame.gameConfig));
    if (!gameState.isPlaying) return;
    
    setState(() {
      _tappedColumn = column;
    });
    
    // Find the bottommost black tile in this column
    Tile? targetTile;
    for (final tile in _tiles) {
      if (tile.column == column && tile.isBlack && !tile.isTapped && !tile.isMissed) {
        if (targetTile == null || tile.y > targetTile.y) {
          targetTile = tile;
        }
      }
    }
    
    if (targetTile != null && targetTile.y >= _gameHeight - _tileHeight - 50) {
      // Successful tap
      targetTile.isTapped = true;
      
      // Add score
      final gameNotifier = ref.read(gameStateProvider(BeatTapGame.gameConfig).notifier);
      gameNotifier.addScore(1);
      
      // Trigger feedback
      ref.read(gameFeedbackServiceProvider).triggerFeedback(GameEvent.scoreIncrease);
      
      // Play tap animation
      _tapController.forward().then((_) {
        _tapController.reset();
        setState(() {
          _tappedColumn = -1;
        });
      });
    } else {
      // Wrong tap - game over
      _handleMiss();
    }
  }

  void _handleMiss() {
    setState(() {
      _showMissEffect = true;
    });
    
    // Trigger feedback
    ref.read(gameFeedbackServiceProvider).triggerFeedback(GameEvent.obstacleHit);
    
    // Play miss animation
    _missController.forward().then((_) {
      _missController.reset();
      setState(() {
        _showMissEffect = false;
      });
    });
    
    // Game over
    final gameNotifier = ref.read(gameStateProvider(BeatTapGame.gameConfig).notifier);
    gameNotifier.endGame(reason: 'Missed a beat');
  }

  @override
  Widget build(BuildContext context) {
    final gameState = ref.watch(gameStateProvider(BeatTapGame.gameConfig));
    final columnWidth = _gameWidth / _columns;
    
    return Container(
      width: _gameWidth,
      height: _gameHeight,
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [Color(0xFF1a1a2e), Color(0xFF16213e)],
        ),
      ),
      child: Stack(
        children: [
          // Background pattern
          _buildBackground(),
          
          // Tiles
          ..._tiles.map((tile) => _buildTile(tile, columnWidth)),
          
          // Column separators
          _buildColumnSeparators(columnWidth),
          
          // Tap zones
          _buildTapZones(columnWidth),
          
          // Miss effect
          if (_showMissEffect) _buildMissEffect(),
          
          // Score display
          _buildScoreDisplay(gameState),
          
          // Instructions
          if (!gameState.isPlaying) _buildInstructions(),
        ],
      ),
    );
  }

  Widget _buildBackground() {
    return Positioned.fill(
      child: CustomPaint(
        painter: _BackgroundPainter(),
      ),
    );
  }

  Widget _buildTile(Tile tile, double columnWidth) {
    return Positioned(
      left: tile.column * columnWidth,
      top: tile.y,
      child: Container(
        width: columnWidth,
        height: _tileHeight,
        decoration: BoxDecoration(
          color: tile.isBlack 
              ? (tile.isTapped ? Colors.grey[600] : Colors.black)
              : Colors.white,
          border: Border.all(color: Colors.grey[400]!, width: 0.5),
        ),
        child: tile.isBlack && !tile.isTapped ? const Center(
          child: Icon(
            Icons.music_note,
            color: Colors.white,
            size: 30,
          ),
        ) : null,
      ),
    );
  }

  Widget _buildColumnSeparators(double columnWidth) {
    return Stack(
      children: List.generate(_columns - 1, (index) {
        final x = (index + 1) * columnWidth;
        return Positioned(
          left: x - 1,
          top: 0,
          child: Container(
            width: 2,
            height: _gameHeight,
            color: Colors.grey[600],
          ),
        );
      }),
    );
  }

  Widget _buildTapZones(double columnWidth) {
    return Stack(
      children: List.generate(_columns, (index) {
        return Positioned(
          left: index * columnWidth,
          bottom: 0,
          child: GestureDetector(
            onTap: () => _handleTap(index),
            child: Container(
              width: columnWidth,
              height: _tileHeight + 50,
              decoration: BoxDecoration(
                color: _tappedColumn == index 
                    ? Colors.cyan.withOpacity(0.3)
                    : Colors.transparent,
                border: Border.all(
                  color: Colors.cyan.withOpacity(0.5),
                  width: 2,
                ),
              ),
              child: const Center(
                child: Icon(
                  Icons.touch_app,
                  color: Colors.cyan,
                  size: 24,
                ),
              ),
            ),
          ),
        );
      }),
    );
  }

  Widget _buildMissEffect() {
    return Positioned.fill(
      child: ScaleTransition(
        scale: _missController,
        child: Container(
          decoration: BoxDecoration(
            color: Colors.red.withOpacity(0.3),
          ),
          child: const Center(
            child: Text(
              'MISS!',
              style: TextStyle(
                color: Colors.white,
                fontSize: 48,
                fontWeight: FontWeight.bold,
                shadows: [
                  Shadow(
                    offset: Offset(2, 2),
                    blurRadius: 4,
                    color: Colors.black,
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildScoreDisplay(GameState gameState) {
    return Positioned(
      top: 50,
      left: 0,
      right: 0,
      child: Center(
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            color: Colors.black54,
            borderRadius: BorderRadius.circular(20),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Icon(Icons.music_note, color: Colors.white, size: 20),
              const SizedBox(width: 8),
              Text(
                '${gameState.score}',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInstructions() {
    return Positioned(
      bottom: 200,
      left: 0,
      right: 0,
      child: Center(
        child: Container(
          padding: EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.black54,
            borderRadius: BorderRadius.all(Radius.circular(12)),
          ),
          child: Text(
            'Tap the black tiles as they reach the bottom!\nDon\'t tap white tiles or miss black ones.\nSpeed increases over time!',
            textAlign: TextAlign.center,
            style: TextStyle(
              color: Colors.white,
              fontSize: 16,
            ),
          ),
        ),
      ),
    );
  }
}

class _BackgroundPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.white.withOpacity(0.05)
      ..strokeWidth = 1;
    
    // Draw horizontal lines for rhythm
    for (double y = 0; y < size.height; y += 120) {
      canvas.drawLine(
        Offset(0, y),
        Offset(size.width, y),
        paint,
      );
    }
    
    // Draw musical staff lines
    final staffPaint = Paint()
      ..color = Colors.cyan.withOpacity(0.1)
      ..strokeWidth = 0.5;
    
    for (int i = 0; i < 5; i++) {
      final y = 100.0 + i * 20.0;
      canvas.drawLine(
        Offset(0, y),
        Offset(size.width.toDouble(), y),
        staffPaint,
      );
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
