import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/game_state.dart';
import '../models/user_model.dart';
import '../models/leaderboard_entry.dart';
import '../utils/scoring_system.dart';
import '../providers/app_providers.dart';
import 'auth_service.dart';
import 'firestore_service.dart';
import 'token_service.dart';
import 'sound_service.dart';
import 'vibration_service.dart';
import 'achievement_service.dart';

/// Result of a score submission
class ScoreSubmissionResult {
  final bool success;
  final bool isNewHighScore;
  final bool isNewPersonalBest;
  final int tokensEarned;
  final int previousHighScore;
  final int leaderboardPosition;
  final String? errorMessage;
  final Map<String, dynamic> achievements;

  const ScoreSubmissionResult({
    required this.success,
    required this.isNewHighScore,
    required this.isNewPersonalBest,
    required this.tokensEarned,
    required this.previousHighScore,
    required this.leaderboardPosition,
    this.errorMessage,
    this.achievements = const {},
  });

  ScoreSubmissionResult copyWith({
    bool? success,
    bool? isNewHighScore,
    bool? isNewPersonalBest,
    int? tokensEarned,
    int? previousHighScore,
    int? leaderboardPosition,
    String? errorMessage,
    Map<String, dynamic>? achievements,
  }) {
    return ScoreSubmissionResult(
      success: success ?? this.success,
      isNewHighScore: isNewHighScore ?? this.isNewHighScore,
      isNewPersonalBest: isNewPersonalBest ?? this.isNewPersonalBest,
      tokensEarned: tokensEarned ?? this.tokensEarned,
      previousHighScore: previousHighScore ?? this.previousHighScore,
      leaderboardPosition: leaderboardPosition ?? this.leaderboardPosition,
      errorMessage: errorMessage ?? this.errorMessage,
      achievements: achievements ?? this.achievements,
    );
  }
}

/// Comprehensive score submission service
class ScoreSubmissionService {
  final AuthService _authService;
  final FirestoreService _firestoreService;
  final TokenService _tokenService;
  final SoundService _soundService;
  final VibrationService _vibrationService;
  final AchievementService _achievementService;

  ScoreSubmissionService({
    required AuthService authService,
    required FirestoreService firestoreService,
    required TokenService tokenService,
    required SoundService soundService,
    required VibrationService vibrationService,
    required AchievementService achievementService,
  }) : _authService = authService,
       _firestoreService = firestoreService,
       _tokenService = tokenService,
       _soundService = soundService,
       _vibrationService = vibrationService,
       _achievementService = achievementService;

  /// Submit a game score with comprehensive validation and rewards
  Future<ScoreSubmissionResult> submitScore({
    required GameConfig config,
    required GameState gameState,
    Map<String, dynamic>? gameStats,
  }) async {
    try {
      final user = _authService.currentUser;
      if (user == null) {
        return const ScoreSubmissionResult(
          success: false,
          isNewHighScore: false,
          isNewPersonalBest: false,
          tokensEarned: 0,
          previousHighScore: 0,
          leaderboardPosition: -1,
          errorMessage: 'User not authenticated',
        );
      }

      // Validate score
      final validationResult = await _validateScore(config, gameState, gameStats);
      if (!validationResult['isValid']) {
        return ScoreSubmissionResult(
          success: false,
          isNewHighScore: false,
          isNewPersonalBest: false,
          tokensEarned: 0,
          previousHighScore: 0,
          leaderboardPosition: -1,
          errorMessage: validationResult['reason'],
        );
      }

      // Get current user data and scores
      final userData = await _firestoreService.getUser(user.uid);
      final previousHighScore = userData?.getHighScore(config.gameId) ?? 0;
      final isNewPersonalBest = gameState.score > previousHighScore;

      // Check leaderboard position
      final leaderboard = await _getLeaderboardSnapshot(config.gameId);
      final isNewHighScore = _isNewHighScore(gameState.score, leaderboard);
      final leaderboardPosition = _calculateLeaderboardPosition(gameState.score, leaderboard);

      // Calculate token reward using the improved token service method
      final tokensEarned = _tokenService.calculateTokenReward(
        gameState.score,
        config.gameId,
      );

      // Calculate achievements (only if userData is available)
      final achievements = userData != null
        ? await _calculateAchievements(
            config: config,
            gameState: gameState,
            userData: userData,
            gameStats: gameStats,
          )
        : <String, dynamic>{};

      // Submit to Firestore
      await _submitToFirestore(
        config: config,
        gameState: gameState,
        user: user,
        isNewPersonalBest: isNewPersonalBest,
        tokensEarned: tokensEarned,
      );

      // Check and unlock achievements
      await _checkAchievements(user.uid);

      // Play feedback
      await _playSubmissionFeedback(isNewHighScore, isNewPersonalBest);

      return ScoreSubmissionResult(
        success: true,
        isNewHighScore: isNewHighScore,
        isNewPersonalBest: isNewPersonalBest,
        tokensEarned: tokensEarned,
        previousHighScore: previousHighScore,
        leaderboardPosition: leaderboardPosition,
        achievements: achievements,
      );

    } catch (e) {
      print('Error submitting score: $e');
      return ScoreSubmissionResult(
        success: false,
        isNewHighScore: false,
        isNewPersonalBest: false,
        tokensEarned: 0,
        previousHighScore: 0,
        leaderboardPosition: -1,
        errorMessage: 'Failed to submit score: ${e.toString()}',
      );
    }
  }

  /// Validate the submitted score
  Future<Map<String, dynamic>> _validateScore(
    GameConfig config,
    GameState gameState,
    Map<String, dynamic>? gameStats,
  ) async {
    // Basic validation
    if (gameState.score < 0) {
      return {'isValid': false, 'reason': 'Invalid score: negative value'};
    }

    if (gameState.score > 1000000) {
      return {'isValid': false, 'reason': 'Invalid score: too high'};
    }

    // Game-specific validation
    if (config.hasTimer && config.timeLimit != null) {
      if (gameState.gameTime > config.timeLimit!) {
        return {'isValid': false, 'reason': 'Invalid game time'};
      }
    }

    // Additional validation based on game stats
    if (gameStats != null) {
      final validationResult = _validateGameSpecificStats(config.gameId, gameStats);
      if (!validationResult['isValid']) {
        return validationResult;
      }
    }

    return {'isValid': true};
  }

  /// Validate game-specific statistics
  Map<String, dynamic> _validateGameSpecificStats(String gameId, Map<String, dynamic> stats) {
    // Implement game-specific validation logic here
    // For example, check if actions per minute is reasonable, etc.
    return {'isValid': true};
  }

  /// Get current leaderboard snapshot
  Future<List<LeaderboardEntry>> _getLeaderboardSnapshot(String gameId) async {
    try {
      return await _firestoreService.getLeaderboard(gameId, limit: 100).first;
    } catch (e) {
      print('Error getting leaderboard: $e');
      return [];
    }
  }

  /// Check if this is a new high score
  bool _isNewHighScore(int score, List<LeaderboardEntry> leaderboard) {
    if (leaderboard.isEmpty) return true;
    return score > leaderboard.first.score;
  }

  /// Calculate leaderboard position
  int _calculateLeaderboardPosition(int score, List<LeaderboardEntry> leaderboard) {
    if (leaderboard.isEmpty) return 1;
    
    int position = 1;
    for (final entry in leaderboard) {
      if (score > entry.score) break;
      position++;
    }
    return position;
  }

  /// Calculate achievements based on performance
  Future<Map<String, dynamic>> _calculateAchievements({
    required GameConfig config,
    required GameState gameState,
    required UserModel userData,
    Map<String, dynamic>? gameStats,
  }) async {
    final achievements = <String, dynamic>{};

    // First time playing this game
    if (!userData.hasPlayedGame(config.gameId)) {
      achievements['first_play'] = true;
    }

    // Perfect score achievements
    if (gameStats != null) {
      final accuracy = gameStats['accuracy'] as double?;
      if (accuracy != null && accuracy >= 1.0) {
        achievements['perfect_accuracy'] = true;
      }
    }

    // Speed achievements
    if (config.hasTimer && gameState.gameTime.inSeconds < 60) {
      achievements['speed_demon'] = true;
    }

    // Survival achievements
    if (gameState.gameTime.inMinutes >= 10) {
      achievements['survivor'] = true;
    }

    // Score milestones
    if (gameState.score >= 100000) {
      achievements['high_scorer'] = true;
    }

    return achievements;
  }

  /// Submit score data to Firestore
  Future<void> _submitToFirestore({
    required GameConfig config,
    required GameState gameState,
    required dynamic user,
    required bool isNewPersonalBest,
    required int tokensEarned,
  }) async {
    // Submit to leaderboard
    await _firestoreService.submitScore(
      config.gameId,
      user.uid,
      user.displayName ?? 'Anonymous',
      gameState.score,
    );

    // Update personal high score if needed
    if (isNewPersonalBest) {
      await _firestoreService.updateUserHighScore(
        user.uid,
        config.gameId,
        gameState.score,
      );
    }

    // Award tokens
    if (tokensEarned > 0) {
      await _tokenService.awardTokens(
        user.uid,
        tokensEarned,
        reason: 'Completed ${config.gameName} with score ${gameState.score}',
      );
    }
  }

  /// Play audio and haptic feedback for score submission
  Future<void> _playSubmissionFeedback(bool isNewHighScore, bool isNewPersonalBest) async {
    if (isNewHighScore) {
      await _soundService.playVictory();
      await _vibrationService.onScore();
    } else if (isNewPersonalBest) {
      await _soundService.playScore();
      await _vibrationService.onScore();
    } else {
      await _soundService.playGameOver();
      await _vibrationService.onGameOver();
    }
  }

  /// Get user's game statistics
  Future<Map<String, dynamic>> getUserGameStats(String gameId, String userId) async {
    try {
      final userData = await _firestoreService.getUser(userId);
      if (userData == null) return {};

      final userScore = await _firestoreService.getUserScore(gameId, userId);
      final leaderboard = await _getLeaderboardSnapshot(gameId);
      
      return {
        'personalBest': userData.getHighScore(gameId),
        'gamesPlayed': userData.hasPlayedGame(gameId) ? 1 : 0, // This would need to be tracked separately
        'leaderboardPosition': userScore != null 
            ? _calculateLeaderboardPosition(userScore.score, leaderboard)
            : -1,
        'rank': userScore != null 
            ? ScoringSystem.calculateRank(userScore.score, leaderboard.map((e) => e.score).toList())
            : 'Unranked',
      };
    } catch (e) {
      print('Error getting user game stats: $e');
      return {};
    }
  }

  /// Batch submit multiple scores (for offline play)
  Future<List<ScoreSubmissionResult>> batchSubmitScores(
    List<Map<String, dynamic>> scoreData,
  ) async {
    final results = <ScoreSubmissionResult>[];
    
    for (final data in scoreData) {
      final result = await submitScore(
        config: data['config'] as GameConfig,
        gameState: data['gameState'] as GameState,
        gameStats: data['gameStats'] as Map<String, dynamic>?,
      );
      results.add(result);
    }
    
    return results;
  }

  /// Check and unlock achievements for user
  Future<void> _checkAchievements(String userId) async {
    try {
      // Get current user data to build stats for achievement checking
      final userData = await _firestoreService.getUser(userId);
      if (userData == null) return;

      // Calculate total games played across all individual game stats
      int totalGamesPlayed = 0;
      int totalTokensEarned = 0;

      // Get game stats from all individual game collections
      final gameStatsSnapshot = await FirebaseFirestore.instance
          .collection('users')
          .doc(userId)
          .collection('gameStats')
          .get();

      for (final doc in gameStatsSnapshot.docs) {
        final data = doc.data();
        totalGamesPlayed += (data['gamesPlayed'] ?? 0) as int;
        totalTokensEarned += (data['totalTokensEarned'] ?? 0) as int;
      }

      // Build user stats map for achievement checking
      final userStats = {
        'totalGamesPlayed': totalGamesPlayed,
        'uniqueGamesPlayed': userData.highScores.keys.length, // Number of different games played
        'totalTokensEarned': totalTokensEarned, // Total tokens earned from games
        'highestScore': userData.highScores.values.isNotEmpty
            ? userData.highScores.values.reduce((a, b) => a > b ? a : b)
            : 0,
        'hasPerfectScore': false, // This would need to be tracked separately
        'consecutiveDays': 0, // This would need to be tracked separately
      };

      // Check and unlock achievements
      final newlyUnlocked = await _achievementService.checkAndUnlockAchievements(userId, userStats);

      // Log newly unlocked achievements
      if (newlyUnlocked.isNotEmpty) {
        print('Unlocked ${newlyUnlocked.length} new achievements for user $userId');
        for (final achievement in newlyUnlocked) {
          print('- ${achievement.title}: ${achievement.description}');
        }
      }
    } catch (e) {
      print('Error checking achievements: $e');
    }
  }
}

/// Provider for score submission service
final scoreSubmissionServiceProvider = Provider<ScoreSubmissionService>((ref) {
  return ScoreSubmissionService(
    authService: ref.watch(authServiceProvider),
    firestoreService: ref.watch(firestoreServiceProvider),
    tokenService: ref.watch(tokenServiceProvider),
    soundService: ref.watch(soundServiceProvider),
    vibrationService: ref.watch(vibrationServiceProvider),
    achievementService: ref.watch(achievementServiceProvider),
  );
});
