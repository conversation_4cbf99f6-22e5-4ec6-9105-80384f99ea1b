import 'dart:math' as math;
import 'package:flame/components.dart';
import 'package:flutter/material.dart';
import '../../core/mini_game_base.dart';
import '../../core/hud.dart';
import '../../core/effects.dart';
import '../../core/analytics.dart';
import '../../core/difficulty.dart';

/// TileTide - Sliding tile puzzle with time pressure
/// Controls: Swipe to slide tile rows/columns; form target patterns
/// Scoring: +10 per pattern match; +50 per level complete; time bonus
class TileTideGame extends MiniGameBase {
  TileTideGame(int seed) : super(modeId: 'tile_tide', sessionSeed: seed) {
    rng = math.Random(seed);
  }

  late math.Random rng;
  late ScoreText scoreText;
  late TimerText timerText;
  
  // Game state
  int score = 0;
  int level = 1;
  int patternsMatched = 0;
  
  // Grid system
  static const int gridSize = 4;
  List<List<int>> grid = [];
  List<List<int>> targetPattern = [];
  
  // Difficulty curves
  final timeLimitCurve = const CurveParam(start: 30, max: 15, perMinute: -7.5);
  
  late DifficultyManager difficultyManager;
  
  // Visual elements
  late TileGrid tileGrid;
  late PatternDisplay patternDisplay;

  @override
  Future<void> loadAssets() async {}

  @override
  void setupWorld() {
    camera.viewfinder.visibleGameSize = Vector2(720, 1280);
    
    difficultyManager = DifficultyManager();
    difficultyManager.addParameter('time_limit', timeLimitCurve);
    
    _initializeGrid();
    _createGameEntities();
  }

  void _initializeGrid() {
    grid = List.generate(gridSize, (i) => List.generate(gridSize, (j) => 0));
    targetPattern = List.generate(gridSize, (i) => List.generate(gridSize, (j) => 0));
    
    _generateRandomGrid();
    _generateTargetPattern();
  }

  void _generateRandomGrid() {
    for (int i = 0; i < gridSize; i++) {
      for (int j = 0; j < gridSize; j++) {
        grid[i][j] = rng.nextInt(4); // 4 different tile types
      }
    }
  }

  void _generateTargetPattern() {
    // Generate a solvable target pattern
    for (int i = 0; i < gridSize; i++) {
      for (int j = 0; j < gridSize; j++) {
        targetPattern[i][j] = rng.nextInt(4);
      }
    }
  }

  void _createGameEntities() {
    // Create tile grid
    tileGrid = TileGrid(
      position: Vector2(160, 400),
      grid: grid,
      onSwipe: _handleGridSwipe,
    );
    add(tileGrid);

    // Create pattern display
    patternDisplay = PatternDisplay(
      position: Vector2(160, 100),
      pattern: targetPattern,
    );
    add(patternDisplay);
  }

  @override
  void setupHUD() {
    scoreText = ScoreText(position: Vector2(16, 16));
    add(scoreText);

    timerText = TimerText(position: Vector2(16, 50));
    add(timerText);
  }

  @override
  void onStart() {
    score = 0;
    level = 1;
    patternsMatched = 0;
    
    difficultyManager.start();
    startGame();
    
    Analytics.log('tile_tide_start', {'session_seed': sessionSeed});
  }

  @override
  void onGameUpdate(double dt) {
    final timeLimit = difficultyManager.getValue('time_limit');
    
    // Update timer
    timerText.updateTime(gameTime);
    
    // Check time limit
    if (gameTime.inSeconds >= timeLimit) {
      _timeUp();
    }
    
    // Check for pattern match
    if (_checkPatternMatch()) {
      _patternMatched();
    }
  }

  void _handleGridSwipe(int row, int col, String direction) {
    switch (direction) {
      case 'up':
        _slideColumn(col, -1);
        break;
      case 'down':
        _slideColumn(col, 1);
        break;
      case 'left':
        _slideRow(row, -1);
        break;
      case 'right':
        _slideRow(row, 1);
        break;
    }
    
    tileGrid.updateGrid(grid);
    
    Analytics.log('tile_slide', {
      'row': row,
      'col': col,
      'direction': direction,
    });
  }

  void _slideRow(int row, int direction) {
    if (direction == 1) { // Right
      final temp = grid[row][gridSize - 1];
      for (int i = gridSize - 1; i > 0; i--) {
        grid[row][i] = grid[row][i - 1];
      }
      grid[row][0] = temp;
    } else { // Left
      final temp = grid[row][0];
      for (int i = 0; i < gridSize - 1; i++) {
        grid[row][i] = grid[row][i + 1];
      }
      grid[row][gridSize - 1] = temp;
    }
  }

  void _slideColumn(int col, int direction) {
    if (direction == 1) { // Down
      final temp = grid[gridSize - 1][col];
      for (int i = gridSize - 1; i > 0; i--) {
        grid[i][col] = grid[i - 1][col];
      }
      grid[0][col] = temp;
    } else { // Up
      final temp = grid[0][col];
      for (int i = 0; i < gridSize - 1; i++) {
        grid[i][col] = grid[i + 1][col];
      }
      grid[gridSize - 1][col] = temp;
    }
  }

  bool _checkPatternMatch() {
    for (int i = 0; i < gridSize; i++) {
      for (int j = 0; j < gridSize; j++) {
        if (grid[i][j] != targetPattern[i][j]) {
          return false;
        }
      }
    }
    return true;
  }

  void _patternMatched() {
    patternsMatched++;
    score += 10;
    addScore(10);
    scoreText.updateScore(score);
    
    // Visual effects
    add(Effects.sparkle(Vector2(360, 500), colors: [Colors.amber, Colors.yellow]));
    add(Effects.textPopup(
      Vector2(360, 450),
      '+10',
      color: Colors.amber,
    ));
    
    // Level complete
    _levelComplete();
    
    Analytics.log('pattern_matched', {
      'level': level,
      'patterns_matched': patternsMatched,
    });
  }

  void _levelComplete() {
    level++;
    score += 50;
    addScore(50);
    scoreText.updateScore(score);
    
    // Time bonus
    final timeLimit = difficultyManager.getValue('time_limit');
    final timeRemaining = timeLimit - gameTime.inSeconds;
    if (timeRemaining > 0) {
      final timeBonus = (timeRemaining * 2).round();
      score += timeBonus;
      addScore(timeBonus);
      scoreText.updateScore(score);
      
      add(Effects.textPopup(
        Vector2(360, 400),
        'TIME BONUS +$timeBonus',
        color: Colors.cyan,
      ));
    }
    
    // Generate next level
    _generateRandomGrid();
    _generateTargetPattern();
    tileGrid.updateGrid(grid);
    patternDisplay.updatePattern(targetPattern);
    
    Analytics.log('level_complete', {
      'level': level - 1,
      'time_bonus': timeRemaining > 0 ? (timeRemaining * 2).round() : 0,
    });
  }

  void _timeUp() {
    endGame(success: false, score: score);
    
    Analytics.log('time_up', {
      'final_score': score,
      'level': level,
      'patterns_matched': patternsMatched,
    });
  }


  @override
  void reportScore(int score) {}

  @override
  void awardTokens(int tokens) {}
}

class TileGrid extends PositionComponent {
  TileGrid({
    required super.position,
    required this.grid,
    required this.onSwipe,
  }) : super(size: Vector2(400, 400));

  List<List<int>> grid;
  final Function(int, int, String) onSwipe;
  
  static const double tileSize = 90;
  static const double tileSpacing = 10;

  @override
  void render(Canvas canvas) {
    for (int i = 0; i < grid.length; i++) {
      for (int j = 0; j < grid[i].length; j++) {
        final tileX = j * (tileSize + tileSpacing);
        final tileY = i * (tileSize + tileSpacing);
        
        // Draw tile background
        final bgPaint = Paint()..color = Colors.grey[300]!;
        canvas.drawRect(
          Rect.fromLTWH(tileX, tileY, tileSize, tileSize),
          bgPaint,
        );
        
        // Draw tile color based on value
        final colors = [Colors.red, Colors.blue, Colors.green, Colors.yellow];
        final tilePaint = Paint()..color = colors[grid[i][j]];
        canvas.drawRect(
          Rect.fromLTWH(tileX + 5, tileY + 5, tileSize - 10, tileSize - 10),
          tilePaint,
        );
      }
    }
  }

  // TODO: Implement input handling
  void handlePanEnd(Vector2 position, Vector2 delta) {
    final col = (position.x / (tileSize + tileSpacing)).floor();
    final row = (position.y / (tileSize + tileSpacing)).floor();

    if (row >= 0 && row < grid.length && col >= 0 && col < grid[0].length) {
      
      String direction;
      if (delta.x.abs() > delta.y.abs()) {
        direction = delta.x > 0 ? 'right' : 'left';
      } else {
        direction = delta.y > 0 ? 'down' : 'up';
      }
      
      onSwipe(row, col, direction);
    }
  }

  void updateGrid(List<List<int>> newGrid) {
    grid = newGrid;
  }
}

class PatternDisplay extends PositionComponent {
  PatternDisplay({
    required super.position,
    required this.pattern,
  }) : super(size: Vector2(200, 200));

  List<List<int>> pattern;
  
  static const double tileSize = 40;
  static const double tileSpacing = 5;

  @override
  void render(Canvas canvas) {
    // Draw title
    final textPaint = TextPaint(
      style: const TextStyle(
        color: Colors.white,
        fontSize: 16,
        fontWeight: FontWeight.bold,
      ),
    );
    textPaint.render(canvas, 'TARGET:', Vector2(0, -25));
    
    // Draw pattern
    for (int i = 0; i < pattern.length; i++) {
      for (int j = 0; j < pattern[i].length; j++) {
        final tileX = j * (tileSize + tileSpacing);
        final tileY = i * (tileSize + tileSpacing);
        
        // Draw tile background
        final bgPaint = Paint()..color = Colors.grey[600]!;
        canvas.drawRect(
          Rect.fromLTWH(tileX, tileY, tileSize, tileSize),
          bgPaint,
        );
        
        // Draw tile color
        final colors = [Colors.red, Colors.blue, Colors.green, Colors.yellow];
        final tilePaint = Paint()..color = colors[pattern[i][j]];
        canvas.drawRect(
          Rect.fromLTWH(tileX + 2, tileY + 2, tileSize - 4, tileSize - 4),
          tilePaint,
        );
      }
    }
  }

  void updatePattern(List<List<int>> newPattern) {
    pattern = newPattern;
  }
}
