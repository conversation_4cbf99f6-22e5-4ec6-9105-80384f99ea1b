import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../../../core/providers/app_providers.dart';
import '../../../core/services/game_navigation_service.dart';

import 'game_card.dart';

class PinnedGamesSection extends ConsumerStatefulWidget {
  const PinnedGamesSection({super.key});

  @override
  ConsumerState<PinnedGamesSection> createState() => _PinnedGamesSectionState();
}

class _PinnedGamesSectionState extends ConsumerState<PinnedGamesSection> {
  List<GameInfo> _pinnedGames = [];

  @override
  void initState() {
    super.initState();
    _loadPinnedGames();
  }

  Future<void> _loadPinnedGames() async {
    final pinnedGamesService = ref.read(pinnedGamesServiceProvider);
    final gameNavigation = ref.read(gameNavigationServiceProvider);
    
    final pinnedGameIds = await pinnedGamesService.getPinnedGames();
    final allGames = gameNavigation.allGames;
    
    final pinnedGames = allGames
        .where((game) => pinnedGameIds.contains(game.id))
        .toList();
    
    if (mounted) {
      setState(() {
        _pinnedGames = pinnedGames;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_pinnedGames.isEmpty) {
      return const SizedBox.shrink();
    }

    final gameNavigation = ref.watch(gameNavigationServiceProvider);
    final soundService = ref.watch(soundServiceProvider);
    final vibrationService = ref.watch(vibrationServiceProvider);
    final pinnedGamesService = ref.watch(pinnedGamesServiceProvider);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Section header
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.amber.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  Icons.push_pin,
                  color: Colors.amber.shade300,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Text(
                'Pinned Games',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              const Spacer(),
              Text(
                '${_pinnedGames.length}/4',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.white.withValues(alpha: 0.7),
                ),
              ),
            ],
          ),
        ),
        
        // Horizontal scrolling game cards
        SizedBox(
          height: 200,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            padding: const EdgeInsets.symmetric(horizontal: 16),
            itemCount: _pinnedGames.length,
            itemBuilder: (context, index) {
              final game = _pinnedGames[index];
              
              return Container(
                width: 160,
                margin: EdgeInsets.only(
                  right: index < _pinnedGames.length - 1 ? 12 : 0,
                ),
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(
                      color: Colors.amber.withValues(alpha: 0.3),
                      width: 2,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.amber.withValues(alpha: 0.2),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: GameCard(
                    gameInfo: game,
                    isPinned: true,
                    onTap: () async {
                      await soundService.playButtonClick();
                      await vibrationService.onButtonTap();
                      if (mounted) {
                        gameNavigation.navigateToGame(context, game.type);
                      }
                    },
                    onPinToggle: () async {
                      await pinnedGamesService.toggleGamePin(game.id);
                      await _loadPinnedGames();
                    },
                  ),
                ),
              ).animate(delay: Duration(milliseconds: index * 100))
               .fadeIn(duration: 400.ms)
               .slideX(begin: 0.3, end: 0);
            },
          ),
        ),
      ],
    ).animate()
     .fadeIn(duration: 600.ms, delay: 200.ms)
     .slideY(begin: 0.2, end: 0);
  }
}
