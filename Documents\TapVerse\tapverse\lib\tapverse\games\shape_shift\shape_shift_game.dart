import 'dart:math' as math;
import 'package:flame/components.dart';
import 'package:flame/collisions.dart';
import 'package:flutter/material.dart';
import '../../core/mini_game_base.dart';
import '../../core/hud.dart';
import '../../core/effects.dart';
import '../../core/analytics.dart';
import '../../core/difficulty.dart';

/// ShapeShift - Morphing puzzle with shape matching
/// Controls: Tap shape buttons to morph player; match incoming obstacles
/// Scoring: +5 per correct match; +15 per perfect sequence (5+ consecutive)
class ShapeShiftGame extends MiniGameBase {
  ShapeShiftGame(int seed) : super(modeId: 'shape_shift', sessionSeed: seed) {
    rng = math.Random(seed);
  }

  late math.Random rng;
  late ScoreText scoreText;
  late ComboText sequenceText;
  
  // Game state
  int score = 0;
  int consecutiveMatches = 0;
  
  // Game entities
  late ShapePlayer player;
  late List<ShapeObstacle> obstacles;
  late List<ShapeButton> shapeButtons;
  
  // Difficulty curves
  final obstacleSpeedCurve = const CurveParam(start: 150, max: 350, perMinute: 100);
  final spawnRateCurve = const CurveParam(start: 1.5, max: 0.8, perMinute: -0.35);
  
  late DifficultyManager difficultyManager;
  
  // Level generation
  double nextObstacleY = -200;
  double spawnTimer = 0;

  @override
  Future<void> loadAssets() async {}

  @override
  void setupWorld() {
    camera.viewfinder.visibleGameSize = Vector2(720, 1280);
    
    difficultyManager = DifficultyManager();
    difficultyManager.addParameter('obstacle_speed', obstacleSpeedCurve);
    difficultyManager.addParameter('spawn_rate', spawnRateCurve);
    
    obstacles = [];
    shapeButtons = [];
    
    _createGameEntities();
  }

  void _createGameEntities() {
    // Create player
    player = ShapePlayer(
      position: Vector2(360, 1000),
      onShapeMatch: _onShapeMatch,
      onShapeMismatch: _onShapeMismatch,
      onObstacleRemove: (obstacle) => obstacles.remove(obstacle),
    );
    add(player);

    // Create shape buttons
    final shapes = [ShapeType.circle, ShapeType.square, ShapeType.triangle];
    for (int i = 0; i < shapes.length; i++) {
      final button = ShapeButton(
        position: Vector2(120 + i * 240, 1200),
        shapeType: shapes[i],
        onPressed: () => player.morphTo(shapes[i]),
      );
      shapeButtons.add(button);
      add(button);
    }
  }

  @override
  void setupHUD() {
    scoreText = ScoreText(position: Vector2(16, 16));
    add(scoreText);

    sequenceText = ComboText(
      prefix: 'Sequence: ',
      position: Vector2(16, 50),
      minDisplayCombo: 1,
    );
    add(sequenceText);
  }

  @override
  void onStart() {
    score = 0;
    consecutiveMatches = 0;
    
    difficultyManager.start();
    startGame();
    
    Analytics.log('shape_shift_start', {'session_seed': sessionSeed});
  }

  @override
  void onGameUpdate(double dt) {
    final spawnRate = difficultyManager.getValue('spawn_rate');
    final obstacleSpeed = difficultyManager.getValue('obstacle_speed');
    
    // Update obstacle speeds
    for (final obstacle in obstacles) {
      obstacle.updateSpeed(obstacleSpeed);
    }
    
    // Spawn obstacles
    spawnTimer += dt;
    if (spawnTimer >= spawnRate) {
      _spawnObstacle();
      spawnTimer = 0;
    }
    
    // Clean up old obstacles
    obstacles.removeWhere((obstacle) {
      if (obstacle.position.y > 1300) {
        obstacle.removeFromParent();
        return true;
      }
      return false;
    });
  }

  void _spawnObstacle() {
    final shapes = [ShapeType.circle, ShapeType.square, ShapeType.triangle];
    final randomShape = shapes[rng.nextInt(shapes.length)];
    
    final obstacle = ShapeObstacle(
      position: Vector2(360, nextObstacleY),
      shapeType: randomShape,
    );
    
    obstacles.add(obstacle);
    add(obstacle);
    
    nextObstacleY -= 100;
  }

  void _onShapeMatch() {
    consecutiveMatches++;
    score += 5;
    addScore(5);
    scoreText.updateScore(score);
    sequenceText.updateCombo(consecutiveMatches);
    
    // Perfect sequence bonus
    if (consecutiveMatches >= 5 && consecutiveMatches % 5 == 0) {
      score += 15;
      addScore(15);
      scoreText.updateScore(score);
      
      add(Effects.textPopup(
        player.position + Vector2(0, -30),
        'PERFECT SEQUENCE +15',
        color: Colors.amber,
      ));
    }
    
    add(Effects.sparkle(player.position, colors: [Colors.green, Colors.white]));
    
    Analytics.log('shape_match', {
      'consecutive_matches': consecutiveMatches,
      'score': score,
    });
  }

  void _onShapeMismatch() {
    consecutiveMatches = 0;
    sequenceText.updateCombo(consecutiveMatches);
    
    endGame(success: false, score: score);
    
    Analytics.log('shape_mismatch', {
      'final_score': score,
    });
  }


  @override
  void reportScore(int score) {}

  @override
  void awardTokens(int tokens) {}
}

enum ShapeType { circle, square, triangle }

class ShapePlayer extends PositionComponent with HasCollisionDetection, CollisionCallbacks {
  ShapePlayer({
    required super.position,
    required this.onShapeMatch,
    required this.onShapeMismatch,
    required this.onObstacleRemove,
  }) : super(size: Vector2(60, 60));

  final VoidCallback onShapeMatch;
  final VoidCallback onShapeMismatch;
  final Function(ShapeObstacle) onObstacleRemove;

  ShapeType currentShape = ShapeType.circle;

  @override
  Future<void> onLoad() async {
    add(RectangleHitbox());
  }

  @override
  void render(Canvas canvas) {
    final paint = Paint()..color = Colors.blue;
    
    switch (currentShape) {
      case ShapeType.circle:
        canvas.drawCircle(Offset(size.x / 2, size.y / 2), size.x / 2, paint);
        break;
      case ShapeType.square:
        canvas.drawRect(size.toRect(), paint);
        break;
      case ShapeType.triangle:
        final path = Path()
          ..moveTo(size.x / 2, 0)
          ..lineTo(0, size.y)
          ..lineTo(size.x, size.y)
          ..close();
        canvas.drawPath(path, paint);
        break;
    }
  }

  void morphTo(ShapeType newShape) {
    currentShape = newShape;
    
    // Visual effect
    add(Effects.pulse(scale: 1.3, duration: 0.3));
  }

  @override
  bool onCollisionStart(Set<Vector2> intersectionPoints, PositionComponent other) {
    if (other is ShapeObstacle) {
      if (currentShape == other.shapeType) {
        onShapeMatch();
      } else {
        onShapeMismatch();
      }
      
      onObstacleRemove(other);
      other.removeFromParent();
      return true;
    }
    return false;
  }
}

class ShapeObstacle extends PositionComponent with HasCollisionDetection {
  ShapeObstacle({
    required super.position,
    required this.shapeType,
  }) : super(size: Vector2(50, 50));

  final ShapeType shapeType;
  double speed = 150;

  @override
  Future<void> onLoad() async {
    add(RectangleHitbox());
  }

  @override
  void update(double dt) {
    super.update(dt);
    position.y += speed * dt;
  }

  @override
  void render(Canvas canvas) {
    final paint = Paint()..color = Colors.red;
    
    switch (shapeType) {
      case ShapeType.circle:
        canvas.drawCircle(Offset(size.x / 2, size.y / 2), size.x / 2, paint);
        break;
      case ShapeType.square:
        canvas.drawRect(size.toRect(), paint);
        break;
      case ShapeType.triangle:
        final path = Path()
          ..moveTo(size.x / 2, 0)
          ..lineTo(0, size.y)
          ..lineTo(size.x, size.y)
          ..close();
        canvas.drawPath(path, paint);
        break;
    }
  }

  void updateSpeed(double newSpeed) {
    speed = newSpeed;
  }
}

class ShapeButton extends PositionComponent with HasCollisionDetection {
  ShapeButton({
    required super.position,
    required this.shapeType,
    required this.onPressed,
  }) : super(size: Vector2(80, 80));

  final ShapeType shapeType;
  final VoidCallback onPressed;

  @override
  Future<void> onLoad() async {
    add(RectangleHitbox());
  }

  @override
  void render(Canvas canvas) {
    final paint = Paint()..color = Colors.grey;
    canvas.drawRect(size.toRect(), paint);
    
    final shapePaint = Paint()..color = Colors.white;
    
    switch (shapeType) {
      case ShapeType.circle:
        canvas.drawCircle(Offset(size.x / 2, size.y / 2), 25, shapePaint);
        break;
      case ShapeType.square:
        canvas.drawRect(Rect.fromCenter(
          center: Offset(size.x / 2, size.y / 2),
          width: 40,
          height: 40,
        ), shapePaint);
        break;
      case ShapeType.triangle:
        final path = Path()
          ..moveTo(size.x / 2, 15)
          ..lineTo(20, 55)
          ..lineTo(60, 55)
          ..close();
        canvas.drawPath(path, shapePaint);
        break;
    }
  }

  // TODO: Implement input handling
  void handleTapDown(Vector2 position) {
    onPressed();
  }
}
