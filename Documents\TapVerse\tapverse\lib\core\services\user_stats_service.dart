import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../models/user_model.dart';
import '../models/inventory_item.dart';

class UserStatsService {
  static final UserStatsService _instance = UserStatsService._internal();
  factory UserStatsService() => _instance;
  UserStatsService._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;

  // Get user data stream
  Stream<UserModel?> getUserDataStream(String uid) {
    return _firestore
        .collection('users')
        .doc(uid)
        .snapshots()
        .map((doc) {
      if (doc.exists) {
        return UserModel.fromFirestore(doc);
      }
      return null;
    });
  }

  // Get user data once
  Future<UserModel?> getUserData(String uid) async {
    try {
      final doc = await _firestore.collection('users').doc(uid).get();
      if (doc.exists) {
        return UserModel.fromFirestore(doc);
      }
      return null;
    } catch (e) {
      print('Error getting user data: $e');
      return null;
    }
  }

  // Get user inventory stream
  Stream<List<InventoryItem>> getUserInventoryStream(String uid) {
    return _firestore
        .collection('users')
        .doc(uid)
        .collection('inventory')
        .orderBy('ownedAt', descending: true)
        .snapshots()
        .map((snapshot) {
      return snapshot.docs
          .map((doc) => InventoryItem.fromFirestore(doc))
          .toList();
    });
  }

  // Get user inventory once
  Future<List<InventoryItem>> getUserInventory(String uid) async {
    try {
      final snapshot = await _firestore
          .collection('users')
          .doc(uid)
          .collection('inventory')
          .orderBy('ownedAt', descending: true)
          .get();
      
      return snapshot.docs
          .map((doc) => InventoryItem.fromFirestore(doc))
          .toList();
    } catch (e) {
      print('Error getting user inventory: $e');
      return [];
    }
  }

  // Get user's high scores for all games
  Future<Map<String, int>> getUserHighScores(String uid) async {
    try {
      final userData = await getUserData(uid);
      return userData?.highScores ?? {};
    } catch (e) {
      print('Error getting user high scores: $e');
      return {};
    }
  }

  // Get user's high score for a specific game
  Future<int> getUserHighScore(String uid, String gameId) async {
    try {
      final highScores = await getUserHighScores(uid);
      return highScores[gameId] ?? 0;
    } catch (e) {
      print('Error getting user high score for $gameId: $e');
      return 0;
    }
  }

  // Update user display name
  Future<bool> updateDisplayName(String uid, String displayName) async {
    try {
      await _firestore.collection('users').doc(uid).update({
        'displayName': displayName,
      });
      
      // Also update Firebase Auth display name
      final user = _auth.currentUser;
      if (user != null && user.uid == uid) {
        await user.updateDisplayName(displayName);
      }
      
      return true;
    } catch (e) {
      print('Error updating display name: $e');
      return false;
    }
  }

  // Get user statistics
  Future<Map<String, dynamic>> getUserStatistics(String uid) async {
    try {
      final userData = await getUserData(uid);
      final inventory = await getUserInventory(uid);
      
      if (userData == null) {
        return {};
      }

      final stats = {
        'totalTokens': userData.tokens,
        'totalGamesPlayed': userData.totalGamesPlayed,
        'totalScore': userData.totalScore,
        'inventoryCount': inventory.length,
        'equippedItemsCount': userData.hasEquippedItems() 
            ? userData.safeEquippedItems.length 
            : 0,
        'accountAge': userData.createdAt != null 
            ? DateTime.now().difference(userData.createdAt!).inDays 
            : 0,
        'averageScore': userData.totalGamesPlayed > 0 
            ? (userData.totalScore / userData.totalGamesPlayed).round() 
            : 0,
        'highestScore': userData.highScores.values.isNotEmpty 
            ? userData.highScores.values.reduce((a, b) => a > b ? a : b) 
            : 0,
        'favoriteGame': _getFavoriteGame(userData.highScores),
      };

      return stats;
    } catch (e) {
      print('Error getting user statistics: $e');
      return {};
    }
  }

  String? _getFavoriteGame(Map<String, int> highScores) {
    if (highScores.isEmpty) return null;
    
    String? favoriteGame;
    int highestScore = 0;
    
    for (final entry in highScores.entries) {
      if (entry.value > highestScore) {
        highestScore = entry.value;
        favoriteGame = entry.key;
      }
    }
    
    return favoriteGame;
  }

  // Get leaderboard position for user in a specific game
  Future<int?> getUserLeaderboardPosition(String uid, String gameId) async {
    try {
      final snapshot = await _firestore
          .collection('leaderboards')
          .doc(gameId)
          .collection('scores')
          .orderBy('score', descending: true)
          .get();

      for (int i = 0; i < snapshot.docs.length; i++) {
        if (snapshot.docs[i].id == uid) {
          return i + 1; // Position is 1-based
        }
      }
      
      return null; // User not found in leaderboard
    } catch (e) {
      print('Error getting user leaderboard position: $e');
      return null;
    }
  }

  // Get user's rank across all games
  Future<Map<String, int>> getUserRanksAllGames(String uid) async {
    try {
      final ranks = <String, int>{};
      
      // Get all leaderboards
      final leaderboardsSnapshot = await _firestore
          .collection('leaderboards')
          .get();

      for (final leaderboardDoc in leaderboardsSnapshot.docs) {
        final gameId = leaderboardDoc.id;
        final position = await getUserLeaderboardPosition(uid, gameId);
        if (position != null) {
          ranks[gameId] = position;
        }
      }

      return ranks;
    } catch (e) {
      print('Error getting user ranks for all games: $e');
      return {};
    }
  }

  // Check if user has played recently (within last 7 days)
  Future<bool> hasPlayedRecently(String uid) async {
    try {
      final userData = await getUserData(uid);
      if (userData?.createdAt == null) return false;
      
      final daysSinceCreation = DateTime.now().difference(userData!.createdAt!).inDays;
      return daysSinceCreation <= 7;
    } catch (e) {
      print('Error checking if user played recently: $e');
      return false;
    }
  }

  // Get achievement-like data
  Future<Map<String, bool>> getUserAchievements(String uid) async {
    try {
      final userData = await getUserData(uid);
      final inventory = await getUserInventory(uid);
      final stats = await getUserStatistics(uid);
      
      if (userData == null) return {};

      return {
        'firstGame': userData.totalGamesPlayed >= 1,
        'gamesExplorer': userData.totalGamesPlayed >= 5,
        'gamesMaster': userData.totalGamesPlayed >= 10,
        'tokenCollector': userData.tokens >= 100,
        'tokenHoarder': userData.tokens >= 1000,
        'shopperBeginner': inventory.isNotEmpty,
        'shopperExpert': inventory.length >= 5,
        'highScorer': (stats['highestScore'] ?? 0) >= 100,
        'consistent': userData.totalGamesPlayed >= 3,
        'veteran': (stats['accountAge'] ?? 0) >= 7,
      };
    } catch (e) {
      print('Error getting user achievements: $e');
      return {};
    }
  }
}
