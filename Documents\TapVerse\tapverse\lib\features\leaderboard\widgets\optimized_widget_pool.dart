import 'package:flutter/material.dart';
import 'dart:collection';

/// Widget pool for reusing expensive widgets to improve performance
class OptimizedWidgetPool<T extends Widget> {
  final Queue<T> _availableWidgets = Queue<T>();
  final Set<T> _usedWidgets = <T>{};
  final T Function() _widgetFactory;
  final int _maxPoolSize;
  final int _initialPoolSize;

  OptimizedWidgetPool({
    required T Function() widgetFactory,
    int maxPoolSize = 20,
    int initialPoolSize = 5,
  }) : _widgetFactory = widgetFactory,
       _maxPoolSize = maxPoolSize,
       _initialPoolSize = initialPoolSize {
    _initializePool();
  }

  /// Get a widget from the pool
  T acquire() {
    if (_availableWidgets.isNotEmpty) {
      final widget = _availableWidgets.removeFirst();
      _usedWidgets.add(widget);
      return widget;
    }

    // Create new widget if pool is empty
    final widget = _widgetFactory();
    _usedWidgets.add(widget);
    return widget;
  }

  /// Return a widget to the pool
  void release(T widget) {
    if (_usedWidgets.remove(widget)) {
      if (_availableWidgets.length < _maxPoolSize) {
        _availableWidgets.add(widget);
      }
      // If pool is full, widget will be garbage collected
    }
  }

  /// Get pool statistics
  PoolStats get stats => PoolStats(
    available: _availableWidgets.length,
    used: _usedWidgets.length,
    total: _availableWidgets.length + _usedWidgets.length,
    maxSize: _maxPoolSize,
  );

  /// Clear the pool
  void clear() {
    _availableWidgets.clear();
    _usedWidgets.clear();
  }

  void _initializePool() {
    for (int i = 0; i < _initialPoolSize; i++) {
      _availableWidgets.add(_widgetFactory());
    }
  }
}

/// Statistics for widget pool
class PoolStats {
  final int available;
  final int used;
  final int total;
  final int maxSize;

  PoolStats({
    required this.available,
    required this.used,
    required this.total,
    required this.maxSize,
  });

  double get utilizationRate => total > 0 ? used / total : 0.0;
}

/// Optimized list view that reuses widgets
class OptimizedListView extends StatefulWidget {
  final int itemCount;
  final Widget Function(BuildContext context, int index) itemBuilder;
  final ScrollController? controller;
  final EdgeInsets? padding;
  final bool shrinkWrap;
  final ScrollPhysics? physics;

  const OptimizedListView({
    super.key,
    required this.itemCount,
    required this.itemBuilder,
    this.controller,
    this.padding,
    this.shrinkWrap = false,
    this.physics,
  });

  @override
  State<OptimizedListView> createState() => _OptimizedListViewState();
}

class _OptimizedListViewState extends State<OptimizedListView> {
  final Map<int, Widget> _widgetCache = {};
  final Set<int> _visibleIndices = <int>{};

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      controller: widget.controller,
      padding: widget.padding,
      shrinkWrap: widget.shrinkWrap,
      physics: widget.physics,
      itemCount: widget.itemCount,
      itemBuilder: (context, index) {
        // Cache widgets for better performance
        if (!_widgetCache.containsKey(index)) {
          _widgetCache[index] = widget.itemBuilder(context, index);
        }

        _visibleIndices.add(index);
        
        // Clean up cache for items that are far from view
        _cleanupCache(index);

        return _widgetCache[index]!;
      },
    );
  }

  void _cleanupCache(int currentIndex) {
    // Remove cached widgets that are far from current view
    const cacheWindow = 20; // Keep widgets within 20 items of current view
    
    _widgetCache.removeWhere((index, widget) {
      return (index - currentIndex).abs() > cacheWindow;
    });
  }

  @override
  void dispose() {
    _widgetCache.clear();
    _visibleIndices.clear();
    super.dispose();
  }
}

/// Optimized grid view with widget recycling
class OptimizedGridView extends StatefulWidget {
  final int itemCount;
  final Widget Function(BuildContext context, int index) itemBuilder;
  final SliverGridDelegate gridDelegate;
  final ScrollController? controller;
  final EdgeInsets? padding;
  final bool shrinkWrap;
  final ScrollPhysics? physics;

  const OptimizedGridView({
    super.key,
    required this.itemCount,
    required this.itemBuilder,
    required this.gridDelegate,
    this.controller,
    this.padding,
    this.shrinkWrap = false,
    this.physics,
  });

  @override
  State<OptimizedGridView> createState() => _OptimizedGridViewState();
}

class _OptimizedGridViewState extends State<OptimizedGridView> {
  final Map<int, Widget> _widgetCache = {};

  @override
  Widget build(BuildContext context) {
    return GridView.builder(
      controller: widget.controller,
      padding: widget.padding,
      shrinkWrap: widget.shrinkWrap,
      physics: widget.physics,
      gridDelegate: widget.gridDelegate,
      itemCount: widget.itemCount,
      itemBuilder: (context, index) {
        if (!_widgetCache.containsKey(index)) {
          _widgetCache[index] = widget.itemBuilder(context, index);
        }
        return _widgetCache[index]!;
      },
    );
  }

  @override
  void dispose() {
    _widgetCache.clear();
    super.dispose();
  }
}

/// Mixin for widgets that support pooling
mixin PoolableWidget {
  void reset();
  bool get isReusable;
}

/// Manager for multiple widget pools
class WidgetPoolManager {
  static final WidgetPoolManager _instance = WidgetPoolManager._internal();
  factory WidgetPoolManager() => _instance;
  WidgetPoolManager._internal();

  final Map<Type, OptimizedWidgetPool> _pools = {};

  /// Get or create a pool for a widget type
  OptimizedWidgetPool<T> getPool<T extends Widget>(
    T Function() factory, {
    int maxPoolSize = 20,
    int initialPoolSize = 5,
  }) {
    if (!_pools.containsKey(T)) {
      _pools[T] = OptimizedWidgetPool<T>(
        widgetFactory: factory,
        maxPoolSize: maxPoolSize,
        initialPoolSize: initialPoolSize,
      );
    }
    return _pools[T] as OptimizedWidgetPool<T>;
  }

  /// Get statistics for all pools
  Map<Type, PoolStats> getAllStats() {
    return _pools.map((type, pool) => MapEntry(type, pool.stats));
  }

  /// Clear all pools
  void clearAllPools() {
    for (final pool in _pools.values) {
      pool.clear();
    }
    _pools.clear();
  }
}

/// Optimized image widget with caching
class OptimizedImage extends StatefulWidget {
  final String imageUrl;
  final double? width;
  final double? height;
  final BoxFit? fit;
  final Widget? placeholder;
  final Widget? errorWidget;

  const OptimizedImage({
    super.key,
    required this.imageUrl,
    this.width,
    this.height,
    this.fit,
    this.placeholder,
    this.errorWidget,
  });

  @override
  State<OptimizedImage> createState() => _OptimizedImageState();
}

class _OptimizedImageState extends State<OptimizedImage> {
  static final Map<String, ImageProvider> _imageCache = {};

  @override
  Widget build(BuildContext context) {
    // Use cached image provider if available
    ImageProvider imageProvider;
    if (_imageCache.containsKey(widget.imageUrl)) {
      imageProvider = _imageCache[widget.imageUrl]!;
    } else {
      imageProvider = NetworkImage(widget.imageUrl);
      _imageCache[widget.imageUrl] = imageProvider;
      
      // Limit cache size
      if (_imageCache.length > 100) {
        final firstKey = _imageCache.keys.first;
        _imageCache.remove(firstKey);
      }
    }

    return Image(
      image: imageProvider,
      width: widget.width,
      height: widget.height,
      fit: widget.fit,
      frameBuilder: (context, child, frame, wasSynchronouslyLoaded) {
        if (wasSynchronouslyLoaded || frame != null) {
          return child;
        }
        return widget.placeholder ?? const SizedBox.shrink();
      },
      errorBuilder: (context, error, stackTrace) {
        return widget.errorWidget ?? const Icon(Icons.error);
      },
    );
  }
}

/// Performance-optimized container with reduced rebuilds
class OptimizedContainer extends StatelessWidget {
  final Widget? child;
  final EdgeInsets? padding;
  final EdgeInsets? margin;
  final Color? color;
  final Decoration? decoration;
  final double? width;
  final double? height;
  final AlignmentGeometry? alignment;

  const OptimizedContainer({
    super.key,
    this.child,
    this.padding,
    this.margin,
    this.color,
    this.decoration,
    this.width,
    this.height,
    this.alignment,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: padding,
      margin: margin,
      color: color,
      decoration: decoration,
      width: width,
      height: height,
      alignment: alignment,
      child: child,
    );
  }
}

/// Lazy loading widget that only builds when visible
class LazyWidget extends StatefulWidget {
  final Widget Function() builder;
  final Widget? placeholder;
  final double threshold;

  const LazyWidget({
    super.key,
    required this.builder,
    this.placeholder,
    this.threshold = 100.0,
  });

  @override
  State<LazyWidget> createState() => _LazyWidgetState();
}

class _LazyWidgetState extends State<LazyWidget> {
  bool _isVisible = false;
  Widget? _builtWidget;

  @override
  Widget build(BuildContext context) {
    return VisibilityDetector(
      key: widget.key ?? UniqueKey(),
      onVisibilityChanged: (info) {
        if (info.visibleFraction > 0 && !_isVisible) {
          setState(() {
            _isVisible = true;
            _builtWidget = widget.builder();
          });
        }
      },
      child: _isVisible && _builtWidget != null
          ? _builtWidget!
          : widget.placeholder ?? const SizedBox.shrink(),
    );
  }
}

/// Simple visibility detector for lazy loading
class VisibilityDetector extends StatefulWidget {
  @override
  final Key key;
  final Widget child;
  final Function(VisibilityInfo) onVisibilityChanged;

  const VisibilityDetector({
    required this.key,
    required this.child,
    required this.onVisibilityChanged,
  }) : super(key: key);

  @override
  State<VisibilityDetector> createState() => _VisibilityDetectorState();
}

class _VisibilityDetectorState extends State<VisibilityDetector> {
  @override
  Widget build(BuildContext context) {
    // Simplified visibility detection
    WidgetsBinding.instance.addPostFrameCallback((_) {
      widget.onVisibilityChanged(VisibilityInfo(visibleFraction: 1.0));
    });
    
    return widget.child;
  }
}

class VisibilityInfo {
  final double visibleFraction;
  VisibilityInfo({required this.visibleFraction});
}
