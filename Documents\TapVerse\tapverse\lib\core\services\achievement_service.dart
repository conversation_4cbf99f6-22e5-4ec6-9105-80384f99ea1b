import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/achievement.dart';
import 'token_service.dart';
import 'auth_service.dart';

class AchievementService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final TokenService _tokenService;
  final AuthService _authService;

  AchievementService(this._tokenService, this._authService);

  // Get all available achievements
  List<Achievement> getAllAchievements() {
    return [
      Achievement(
        id: 'firstGame',
        title: 'First Steps',
        description: 'Play your first game',
        icon: Icons.play_arrow,
        color: Colors.green,
        tokenReward: 10,
      ),
      Achievement(
        id: 'gamesExplorer',
        title: 'Explorer',
        description: 'Play 5 different games',
        icon: Icons.explore,
        color: Colors.blue,
        tokenReward: 25,
      ),
      Achievement(
        id: 'gamesMaster',
        title: 'Game Master',
        description: 'Play 10 different games',
        icon: Icons.emoji_events,
        color: Colors.purple,
        tokenReward: 50,
      ),
      Achievement(
        id: 'tokenCollector',
        title: 'Token Collector',
        description: 'Earn 100 tokens',
        icon: Icons.monetization_on,
        color: Colors.amber,
        tokenReward: 20,
      ),
      Achievement(
        id: 'tokenHoarder',
        title: 'Token Hoarder',
        description: 'Earn 1000 tokens',
        icon: Icons.savings,
        color: Colors.orange,
        tokenReward: 100,
      ),
      Achievement(
        id: 'highScorer',
        title: 'High Scorer',
        description: 'Score over 1000 in any game',
        icon: Icons.star,
        color: Colors.yellow,
        tokenReward: 30,
      ),
      Achievement(
        id: 'perfectionist',
        title: 'Perfectionist',
        description: 'Get a perfect score in any game',
        icon: Icons.grade,
        color: Colors.pink,
        tokenReward: 75,
      ),
      Achievement(
        id: 'dailyPlayer',
        title: 'Daily Player',
        description: 'Play games for 7 consecutive days',
        icon: Icons.calendar_today,
        color: Colors.teal,
        tokenReward: 40,
      ),
    ];
  }

  // Get user's achievement progress
  Future<Map<String, AchievementProgress>> getUserAchievementProgress(String userId) async {
    try {
      final doc = await _firestore
          .collection('users')
          .doc(userId)
          .collection('achievements')
          .doc('progress')
          .get();

      if (!doc.exists) {
        return {};
      }

      final data = doc.data() ?? {};
      final Map<String, AchievementProgress> progress = {};

      for (final entry in data.entries) {
        final achievementData = entry.value as Map<String, dynamic>;
        progress[entry.key] = AchievementProgress(
          achievementId: entry.key,
          isUnlocked: achievementData['isUnlocked'] ?? false,
          isRewardClaimed: achievementData['isRewardClaimed'] ?? false,
          unlockedAt: achievementData['unlockedAt'],
          claimedAt: achievementData['claimedAt'],
          progress: achievementData['progress'] ?? 0,
        );
      }

      return progress;
    } catch (e) {
      print('Error getting user achievement progress: $e');
      return {};
    }
  }

  // Check and unlock achievements based on user stats
  Future<List<Achievement>> checkAndUnlockAchievements(String userId, Map<String, dynamic> userStats) async {
    try {
      final currentProgress = await getUserAchievementProgress(userId);
      final allAchievements = getAllAchievements();
      final newlyUnlocked = <Achievement>[];

      for (final achievement in allAchievements) {
        final progress = currentProgress[achievement.id];
        
        // Skip if already unlocked
        if (progress?.isUnlocked == true) continue;

        bool shouldUnlock = false;

        // Check achievement conditions
        switch (achievement.id) {
          case 'firstGame':
            shouldUnlock = (userStats['totalGamesPlayed'] ?? 0) >= 1;
            break;
          case 'gamesExplorer':
            shouldUnlock = (userStats['uniqueGamesPlayed'] ?? 0) >= 5;
            break;
          case 'gamesMaster':
            shouldUnlock = (userStats['uniqueGamesPlayed'] ?? 0) >= 10;
            break;
          case 'tokenCollector':
            shouldUnlock = (userStats['totalTokensEarned'] ?? 0) >= 100;
            break;
          case 'tokenHoarder':
            shouldUnlock = (userStats['totalTokensEarned'] ?? 0) >= 1000;
            break;
          case 'highScorer':
            shouldUnlock = (userStats['highestScore'] ?? 0) >= 1000;
            break;
          case 'perfectionist':
            shouldUnlock = userStats['hasPerfectScore'] == true;
            break;
          case 'dailyPlayer':
            shouldUnlock = (userStats['consecutiveDays'] ?? 0) >= 7;
            break;
        }

        if (shouldUnlock) {
          await _unlockAchievement(userId, achievement.id);
          newlyUnlocked.add(achievement);
        }
      }

      return newlyUnlocked;
    } catch (e) {
      print('Error checking achievements: $e');
      return [];
    }
  }

  // Unlock a specific achievement
  Future<void> _unlockAchievement(String userId, String achievementId) async {
    try {
      await _firestore
          .collection('users')
          .doc(userId)
          .collection('achievements')
          .doc('progress')
          .set({
        achievementId: {
          'isUnlocked': true,
          'isRewardClaimed': false,
          'unlockedAt': FieldValue.serverTimestamp(),
        }
      }, SetOptions(merge: true));
    } catch (e) {
      print('Error unlocking achievement: $e');
    }
  }

  // Claim achievement reward
  Future<bool> claimAchievementReward(String achievementId) async {
    try {
      final user = _authService.currentUser;
      if (user == null) return false;

      final achievement = getAllAchievements().firstWhere(
        (a) => a.id == achievementId,
        orElse: () => throw Exception('Achievement not found'),
      );

      final progress = await getUserAchievementProgress(user.uid);
      final achievementProgress = progress[achievementId];

      if (achievementProgress == null || !achievementProgress.isUnlocked || achievementProgress.isRewardClaimed) {
        return false;
      }

      // Award tokens
      await _tokenService.addTokens(achievement.tokenReward, 'Achievement: ${achievement.title}');

      // Mark reward as claimed
      await _firestore
          .collection('users')
          .doc(user.uid)
          .collection('achievements')
          .doc('progress')
          .set({
        achievementId: {
          'isRewardClaimed': true,
          'claimedAt': FieldValue.serverTimestamp(),
        }
      }, SetOptions(merge: true));

      return true;
    } catch (e) {
      print('Error claiming achievement reward: $e');
      return false;
    }
  }

  // Get unclaimed achievements
  Future<List<Achievement>> getUnclaimedAchievements(String userId) async {
    try {
      final progress = await getUserAchievementProgress(userId);
      final allAchievements = getAllAchievements();
      
      return allAchievements.where((achievement) {
        final achievementProgress = progress[achievement.id];
        return achievementProgress?.isUnlocked == true && 
               achievementProgress?.isRewardClaimed == false;
      }).toList();
    } catch (e) {
      print('Error getting unclaimed achievements: $e');
      return [];
    }
  }

  // Stream of user achievement progress
  Stream<Map<String, AchievementProgress>> getUserAchievementProgressStream(String userId) {
    return _firestore
        .collection('users')
        .doc(userId)
        .collection('achievements')
        .doc('progress')
        .snapshots()
        .map((doc) {
      if (!doc.exists) return <String, AchievementProgress>{};

      final data = doc.data() ?? {};
      final Map<String, AchievementProgress> progress = {};

      for (final entry in data.entries) {
        final achievementData = entry.value as Map<String, dynamic>;
        progress[entry.key] = AchievementProgress(
          achievementId: entry.key,
          isUnlocked: achievementData['isUnlocked'] ?? false,
          isRewardClaimed: achievementData['isRewardClaimed'] ?? false,
          unlockedAt: achievementData['unlockedAt'],
          claimedAt: achievementData['claimedAt'],
          progress: achievementData['progress'] ?? 0,
        );
      }

      return progress;
    });
  }
}
