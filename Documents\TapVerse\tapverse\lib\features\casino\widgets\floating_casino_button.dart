import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../../../core/providers/app_providers.dart';
import '../../../core/services/game_navigation_service.dart';
import '../../auth/widgets/account_upgrade_dialog.dart';
import 'casino_effects.dart';

class FloatingCasinoButton extends ConsumerStatefulWidget {
  const FloatingCasinoButton({super.key});

  @override
  ConsumerState<FloatingCasinoButton> createState() => _FloatingCasinoButtonState();
}

class _FloatingCasinoButtonState extends ConsumerState<FloatingCasinoButton>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late AnimationController _rotationController;
  bool _isExpanded = false;

  @override
  void initState() {
    super.initState();
    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    )..repeat();
    
    _rotationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _rotationController.dispose();
    super.dispose();
  }

  void _toggleExpanded() {
    setState(() {
      _isExpanded = !_isExpanded;
    });
    
    if (_isExpanded) {
      _rotationController.forward();
    } else {
      _rotationController.reverse();
    }
  }

  @override
  Widget build(BuildContext context) {
    final casinoService = ref.watch(casinoServiceProvider);
    final gameNavigation = ref.watch(gameNavigationServiceProvider);
    final soundService = ref.watch(soundServiceProvider);
    final vibrationService = ref.watch(vibrationServiceProvider);
    
    final isAuthenticated = casinoService.isUserAuthenticated;
    final casinoGames = gameNavigation.allCasinoGames;

    return Positioned(
      right: 16,
      bottom: 100,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          // Expanded casino games list
          if (_isExpanded) ...[
            ...casinoGames.asMap().entries.map((entry) {
              final index = entry.key;
              final game = entry.value;
              
              return Container(
                margin: const EdgeInsets.only(bottom: 8),
                child: _buildCasinoGameButton(
                  game: game,
                  onTap: () async {
                    await soundService.playButtonClick();
                    await vibrationService.onButtonTap();
                    _toggleExpanded();
                    if (context.mounted) {
                      if (isAuthenticated) {
                        gameNavigation.navigateToCasinoGame(context, game.type);
                      } else {
                        _showAuthenticationRequired(context);
                      }
                    }
                  },
                ),
              ).animate(delay: Duration(milliseconds: index * 100))
               .slideX(begin: 1, end: 0, duration: 300.ms)
               .fadeIn(duration: 300.ms);
            }),
            const SizedBox(height: 8),
          ],
          
          // Main casino button
          GestureDetector(
            onTap: () async {
              await soundService.playButtonClick();
              await vibrationService.onButtonTap();
              
              if (isAuthenticated) {
                _toggleExpanded();
              } else {
                _showAuthenticationRequired(context);
              }
            },
            child: AnimatedBuilder(
              animation: _pulseController,
              builder: (context, child) {
                return Container(
                  width: 64,
                  height: 64,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        Colors.amber.shade600,
                        Colors.orange.shade700,
                        Colors.red.shade600,
                      ],
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.amber.withOpacity(0.4 + 0.2 * _pulseController.value),
                        blurRadius: 12 + 8 * _pulseController.value,
                        spreadRadius: 2 + 4 * _pulseController.value,
                      ),
                    ],
                  ),
                  child: AnimatedBuilder(
                    animation: _rotationController,
                    builder: (context, child) {
                      return Transform.rotate(
                        angle: _rotationController.value * 0.5,
                        child: Icon(
                          _isExpanded ? Icons.close : Icons.casino,
                          color: Colors.white,
                          size: 28,
                        ),
                      );
                    },
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCasinoGameButton({
    required CasinoGameInfo game,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              game.primaryColor.withOpacity(0.9),
              game.secondaryColor.withOpacity(0.9),
            ],
          ),
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: game.primaryColor.withOpacity(0.3),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              game.icon,
              color: Colors.white,
              size: 20,
            ),
            const SizedBox(width: 8),
            Flexible(
              child: Text(
                game.name,
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                  fontSize: 12,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            const SizedBox(width: 4),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.2),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                '${game.entryCost}',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 10,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showAuthenticationRequired(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.lock, color: Colors.orange),
            SizedBox(width: 8),
            Text('Account Required'),
          ],
        ),
        content: const Text(
          'Casino games require a registered account to ensure fair play and secure transactions. Would you like to create an account?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Maybe Later'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              showDialog(
                context: context,
                builder: (context) => const AccountUpgradeDialog(),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.orange,
              foregroundColor: Colors.white,
            ),
            child: const Text('Create Account'),
          ),
        ],
      ),
    );
  }
}
