import 'dart:math';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../../../core/models/game_state.dart';
import '../../../core/providers/game_providers.dart';
import '../../../core/services/game_feedback_service.dart';
import '../../../shared/widgets/base_game_widget.dart';

class StackifyGame extends BaseGameWidget {
  static const GameConfig gameConfig = GameConfig(
    gameId: 'stackify',
    gameName: 'Stackify',
    scoreMultiplier: 2,
    hasLives: false,
    hasLevels: false,
    hasTimer: false,
  );

  const StackifyGame({
    super.key,
    super.onGameComplete,
    super.onGameExit,
  }) : super(config: gameConfig);

  @override
  Widget buildGameContent(BuildContext context, WidgetRef ref, GameState gameState) {
    return const _StackifyGameContent();
  }
}

class Block {
  double x;
  double y;
  double width;
  final double height;
  final Color color;
  bool isMoving;
  bool isStacked;

  Block({
    required this.x,
    required this.y,
    required this.width,
    required this.height,
    required this.color,
    this.isMoving = false,
    this.isStacked = false,
  });
}

class _StackifyGameContent extends ConsumerStatefulWidget {
  const _StackifyGameContent();

  @override
  ConsumerState<_StackifyGameContent> createState() => _StackifyGameContentState();
}

class _StackifyGameContentState extends ConsumerState<_StackifyGameContent>
    with TickerProviderStateMixin {
  
  // Game state
  late AnimationController _stackController;
  late AnimationController _perfectController;
  late AnimationController _shrinkController;
  
  // Blocks
  final List<Block> _stackedBlocks = [];
  Block? _movingBlock;
  double _movingDirection = 1;
  final double _movingSpeed = 150;
  
  // Game mechanics
  int _stackHeight = 0;
  double _currentBlockWidth = 200;
  final double _blockHeight = 40;
  final double _baseY = 520;
  bool _showPerfectEffect = false;
  bool _gameActive = true;
  
  // Colors for blocks
  final List<Color> _blockColors = [
    Colors.red[400]!,
    Colors.blue[400]!,
    Colors.green[400]!,
    Colors.orange[400]!,
    Colors.purple[400]!,
    Colors.cyan[400]!,
    Colors.pink[400]!,
    Colors.yellow[600]!,
  ];
  
  // Constants
  static const double _gameWidth = 400;
  static const double _gameHeight = 600;

  @override
  void initState() {
    super.initState();
    
    _stackController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    
    _perfectController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );
    
    _shrinkController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _initializeGame();
    _startMovingBlock();
  }

  @override
  void dispose() {
    _stackController.dispose();
    _perfectController.dispose();
    _shrinkController.dispose();
    super.dispose();
  }

  void _initializeGame() {
    // Create base block
    _stackedBlocks.add(Block(
      x: _gameWidth / 2 - _currentBlockWidth / 2,
      y: _baseY,
      width: _currentBlockWidth,
      height: _blockHeight,
      color: _blockColors[0],
      isStacked: true,
    ));
    _stackHeight = 1;
  }

  void _startMovingBlock() {
    if (!_gameActive) return;
    
    setState(() {
      _movingBlock = Block(
        x: 0,
        y: _baseY - _stackHeight * _blockHeight,
        width: _currentBlockWidth,
        height: _blockHeight,
        color: _blockColors[_stackHeight % _blockColors.length],
        isMoving: true,
      );
    });
    
    _moveBlock();
  }

  void _moveBlock() {
    const frameRate = 60;
    const frameDuration = Duration(milliseconds: 1000 ~/ frameRate);
    const deltaTime = 1.0 / frameRate;
    
    void updateBlock() {
      if (_movingBlock == null || !_movingBlock!.isMoving || !mounted) return;
      
      setState(() {
        _movingBlock!.x += _movingDirection * _movingSpeed * deltaTime;
        
        // Bounce off walls
        if (_movingBlock!.x <= 0) {
          _movingBlock!.x = 0;
          _movingDirection = 1;
        } else if (_movingBlock!.x + _movingBlock!.width >= _gameWidth) {
          _movingBlock!.x = _gameWidth - _movingBlock!.width;
          _movingDirection = -1;
        }
      });
      
      if (_movingBlock!.isMoving) {
        Future.delayed(frameDuration, updateBlock);
      }
    }
    
    updateBlock();
  }

  void _onTap() {
    final gameState = ref.read(gameStateProvider(StackifyGame.gameConfig));
    if (!gameState.isPlaying || _movingBlock == null || !_movingBlock!.isMoving) return;
    
    _stackBlock();
  }

  void _stackBlock() {
    if (_movingBlock == null) return;
    
    setState(() {
      _movingBlock!.isMoving = false;
    });
    
    final lastBlock = _stackedBlocks.last;
    final movingLeft = _movingBlock!.x;
    final movingRight = _movingBlock!.x + _movingBlock!.width;
    final lastLeft = lastBlock.x;
    final lastRight = lastBlock.x + lastBlock.width;
    
    // Calculate overlap
    final overlapLeft = max(movingLeft, lastLeft);
    final overlapRight = min(movingRight, lastRight);
    final overlapWidth = max(0.0, overlapRight - overlapLeft);
    
    if (overlapWidth <= 0) {
      // No overlap - game over
      _gameOver();
      return;
    }
    
    // Check for perfect alignment
    final isPerfect = (movingLeft - lastLeft).abs() < 5 && 
                     (movingRight - lastRight).abs() < 5;
    
    if (isPerfect) {
      // Perfect stack - no shrinking
      _handlePerfectStack();
    } else {
      // Partial overlap - shrink block
      _handlePartialStack(overlapLeft, overlapWidth);
    }
    
    _stackHeight++;
    
    // Add score
    final gameNotifier = ref.read(gameStateProvider(StackifyGame.gameConfig).notifier);
    gameNotifier.addScore(isPerfect ? 20 : 10);
    
    // Trigger feedback
    if (isPerfect) {
      ref.read(gameFeedbackServiceProvider).triggerComboFeedback(2);
    } else {
      ref.read(gameFeedbackServiceProvider).triggerFeedback(GameEvent.scoreIncrease);
    }
    
    // Play stack animation
    _stackController.forward().then((_) {
      _stackController.reset();
      _startMovingBlock();
    });
  }

  void _handlePerfectStack() {
    setState(() {
      _movingBlock!.x = _stackedBlocks.last.x;
      _movingBlock!.isStacked = true;
      _stackedBlocks.add(_movingBlock!);
      _movingBlock = null;
      _showPerfectEffect = true;
    });
    
    // Play perfect animation
    _perfectController.forward().then((_) {
      _perfectController.reset();
      setState(() {
        _showPerfectEffect = false;
      });
    });
  }

  void _handlePartialStack(double overlapLeft, double overlapWidth) {
    setState(() {
      _movingBlock!.x = overlapLeft;
      _movingBlock!.width = overlapWidth;
      _movingBlock!.isStacked = true;
      _stackedBlocks.add(_movingBlock!);
      _currentBlockWidth = overlapWidth;
      _movingBlock = null;
    });
    
    // Play shrink animation
    _shrinkController.forward().then((_) => _shrinkController.reset());
    
    // Check if block is too small
    if (_currentBlockWidth < 20) {
      _gameOver();
    }
  }

  void _gameOver() {
    setState(() {
      _gameActive = false;
    });
    
    final gameNotifier = ref.read(gameStateProvider(StackifyGame.gameConfig).notifier);
    gameNotifier.endGame(reason: 'Stack collapsed or block too small');
  }

  @override
  Widget build(BuildContext context) {
    final gameState = ref.watch(gameStateProvider(StackifyGame.gameConfig));
    
    return Container(
      width: _gameWidth,
      height: _gameHeight,
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [Color(0xFF2c3e50), Color(0xFF34495e)],
        ),
      ),
      child: GestureDetector(
        onTap: _onTap,
        child: Stack(
          children: [
            // Background
            _buildBackground(),
            
            // Stacked blocks
            ..._stackedBlocks.map((block) => _buildBlock(block)),
            
            // Moving block
            if (_movingBlock != null) _buildBlock(_movingBlock!),
            
            // Perfect effect
            if (_showPerfectEffect) _buildPerfectEffect(),
            
            // Game info
            _buildGameInfo(gameState),
            
            // Instructions
            if (!gameState.isPlaying) _buildInstructions(),
          ],
        ),
      ),
    );
  }

  Widget _buildBackground() {
    return Positioned.fill(
      child: CustomPaint(
        painter: _BackgroundPainter(),
      ),
    );
  }

  Widget _buildBlock(Block block) {
    return Positioned(
      left: block.x,
      top: block.y,
      child: ScaleTransition(
        scale: block.isMoving 
            ? const AlwaysStoppedAnimation(1.0)
            : Tween<double>(begin: 1.0, end: 1.1).animate(_stackController),
        child: Container(
          width: block.width,
          height: block.height,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                block.color.withOpacity(0.8),
                block.color,
                block.color.withOpacity(0.6),
              ],
            ),
            borderRadius: BorderRadius.circular(4),
            border: Border.all(color: Colors.white.withOpacity(0.3), width: 1),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.3),
                blurRadius: 4,
                offset: const Offset(2, 2),
              ),
            ],
          ),
          child: Center(
            child: Text(
              '${_stackedBlocks.indexOf(block) + 1}',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildPerfectEffect() {
    final lastBlock = _stackedBlocks.last;
    
    return Positioned(
      left: lastBlock.x - 20,
      top: lastBlock.y - 20,
      child: ScaleTransition(
        scale: _perfectController,
        child: Container(
          width: lastBlock.width + 40,
          height: lastBlock.height + 40,
          decoration: BoxDecoration(
            border: Border.all(color: Colors.yellow, width: 3),
            borderRadius: BorderRadius.circular(8),
          ),
          child: const Center(
            child: Text(
              'PERFECT!',
              style: TextStyle(
                color: Colors.yellow,
                fontSize: 16,
                fontWeight: FontWeight.bold,
                shadows: [
                  Shadow(
                    offset: Offset(1, 1),
                    blurRadius: 2,
                    color: Colors.black,
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildGameInfo(GameState gameState) {
    return Positioned(
      top: 30,
      left: 20,
      right: 20,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.black54,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              children: [
                const Text(
                  'Score',
                  style: TextStyle(color: Colors.white, fontSize: 12),
                ),
                Text(
                  '${gameState.score}',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.black54,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              children: [
                const Text(
                  'Height',
                  style: TextStyle(color: Colors.white, fontSize: 12),
                ),
                Text(
                  '$_stackHeight',
                  style: const TextStyle(
                    color: Colors.cyan,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.black54,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              children: [
                const Text(
                  'Width',
                  style: TextStyle(color: Colors.white, fontSize: 12),
                ),
                Text(
                  '${_currentBlockWidth.toInt()}',
                  style: TextStyle(
                    color: _currentBlockWidth < 50 ? Colors.red : Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInstructions() {
    return Positioned(
      bottom: 100,
      left: 0,
      right: 0,
      child: Center(
        child: Container(
          padding: EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.black54,
            borderRadius: BorderRadius.all(Radius.circular(12)),
          ),
          child: Text(
            'Tap to drop the moving block!\nAlign perfectly for bonus points.\nMisaligned blocks shrink the platform.\nDon\'t let it get too small!',
            textAlign: TextAlign.center,
            style: TextStyle(
              color: Colors.white,
              fontSize: 16,
            ),
          ),
        ),
      ),
    );
  }
}

class _BackgroundPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    // Draw city skyline
    final buildingPaint = Paint()
      ..color = Colors.black.withOpacity(0.3)
      ..style = PaintingStyle.fill;
    
    // Building 1
    canvas.drawRect(Rect.fromLTWH(50, size.height - 200, 60, 200), buildingPaint);
    
    // Building 2
    canvas.drawRect(Rect.fromLTWH(130, size.height - 150, 40, 150), buildingPaint);
    
    // Building 3
    canvas.drawRect(Rect.fromLTWH(190, size.height - 180, 50, 180), buildingPaint);
    
    // Building 4
    canvas.drawRect(Rect.fromLTWH(260, size.height - 120, 45, 120), buildingPaint);
    
    // Building 5
    canvas.drawRect(Rect.fromLTWH(320, size.height - 160, 55, 160), buildingPaint);
    
    // Draw stars
    final starPaint = Paint()
      ..color = Colors.white.withOpacity(0.8)
      ..style = PaintingStyle.fill;
    
    final random = Random(42); // Fixed seed for consistent stars
    for (int i = 0; i < 30; i++) {
      final x = random.nextDouble() * size.width;
      final y = random.nextDouble() * size.height * 0.4;
      canvas.drawCircle(Offset(x, y), 1, starPaint);
    }
    
    // Draw moon
    final moonPaint = Paint()
      ..color = Colors.yellow[100]!
      ..style = PaintingStyle.fill;
    
    canvas.drawCircle(Offset(size.width - 60, 60), 20, moonPaint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
