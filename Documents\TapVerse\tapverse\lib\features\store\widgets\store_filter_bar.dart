import 'package:flutter/material.dart';
import '../../../core/models/store_item.dart';

class StoreFilterBar extends StatefulWidget {
  final Function(String) onSearchChanged;
  final Function(StoreItemType?) onTypeFilterChanged;

  const StoreFilterBar({
    super.key,
    required this.onSearchChanged,
    required this.onTypeFilterChanged,
  });

  @override
  State<StoreFilterBar> createState() => _StoreFilterBarState();
}

class _StoreFilterBarState extends State<StoreFilterBar> {
  final TextEditingController _searchController = TextEditingController();
  StoreItemType? _selectedType;

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Search bar
          TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'Search items...',
              prefixIcon: const Icon(Icons.search),
              suffixIcon: _searchController.text.isNotEmpty
                  ? IconButton(
                      icon: const Icon(Icons.clear),
                      onPressed: () {
                        _searchController.clear();
                        widget.onSearchChanged('');
                      },
                    )
                  : null,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide.none,
              ),
              filled: true,
              fillColor: Theme.of(context).colorScheme.surfaceContainerHighest,
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 12,
              ),
            ),
            onChanged: widget.onSearchChanged,
          ),
          
          const SizedBox(height: 12),
          
          // Filter chips
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: [
                // All items chip
                FilterChip(
                  label: const Text('All'),
                  selected: _selectedType == null,
                  onSelected: (selected) {
                    setState(() {
                      _selectedType = null;
                    });
                    widget.onTypeFilterChanged(null);
                  },
                ),
                
                const SizedBox(width: 8),
                
                // Type filter chips
                ...StoreItemType.values.map((type) => Padding(
                  padding: const EdgeInsets.only(right: 8),
                  child: FilterChip(
                    label: Text(_getTypeDisplayName(type)),
                    selected: _selectedType == type,
                    onSelected: (selected) {
                      setState(() {
                        _selectedType = selected ? type : null;
                      });
                      widget.onTypeFilterChanged(selected ? type : null);
                    },
                    avatar: Icon(
                      _getTypeIcon(type),
                      size: 16,
                    ),
                  ),
                )),
              ],
            ),
          ),
        ],
      ),
    );
  }

  String _getTypeDisplayName(StoreItemType type) {
    switch (type) {
      case StoreItemType.cosmetic:
        return 'Cosmetics';
      case StoreItemType.gameUnlock:
        return 'Game Unlocks';
      case StoreItemType.flair:
        return 'Flair';
      case StoreItemType.mysteryDraw:
        return 'Mystery Draws';
      case StoreItemType.tokenBoost:
        return 'Token Boosts';
      case StoreItemType.skin:
        return 'Skins';
      case StoreItemType.effect:
        return 'Effects';
      case StoreItemType.powerup:
        return 'Power-ups';
      case StoreItemType.theme:
        return 'Themes';
      case StoreItemType.sound:
        return 'Sounds';
    }
  }

  IconData _getTypeIcon(StoreItemType type) {
    switch (type) {
      case StoreItemType.cosmetic:
        return Icons.style;
      case StoreItemType.gameUnlock:
        return Icons.lock_open;
      case StoreItemType.flair:
        return Icons.star;
      case StoreItemType.mysteryDraw:
        return Icons.card_giftcard;
      case StoreItemType.tokenBoost:
        return Icons.trending_up;
      case StoreItemType.skin:
        return Icons.palette;
      case StoreItemType.effect:
        return Icons.auto_awesome;
      case StoreItemType.powerup:
        return Icons.flash_on;
      case StoreItemType.theme:
        return Icons.color_lens;
      case StoreItemType.sound:
        return Icons.volume_up;
    }
  }
}
