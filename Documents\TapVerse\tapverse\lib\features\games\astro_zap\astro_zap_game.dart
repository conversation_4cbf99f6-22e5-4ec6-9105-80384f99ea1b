import 'dart:math';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../../../core/models/game_state.dart';
import '../../../core/providers/game_providers.dart';
import '../../../core/services/game_feedback_service.dart';
import '../../../shared/widgets/base_game_widget.dart';

class AstroZapGame extends BaseGameWidget {
  static const GameConfig gameConfig = GameConfig(
    gameId: 'astro_zap',
    gameName: 'AstroZap',
    scoreMultiplier: 3,
    hasLives: false,
    hasLevels: false,
    hasTimer: false,
  );

  const AstroZapGame({
    super.key,
    super.onGameComplete,
    super.onGameExit,
  }) : super(config: gameConfig);

  @override
  Widget buildGameContent(BuildContext context, WidgetRef ref, GameState gameState) {
    return const _AstroZapGameContent();
  }
}

class Asteroid {
  Offset position;
  Offset velocity;
  double size;
  double rotation;
  double rotationSpeed;
  bool isDestroyed;

  Asteroid({
    required this.position,
    required this.velocity,
    required this.size,
    this.rotation = 0,
    required this.rotationSpeed,
    this.isDestroyed = false,
  });
}

class Bullet {
  Offset position;
  final Offset velocity;
  bool isActive;

  Bullet({
    required this.position,
    required this.velocity,
    this.isActive = true,
  });
}

class _AstroZapGameContent extends ConsumerStatefulWidget {
  const _AstroZapGameContent();

  @override
  ConsumerState<_AstroZapGameContent> createState() => _AstroZapGameContentState();
}

class _AstroZapGameContentState extends ConsumerState<_AstroZapGameContent>
    with TickerProviderStateMixin {
  
  // Game state
  late AnimationController _shipController;
  late AnimationController _explosionController;
  
  // Ship state
  Offset _shipPosition = const Offset(200, 500);
  final double _shipSize = 40;
  
  // Game objects
  final List<Asteroid> _asteroids = [];
  final List<Bullet> _bullets = [];
  final Random _random = Random();
  
  // Game mechanics
  double _asteroidSpawnTimer = 0;
  final double _asteroidSpawnInterval = 2.0; // seconds
  final List<Offset> _explosions = [];
  
  // Constants
  static const double _gameWidth = 400;
  static const double _gameHeight = 600;
  static const double _bulletSpeed = 400;
  static const double _bulletSize = 6;

  @override
  void initState() {
    super.initState();
    
    _shipController = AnimationController(
      duration: const Duration(milliseconds: 100),
      vsync: this,
    );
    
    _explosionController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    
    _startGameLoop();
  }

  @override
  void dispose() {
    _shipController.dispose();
    _explosionController.dispose();
    super.dispose();
  }

  void _startGameLoop() {
    const frameRate = 60;
    const frameDuration = Duration(milliseconds: 1000 ~/ frameRate);
    const deltaTime = 1.0 / frameRate;
    
    void gameLoop() {
      if (!mounted) return;
      
      final gameState = ref.read(gameStateProvider(AstroZapGame.gameConfig));
      if (!gameState.isPlaying) {
        Future.delayed(frameDuration, gameLoop);
        return;
      }
      
      setState(() {
        // Update asteroid spawning
        _asteroidSpawnTimer += deltaTime;
        if (_asteroidSpawnTimer >= _asteroidSpawnInterval) {
          _spawnAsteroid();
          _asteroidSpawnTimer = 0;
        }
        
        // Update asteroids
        for (final asteroid in _asteroids) {
          asteroid.position = Offset(
            asteroid.position.dx + asteroid.velocity.dx * deltaTime,
            asteroid.position.dy + asteroid.velocity.dy * deltaTime,
          );
          asteroid.rotation += asteroid.rotationSpeed * deltaTime;
        }
        
        // Update bullets
        for (final bullet in _bullets) {
          bullet.position = Offset(
            bullet.position.dx + bullet.velocity.dx * deltaTime,
            bullet.position.dy + bullet.velocity.dy * deltaTime,
          );
          
          // Deactivate bullets that go off screen
          if (bullet.position.dy < 0) {
            bullet.isActive = false;
          }
        }
        
        // Check collisions
        _checkBulletAsteroidCollisions();
        _checkShipAsteroidCollisions();
        
        // Remove inactive objects
        _asteroids.removeWhere((asteroid) =>
          asteroid.isDestroyed || asteroid.position.dy > _gameHeight + 100);
        _bullets.removeWhere((bullet) => !bullet.isActive);
      });
      
      Future.delayed(frameDuration, gameLoop);
    }
    
    gameLoop();
  }

  void _spawnAsteroid() {
    final x = _random.nextDouble() * _gameWidth;
    final size = 30 + _random.nextDouble() * 40; // 30-70 size
    final speed = 50 + _random.nextDouble() * 100; // 50-150 speed
    final angle = _random.nextDouble() * pi / 3 + pi / 3; // Downward angles
    
    _asteroids.add(Asteroid(
      position: Offset(x, -size),
      velocity: Offset(sin(angle) * speed, cos(angle) * speed),
      size: size,
      rotationSpeed: (_random.nextDouble() - 0.5) * 4, // -2 to 2 rad/s
    ));
  }

  void _checkBulletAsteroidCollisions() {
    for (final bullet in _bullets) {
      if (!bullet.isActive) continue;
      
      for (final asteroid in _asteroids) {
        if (asteroid.isDestroyed) continue;
        
        final distance = sqrt(
          pow(bullet.position.dx - asteroid.position.dx, 2) +
          pow(bullet.position.dy - asteroid.position.dy, 2)
        );
        
        if (distance < asteroid.size / 2 + _bulletSize / 2) {
          // Collision detected
          bullet.isActive = false;
          asteroid.isDestroyed = true;
          
          // Add explosion
          _explosions.add(asteroid.position);
          _explosionController.forward().then((_) {
            _explosionController.reset();
            setState(() {
              _explosions.clear();
            });
          });
          
          // Add score based on asteroid size
          final points = (100 - asteroid.size).round();
          final gameNotifier = ref.read(gameStateProvider(AstroZapGame.gameConfig).notifier);
          gameNotifier.addScore(points);
          
          // Trigger feedback
          ref.read(gameFeedbackServiceProvider).triggerFeedback(GameEvent.scoreIncrease);
          
          break;
        }
      }
    }
  }

  void _checkShipAsteroidCollisions() {
    for (final asteroid in _asteroids) {
      if (asteroid.isDestroyed) continue;
      
      final distance = sqrt(
        pow(_shipPosition.dx - asteroid.position.dx, 2) +
        pow(_shipPosition.dy - asteroid.position.dy, 2)
      );
      
      if (distance < _shipSize / 2 + asteroid.size / 2) {
        // Ship hit by asteroid - game over
        final gameNotifier = ref.read(gameStateProvider(AstroZapGame.gameConfig).notifier);
        gameNotifier.endGame(reason: 'Ship destroyed by asteroid');
        
        ref.read(gameFeedbackServiceProvider).triggerFeedback(GameEvent.obstacleHit);
        return;
      }
    }
  }

  void _onPanUpdate(DragUpdateDetails details) {
    final gameState = ref.read(gameStateProvider(AstroZapGame.gameConfig));
    if (!gameState.isPlaying) return;
    
    setState(() {
      _shipPosition = Offset(
        details.localPosition.dx.clamp(_shipSize / 2, _gameWidth - _shipSize / 2),
        details.localPosition.dy.clamp(_shipSize / 2, _gameHeight - _shipSize / 2),
      );
    });
  }

  void _onTap() {
    final gameState = ref.read(gameStateProvider(AstroZapGame.gameConfig));
    if (!gameState.isPlaying) return;
    
    // Shoot bullet
    _bullets.add(Bullet(
      position: Offset(_shipPosition.dx, _shipPosition.dy - _shipSize / 2),
      velocity: const Offset(0, -_bulletSpeed),
    ));
    
    // Trigger feedback
    ref.read(gameFeedbackServiceProvider).triggerFeedback(GameEvent.buttonPress);
    
    // Ship recoil animation
    _shipController.forward().then((_) => _shipController.reset());
  }

  @override
  Widget build(BuildContext context) {
    final gameState = ref.watch(gameStateProvider(AstroZapGame.gameConfig));
    
    return Container(
      width: _gameWidth,
      height: _gameHeight,
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [Color(0xFF000011), Color(0xFF001122)],
        ),
      ),
      child: GestureDetector(
        onPanUpdate: _onPanUpdate,
        onTap: _onTap,
        child: Stack(
          children: [
            // Starfield background
            _buildStarfield(),
            
            // Asteroids
            ..._asteroids.map((asteroid) => _buildAsteroid(asteroid)),
            
            // Bullets
            ..._bullets.map((bullet) => _buildBullet(bullet)),
            
            // Spaceship
            _buildSpaceship(),
            
            // Explosions
            ..._explosions.map((explosion) => _buildExplosion(explosion)),
            
            // Score display
            _buildScoreDisplay(gameState),
            
            // Instructions
            if (!gameState.isPlaying) _buildInstructions(),
          ],
        ),
      ),
    );
  }

  Widget _buildStarfield() {
    return Positioned.fill(
      child: CustomPaint(
        painter: _StarfieldPainter(),
      ),
    );
  }

  Widget _buildAsteroid(Asteroid asteroid) {
    return Positioned(
      left: asteroid.position.dx - asteroid.size / 2,
      top: asteroid.position.dy - asteroid.size / 2,
      child: Transform.rotate(
        angle: asteroid.rotation,
        child: Container(
          width: asteroid.size,
          height: asteroid.size,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            gradient: RadialGradient(
              colors: [Colors.brown[400]!, Colors.grey[800]!],
              stops: const [0.3, 1.0],
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.brown.withOpacity(0.3),
                blurRadius: 4,
                spreadRadius: 1,
              ),
            ],
          ),
          child: CustomPaint(
            painter: _AsteroidPainter(),
          ),
        ),
      ),
    );
  }

  Widget _buildBullet(Bullet bullet) {
    return Positioned(
      left: bullet.position.dx - _bulletSize / 2,
      top: bullet.position.dy - _bulletSize / 2,
      child: Container(
        width: _bulletSize,
        height: _bulletSize,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          gradient: RadialGradient(
            colors: [Colors.cyan[300]!, Colors.blue[600]!],
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.cyan.withOpacity(0.8),
              blurRadius: 6,
              spreadRadius: 2,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSpaceship() {
    return Positioned(
      left: _shipPosition.dx - _shipSize / 2,
      top: _shipPosition.dy - _shipSize / 2,
      child: ScaleTransition(
        scale: Tween<double>(begin: 1.0, end: 0.9).animate(_shipController),
        child: Container(
          width: _shipSize,
          height: _shipSize,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [Colors.grey[300]!, Colors.grey[600]!],
            ),
            borderRadius: BorderRadius.circular(8),
            boxShadow: [
              BoxShadow(
                color: Colors.blue.withOpacity(0.5),
                blurRadius: 8,
                spreadRadius: 2,
              ),
            ],
          ),
          child: const Center(
            child: Text(
              '🚀',
              style: TextStyle(fontSize: 24),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildExplosion(Offset position) {
    return Positioned(
      left: position.dx - 40,
      top: position.dy - 40,
      child: ScaleTransition(
        scale: _explosionController,
        child: Container(
          width: 80,
          height: 80,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            gradient: RadialGradient(
              colors: [Colors.yellow, Colors.orange, Colors.red, Colors.transparent],
              stops: const [0.0, 0.3, 0.6, 1.0],
            ),
          ),
          child: const Center(
            child: Text(
              '💥',
              style: TextStyle(fontSize: 40),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildScoreDisplay(GameState gameState) {
    return Positioned(
      top: 30,
      left: 20,
      child: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: Colors.black54,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Score',
              style: TextStyle(color: Colors.white, fontSize: 12),
            ),
            Text(
              '${gameState.score}',
              style: const TextStyle(
                color: Colors.cyan,
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInstructions() {
    return Positioned(
      bottom: 100,
      left: 0,
      right: 0,
      child: Center(
        child: Container(
          padding: EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.black54,
            borderRadius: BorderRadius.all(Radius.circular(12)),
          ),
          child: Text(
            'Drag to move your spaceship!\nTap to shoot lasers at asteroids.\nSmaller asteroids = more points!',
            textAlign: TextAlign.center,
            style: TextStyle(
              color: Colors.white,
              fontSize: 16,
            ),
          ),
        ),
      ),
    );
  }
}

class _StarfieldPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.white
      ..style = PaintingStyle.fill;
    
    final random = Random(42); // Fixed seed for consistent stars
    
    for (int i = 0; i < 100; i++) {
      final x = random.nextDouble() * size.width;
      final y = random.nextDouble() * size.height;
      final starSize = random.nextDouble() * 2 + 1;
      
      canvas.drawCircle(Offset(x, y), starSize, paint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class _AsteroidPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.black54
      ..style = PaintingStyle.fill;
    
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width / 2;
    
    // Draw crater-like spots
    canvas.drawCircle(Offset(center.dx - radius * 0.3, center.dy - radius * 0.2), radius * 0.15, paint);
    canvas.drawCircle(Offset(center.dx + radius * 0.2, center.dy + radius * 0.3), radius * 0.1, paint);
    canvas.drawCircle(Offset(center.dx + radius * 0.1, center.dy - radius * 0.4), radius * 0.08, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
