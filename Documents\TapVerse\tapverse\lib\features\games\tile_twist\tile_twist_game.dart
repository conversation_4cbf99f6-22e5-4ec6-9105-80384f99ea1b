import 'dart:math';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../../../core/models/game_state.dart';
import '../../../core/providers/game_providers.dart';
import '../../../core/services/game_feedback_service.dart';
import '../../../shared/widgets/base_game_widget.dart';

class TileTwistGame extends BaseGameWidget {
  static const GameConfig gameConfig = GameConfig(
    gameId: 'tile_twist',
    gameName: 'TileTwist',
    scoreMultiplier: 1,
    hasLives: false,
    hasLevels: false,
    hasTimer: false,
  );

  const TileTwistGame({
    super.key,
    super.onGameComplete,
    super.onGameExit,
  }) : super(config: gameConfig);

  @override
  Widget buildGameContent(BuildContext context, WidgetRef ref, GameState gameState) {
    return const _TileTwistGameContent();
  }
}

enum Direction { up, down, left, right }

class _TileTwistGameContent extends ConsumerStatefulWidget {
  const _TileTwistGameContent();

  @override
  ConsumerState<_TileTwistGameContent> createState() => _TileTwistGameContentState();
}

class _TileTwistGameContentState extends ConsumerState<_TileTwistGameContent>
    with TickerProviderStateMixin {
  
  // Game state
  late AnimationController _mergeController;
  late AnimationController _newTileController;
  
  // Grid
  List<List<int>> _grid = [];
  final int _gridSize = 4;
  final Random _random = Random();
  
  // Game mechanics
  bool _canMove = true;
  int _highestTile = 2;
  final List<Offset> _mergedPositions = [];
  final List<Offset> _newTilePositions = [];
  
  // Constants
  static const double _gameWidth = 320; // 4 * 80
  static const double _gameHeight = 320; // 4 * 80
  static const double _tileSize = 75;
  static const double _tileSpacing = 5;

  @override
  void initState() {
    super.initState();
    
    _mergeController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _newTileController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    
    _initializeGrid();
    _addRandomTile();
    _addRandomTile();
  }

  @override
  void dispose() {
    _mergeController.dispose();
    _newTileController.dispose();
    super.dispose();
  }

  void _initializeGrid() {
    _grid = List.generate(_gridSize, (i) => List.generate(_gridSize, (j) => 0));
  }

  void _addRandomTile() {
    final emptyCells = <Offset>[];
    for (int i = 0; i < _gridSize; i++) {
      for (int j = 0; j < _gridSize; j++) {
        if (_grid[i][j] == 0) {
          emptyCells.add(Offset(i.toDouble(), j.toDouble()));
        }
      }
    }
    
    if (emptyCells.isNotEmpty) {
      final randomCell = emptyCells[_random.nextInt(emptyCells.length)];
      final value = _random.nextDouble() < 0.9 ? 2 : 4;
      _grid[randomCell.dx.toInt()][randomCell.dy.toInt()] = value;
      
      setState(() {
        _newTilePositions.add(randomCell);
      });
      
      _newTileController.forward().then((_) {
        _newTileController.reset();
        setState(() {
          _newTilePositions.clear();
        });
      });
    }
  }

  void _onPanEnd(DragEndDetails details) {
    final gameState = ref.read(gameStateProvider(TileTwistGame.gameConfig));
    if (!gameState.isPlaying || !_canMove) return;
    
    final velocity = details.velocity.pixelsPerSecond;
    final dx = velocity.dx.abs();
    final dy = velocity.dy.abs();
    
    Direction? direction;
    
    if (dx > dy && dx > 200) {
      direction = velocity.dx > 0 ? Direction.right : Direction.left;
    } else if (dy > dx && dy > 200) {
      direction = velocity.dy > 0 ? Direction.down : Direction.up;
    }
    
    if (direction != null) {
      _makeMove(direction);
    }
  }

  void _makeMove(Direction direction) {
    final oldGrid = _grid.map((row) => List<int>.from(row)).toList();
    bool moved = false;
    _mergedPositions.clear();
    
    switch (direction) {
      case Direction.left:
        moved = _moveLeft();
        break;
      case Direction.right:
        moved = _moveRight();
        break;
      case Direction.up:
        moved = _moveUp();
        break;
      case Direction.down:
        moved = _moveDown();
        break;
    }
    
    if (moved) {
      setState(() {
        _canMove = false;
      });
      
      // Trigger feedback
      ref.read(gameFeedbackServiceProvider).triggerFeedback(GameEvent.scoreIncrease);
      
      // Play merge animation if there were merges
      if (_mergedPositions.isNotEmpty) {
        _mergeController.forward().then((_) {
          _mergeController.reset();
          setState(() {
            _mergedPositions.clear();
          });
        });
      }
      
      // Add new tile after a short delay
      Future.delayed(const Duration(milliseconds: 150), () {
        _addRandomTile();
        setState(() {
          _canMove = true;
        });
        
        // Check game state
        _checkGameState();
      });
    }
  }

  bool _moveLeft() {
    bool moved = false;
    for (int i = 0; i < _gridSize; i++) {
      final row = _grid[i];
      final newRow = _mergeRow(row);
      if (!_listsEqual(row, newRow)) {
        _grid[i] = newRow;
        moved = true;
      }
    }
    return moved;
  }

  bool _moveRight() {
    bool moved = false;
    for (int i = 0; i < _gridSize; i++) {
      final row = _grid[i].reversed.toList();
      final newRow = _mergeRow(row).reversed.toList();
      if (!_listsEqual(_grid[i], newRow)) {
        _grid[i] = newRow;
        moved = true;
      }
    }
    return moved;
  }

  bool _moveUp() {
    bool moved = false;
    for (int j = 0; j < _gridSize; j++) {
      final column = List.generate(_gridSize, (i) => _grid[i][j]);
      final newColumn = _mergeRow(column);
      if (!_listsEqual(column, newColumn)) {
        for (int i = 0; i < _gridSize; i++) {
          _grid[i][j] = newColumn[i];
        }
        moved = true;
      }
    }
    return moved;
  }

  bool _moveDown() {
    bool moved = false;
    for (int j = 0; j < _gridSize; j++) {
      final column = List.generate(_gridSize, (i) => _grid[i][j]).reversed.toList();
      final newColumn = _mergeRow(column).reversed.toList();
      final originalColumn = List.generate(_gridSize, (i) => _grid[i][j]);
      if (!_listsEqual(originalColumn, newColumn)) {
        for (int i = 0; i < _gridSize; i++) {
          _grid[i][j] = newColumn[i];
        }
        moved = true;
      }
    }
    return moved;
  }

  List<int> _mergeRow(List<int> row) {
    // Remove zeros
    final nonZero = row.where((x) => x != 0).toList();
    final result = <int>[];
    
    int i = 0;
    while (i < nonZero.length) {
      if (i + 1 < nonZero.length && nonZero[i] == nonZero[i + 1]) {
        // Merge tiles
        final mergedValue = nonZero[i] * 2;
        result.add(mergedValue);
        
        // Update highest tile and score
        if (mergedValue > _highestTile) {
          _highestTile = mergedValue;
        }
        
        final gameNotifier = ref.read(gameStateProvider(TileTwistGame.gameConfig).notifier);
        gameNotifier.addScore(mergedValue);
        
        i += 2; // Skip next tile as it was merged
      } else {
        result.add(nonZero[i]);
        i++;
      }
    }
    
    // Fill with zeros
    while (result.length < _gridSize) {
      result.add(0);
    }
    
    return result;
  }

  bool _listsEqual(List<int> a, List<int> b) {
    if (a.length != b.length) return false;
    for (int i = 0; i < a.length; i++) {
      if (a[i] != b[i]) return false;
    }
    return true;
  }

  void _checkGameState() {
    // Check for 2048 (win condition)
    if (_highestTile >= 2048) {
      final gameNotifier = ref.read(gameStateProvider(TileTwistGame.gameConfig).notifier);
      gameNotifier.endGame(reason: 'Reached 2048 - You Win!');
      return;
    }
    
    // Check for game over
    if (!_canMakeMove()) {
      final gameNotifier = ref.read(gameStateProvider(TileTwistGame.gameConfig).notifier);
      gameNotifier.endGame(reason: 'No more moves available');
    }
  }

  bool _canMakeMove() {
    // Check for empty cells
    for (int i = 0; i < _gridSize; i++) {
      for (int j = 0; j < _gridSize; j++) {
        if (_grid[i][j] == 0) return true;
      }
    }
    
    // Check for possible merges
    for (int i = 0; i < _gridSize; i++) {
      for (int j = 0; j < _gridSize; j++) {
        final current = _grid[i][j];
        
        // Check right neighbor
        if (j + 1 < _gridSize && _grid[i][j + 1] == current) return true;
        
        // Check bottom neighbor
        if (i + 1 < _gridSize && _grid[i + 1][j] == current) return true;
      }
    }
    
    return false;
  }

  @override
  Widget build(BuildContext context) {
    final gameState = ref.watch(gameStateProvider(TileTwistGame.gameConfig));
    
    return Container(
      width: _gameWidth + 100,
      height: _gameHeight + 200,
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [Color(0xFFfaf8ef), Color(0xFFf2e6d3)],
        ),
      ),
      child: Column(
        children: [
          // Game info
          _buildGameInfo(gameState),
          
          // Grid
          Expanded(
            child: Center(
              child: GestureDetector(
                onPanEnd: _onPanEnd,
                child: Container(
                  width: _gameWidth,
                  height: _gameHeight,
                  decoration: BoxDecoration(
                    color: const Color(0xFFbbada0),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Stack(
                    children: [
                      // Grid background
                      _buildGridBackground(),
                      
                      // Tiles
                      ..._buildTiles(),
                      
                      // Merge effects
                      ..._buildMergeEffects(),
                      
                      // New tile effects
                      ..._buildNewTileEffects(),
                    ],
                  ),
                ),
              ),
            ),
          ),
          
          // Instructions
          if (!gameState.isPlaying) _buildInstructions(),
        ],
      ),
    );
  }

  Widget _buildGameInfo(GameState gameState) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: const Color(0xFFbbada0),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              children: [
                const Text(
                  'SCORE',
                  style: TextStyle(
                    color: Color(0xFFf9f6f2),
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  '${gameState.score}',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: const Color(0xFFbbada0),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              children: [
                const Text(
                  'BEST',
                  style: TextStyle(
                    color: Color(0xFFf9f6f2),
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  '$_highestTile',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGridBackground() {
    return Positioned.fill(
      child: Padding(
        padding: const EdgeInsets.all(5),
        child: GridView.builder(
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 4,
            crossAxisSpacing: 5,
            mainAxisSpacing: 5,
          ),
          itemCount: 16,
          itemBuilder: (context, index) {
            return Container(
              decoration: BoxDecoration(
                color: const Color(0xFFcdc1b4),
                borderRadius: BorderRadius.circular(4),
              ),
            );
          },
        ),
      ),
    );
  }

  List<Widget> _buildTiles() {
    final tiles = <Widget>[];
    for (int i = 0; i < _gridSize; i++) {
      for (int j = 0; j < _gridSize; j++) {
        if (_grid[i][j] != 0) {
          tiles.add(_buildTile(i, j, _grid[i][j]));
        }
      }
    }
    return tiles;
  }

  Widget _buildTile(int row, int col, int value) {
    final left = col * (_tileSize + _tileSpacing) + _tileSpacing;
    final top = row * (_tileSize + _tileSpacing) + _tileSpacing;
    
    return Positioned(
      left: left,
      top: top,
      child: Container(
        width: _tileSize,
        height: _tileSize,
        decoration: BoxDecoration(
          color: _getTileColor(value),
          borderRadius: BorderRadius.circular(4),
        ),
        child: Center(
          child: Text(
            '$value',
            style: TextStyle(
              color: value <= 4 ? const Color(0xFF776e65) : Colors.white,
              fontSize: value >= 1000 ? 24 : value >= 100 ? 28 : 32,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ),
    );
  }

  Color _getTileColor(int value) {
    switch (value) {
      case 2: return const Color(0xFFeee4da);
      case 4: return const Color(0xFFede0c8);
      case 8: return const Color(0xFFf2b179);
      case 16: return const Color(0xFFf59563);
      case 32: return const Color(0xFFf67c5f);
      case 64: return const Color(0xFFf65e3b);
      case 128: return const Color(0xFFedcf72);
      case 256: return const Color(0xFFedcc61);
      case 512: return const Color(0xFFedc850);
      case 1024: return const Color(0xFFedc53f);
      case 2048: return const Color(0xFFedc22e);
      default: return const Color(0xFF3c3a32);
    }
  }

  List<Widget> _buildMergeEffects() {
    return _mergedPositions.map((pos) {
      final left = pos.dy * (_tileSize + _tileSpacing) + _tileSpacing;
      final top = pos.dx * (_tileSize + _tileSpacing) + _tileSpacing;
      
      return Positioned(
        left: left,
        top: top,
        child: ScaleTransition(
          scale: _mergeController,
          child: Container(
            width: _tileSize,
            height: _tileSize,
            decoration: BoxDecoration(
              color: Colors.yellow.withOpacity(0.5),
              borderRadius: BorderRadius.circular(4),
            ),
          ),
        ),
      );
    }).toList();
  }

  List<Widget> _buildNewTileEffects() {
    return _newTilePositions.map((pos) {
      final left = pos.dy * (_tileSize + _tileSpacing) + _tileSpacing;
      final top = pos.dx * (_tileSize + _tileSpacing) + _tileSpacing;
      
      return Positioned(
        left: left,
        top: top,
        child: ScaleTransition(
          scale: _newTileController,
          child: Container(
            width: _tileSize,
            height: _tileSize,
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.3),
              borderRadius: BorderRadius.circular(4),
            ),
          ),
        ),
      );
    }).toList();
  }

  Widget _buildInstructions() {
    return const Padding(
      padding: EdgeInsets.all(16),
      child: Text(
        'Swipe to move tiles!\nCombine tiles with the same number.\nReach 2048 to win!',
        textAlign: TextAlign.center,
        style: TextStyle(
          color: Color(0xFF776e65),
          fontSize: 16,
        ),
      ),
    );
  }
}
