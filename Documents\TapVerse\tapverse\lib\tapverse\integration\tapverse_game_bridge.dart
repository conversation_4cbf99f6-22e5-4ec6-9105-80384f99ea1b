import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flame/game.dart';
import '../core/mini_game_base.dart';
import '../registry/game_registry.dart' as registry;
import '../../core/models/game_state.dart';
import '../../core/providers/game_providers.dart';
import '../../shared/widgets/base_game_widget.dart';

/// Bridge widget that wraps MiniGameBase games to work with the existing TapVerse UI system
class TapVerseGameBridge extends BaseGameWidget {
  final String gameId;
  final int? customSeed;

  const TapVerseGameBridge({
    super.key,
    required this.gameId,
    required super.config,
    this.customSeed,
    super.onGameComplete,
    super.onGameExit,
  });

  @override
  Widget buildGameContent(BuildContext context, WidgetRef ref, GameState gameState) {
    return _TapVerseGameBridgeContent(
      gameId: gameId,
      customSeed: customSeed,
      config: config,
    );
  }
}

class _TapVerseGameBridgeContent extends ConsumerStatefulWidget {
  final String gameId;
  final int? customSeed;
  final GameConfig config;

  const _TapVerseGameBridgeContent({
    required this.gameId,
    this.customSeed,
    required this.config,
  });

  @override
  ConsumerState<_TapVerseGameBridgeContent> createState() => _TapVerseGameBridgeContentState();
}

class _TapVerseGameBridgeContentState extends ConsumerState<_TapVerseGameBridgeContent> {
  late MiniGameBase game;
  bool _gameInitialized = false;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _initializeGame();
  }

  void _initializeGame() {
    try {
      final seed = widget.customSeed ?? DateTime.now().millisecondsSinceEpoch;
      game = registry.GameRegistry.create(widget.gameId, seed: seed);

      // Set up integration hooks
      _setupIntegrationHooks();

      setState(() {
        _gameInitialized = true;
      });
    } catch (e) {
      debugPrint('Failed to initialize game ${widget.gameId}: $e');
      setState(() {
        _errorMessage = e.toString().contains('not yet implemented')
            ? 'This game is coming soon! Stay tuned for updates.'
            : 'Failed to load game. Please try again later.';
      });
    }
  }

  void _setupIntegrationHooks() {
    // Note: Integration hooks need to be set up differently since methods can't be assigned
    // This is a placeholder for future integration work
    // TODO: Implement proper integration hooks using composition or inheritance

    // For now, we'll rely on the existing BaseGameWidget integration
  }

  @override
  Widget build(BuildContext context) {
    if (_errorMessage != null) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.error_outline,
                size: 64,
                color: Colors.orange,
              ),
              const SizedBox(height: 16),
              Text(
                _errorMessage!,
                textAlign: TextAlign.center,
                style: Theme.of(context).textTheme.titleMedium,
              ),
              const SizedBox(height: 24),
              ElevatedButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Go Back'),
              ),
            ],
          ),
        ),
      );
    }

    if (!_gameInitialized) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    return GameWidget(game: game);
  }

  @override
  void dispose() {
    // Clean up the game
    if (_gameInitialized) {
      game.onRemove();
    }
    super.dispose();
  }
}

/// Factory for creating TapVerse game bridges with proper configurations
class TapVerseGameFactory {
  /// Create a game widget for the new MiniGameBase architecture
  static Widget createMiniGame({
    required String gameId,
    int? customSeed,
    VoidCallback? onGameComplete,
    VoidCallback? onGameExit,
  }) {
    // Get game metadata
    final metadata = registry.GameRegistry.getMetadata(gameId);
    
    // Create appropriate game config
    final config = GameConfig(
      gameId: gameId,
      gameName: metadata?.name ?? gameId,
      difficulty: _convertDifficulty(metadata?.difficulty ?? registry.GameDifficulty.medium),
      scoreMultiplier: _getScoreMultiplier(metadata?.category ?? registry.GameCategory.casual),
      hasLives: false, // Most mini-games don't use lives
      hasLevels: false, // Most mini-games don't use levels
      hasTimer: false, // Timer is handled internally by mini-games
    );

    return TapVerseGameBridge(
      gameId: gameId,
      config: config,
      customSeed: customSeed,
      onGameComplete: onGameComplete,
      onGameExit: onGameExit,
    );
  }

  /// Create a legacy game widget for the existing BaseGameWidget architecture
  static Widget createLegacyGame({
    required String gameId,
    VoidCallback? onGameComplete,
    VoidCallback? onGameExit,
  }) {
    // This would create widgets for games that haven't been migrated yet
    // For now, throw an error to indicate the game needs to be migrated
    throw UnimplementedError('Legacy game $gameId needs to be migrated to MiniGameBase');
  }

  /// Determine if a game uses the new MiniGameBase architecture
  static bool isNewArchitecture(String gameId) {
    try {
      // Try to create the game - if it succeeds, it's using new architecture
      registry.GameRegistry.create(gameId, seed: 0);
      return true;
    } catch (e) {
      return false;
    }
  }

  static GameDifficulty _convertDifficulty(registry.GameDifficulty miniGameDifficulty) {
    // Convert from registry difficulty to game state difficulty
    switch (miniGameDifficulty) {
      case registry.GameDifficulty.easy:
        return GameDifficulty.easy;
      case registry.GameDifficulty.medium:
        return GameDifficulty.medium;
      case registry.GameDifficulty.hard:
        return GameDifficulty.hard;
      case registry.GameDifficulty.expert:
        return GameDifficulty.hard; // Map expert to hard for now
    }
  }

  static int _getScoreMultiplier(registry.GameCategory category) {
    // Different categories have different score multipliers
    switch (category) {
      case registry.GameCategory.arcade:
        return 2;
      case registry.GameCategory.sports:
        return 2;
      case registry.GameCategory.shooter:
        return 3;
      case registry.GameCategory.platform:
        return 2;
      case registry.GameCategory.survival:
        return 3;
      case registry.GameCategory.puzzle:
        return 1;
      case registry.GameCategory.rhythm:
        return 2;
      case registry.GameCategory.strategy:
        return 1;
      case registry.GameCategory.casual:
        return 1;
      case registry.GameCategory.stealth:
        return 2;
    }
  }
}

// TODO: Implement GameSession when needed
// /// Provider for game session management
// final gameSessionProvider = StateProvider.family<GameSession?, String>((ref, gameId) {
//   return null;
// });

// /// Provider for creating game sessions
// final gameSessionNotifierProvider = StateNotifierProvider.family<GameSessionNotifier, GameSession?, String>(
//   (ref, gameId) => GameSessionNotifier(gameId),
// );

// class GameSessionNotifier extends StateNotifier<GameSession?> {
//   final String gameId;

//   GameSessionNotifier(this.gameId) : super(null);
// }

/// Extension to add mini-game support to existing game types
extension GameTypeExtension on String {
  /// Check if this game ID is supported by the new architecture
  bool get isNewArchitecture => TapVerseGameFactory.isNewArchitecture(this);
  
  /// Get game metadata if available
  registry.GameMetadata? get metadata => registry.GameRegistry.getMetadata(this);
  
  /// Create a game widget using the appropriate architecture
  Widget createGameWidget({
    VoidCallback? onGameComplete,
    VoidCallback? onGameExit,
    int? customSeed,
  }) {
    if (isNewArchitecture) {
      return TapVerseGameFactory.createMiniGame(
        gameId: this,
        customSeed: customSeed,
        onGameComplete: onGameComplete,
        onGameExit: onGameExit,
      );
    } else {
      return TapVerseGameFactory.createLegacyGame(
        gameId: this,
        onGameComplete: onGameComplete,
        onGameExit: onGameExit,
      );
    }
  }
}
