import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/user_model.dart';
import '../models/leaderboard_entry.dart';
import '../models/store_item.dart';

class FirestoreService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // Getter for accessing Firestore instance
  FirebaseFirestore get db => _firestore;

  // User operations
  Future<UserModel?> getUser(String uid) async {
    try {
      final doc = await _firestore.collection('users').doc(uid).get();
      if (doc.exists) {
        return UserModel.fromFirestore(doc);
      }
      return null;
    } catch (e) {
      print('Error getting user: $e');
      return null;
    }
  }

  Stream<UserModel?> getUserStream(String uid) {
    return _firestore
        .collection('users')
        .doc(uid)
        .snapshots()
        .map((doc) => doc.exists ? UserModel.fromFirestore(doc) : null);
  }

  Future<void> updateUserTokens(String uid, int tokens) async {
    try {
      print('🔥 FirestoreService Debug - Updating tokens:');
      print('  - UID: $uid');
      print('  - New token amount: $tokens');
      print('  - Document path: users/$uid');

      await _firestore.collection('users').doc(uid).update({
        'tokens': tokens,
      });

      print('  - SUCCESS: Token update completed');
    } catch (e) {
      print('  - ERROR in updateUserTokens: $e');
      print('  - Error type: ${e.runtimeType}');
      rethrow;
    }
  }

  Future<void> updateUserHighScore(String uid, String gameId, int score) async {
    try {
      final userRef = _firestore.collection('users').doc(uid);

      await _firestore.runTransaction((transaction) async {
        final userDoc = await transaction.get(userRef);

        if (userDoc.exists) {
          final userData = userDoc.data() ?? {};
          final highScores = userData['highScores'] as Map<String, dynamic>? ?? {};
          final currentHighScore = highScores[gameId] ?? 0;

          // Only update if the new score is higher
          if (score > currentHighScore) {
            transaction.update(userRef, {
              'highScores.$gameId': score,
            });
          }
        } else {
          // First time setting high score for this user
          transaction.set(userRef, {
            'highScores': {gameId: score},
          }, SetOptions(merge: true));
        }
      });
    } catch (e) {
      print('Error updating user high score: $e');
    }
  }

  // Leaderboard operations
  Future<void> submitScore(String gameId, String uid, String displayName, int score) async {
    try {
      final scoreRef = _firestore
          .collection('leaderboards')
          .doc(gameId)
          .collection('scores')
          .doc(uid);

      await _firestore.runTransaction((transaction) async {
        final scoreDoc = await transaction.get(scoreRef);

        if (scoreDoc.exists) {
          final existingScore = scoreDoc.data()?['score'] ?? 0;
          // Only update if the new score is higher
          if (score > existingScore) {
            transaction.update(scoreRef, {
              'displayName': displayName,
              'score': score,
              'timestamp': FieldValue.serverTimestamp(),
            });
          }
        } else {
          // First time submitting score for this user
          transaction.set(scoreRef, {
            'uid': uid,
            'displayName': displayName,
            'score': score,
            'timestamp': FieldValue.serverTimestamp(),
          });
        }
      });
    } catch (e) {
      print('Error submitting score: $e');
    }
  }

  Stream<List<LeaderboardEntry>> getLeaderboard(String gameId, {int limit = 10}) {
    return _firestore
        .collection('leaderboards')
        .doc(gameId)
        .collection('scores')
        .orderBy('score', descending: true)
        .limit(limit)
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => LeaderboardEntry.fromFirestore(doc))
            .toList());
  }

  Future<LeaderboardEntry?> getUserScore(String gameId, String uid) async {
    try {
      final doc = await _firestore
          .collection('leaderboards')
          .doc(gameId)
          .collection('scores')
          .doc(uid)
          .get();
      
      if (doc.exists) {
        return LeaderboardEntry.fromFirestore(doc);
      }
      return null;
    } catch (e) {
      print('Error getting user score: $e');
      return null;
    }
  }

  // Store operations
  Stream<List<StoreItem>> getStoreItems() {
    return _firestore
        .collection('store')
        .where('isAvailable', isEqualTo: true)
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => StoreItem.fromFirestore(doc))
            .toList());
  }

  Future<StoreItem?> getStoreItem(String itemId) async {
    try {
      final doc = await _firestore
          .collection('store')
          .doc(itemId)
          .get();

      if (doc.exists) {
        return StoreItem.fromFirestore(doc);
      }
      return null;
    } catch (e) {
      print('Error getting store item: $e');
      return null;
    }
  }

  // Inventory operations
  Future<void> addToInventory(String uid, String itemId) async {
    try {
      await _firestore
          .collection('users')
          .doc(uid)
          .collection('inventory')
          .doc(itemId)
          .set({
        'ownedAt': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      print('Error adding to inventory: $e');
    }
  }

  Stream<List<String>> getUserInventory(String uid) {
    return _firestore
        .collection('users')
        .doc(uid)
        .collection('inventory')
        .snapshots()
        .map((snapshot) => snapshot.docs.map((doc) => doc.id).toList());
  }

  Future<bool> hasItem(String uid, String itemId) async {
    try {
      final doc = await _firestore
          .collection('users')
          .doc(uid)
          .collection('inventory')
          .doc(itemId)
          .get();
      
      return doc.exists;
    } catch (e) {
      print('Error checking if user has item: $e');
      return false;
    }
  }

  // Batch operations
  Future<void> purchaseItem(String uid, String itemId, int cost) async {
    try {
      final batch = _firestore.batch();
      
      // Deduct tokens
      final userRef = _firestore.collection('users').doc(uid);
      batch.update(userRef, {
        'tokens': FieldValue.increment(-cost),
      });
      
      // Add to inventory
      final inventoryRef = userRef.collection('inventory').doc(itemId);
      batch.set(inventoryRef, {
        'ownedAt': FieldValue.serverTimestamp(),
      });
      
      await batch.commit();
    } catch (e) {
      print('Error purchasing item: $e');
    }
  }
}
