import 'dart:math' as math;
import 'package:flame/components.dart';
import 'package:flame/collisions.dart';
import 'package:flutter/material.dart';
import '../../core/mini_game_base.dart';
import '../../core/hud.dart';
import '../../core/effects.dart';
import '../../core/analytics.dart';
import '../../core/difficulty.dart';

/// PixelDive - Endless falling game with obstacle avoidance
/// Controls: Tilt device or swipe left/right to steer falling character
/// Scoring: +1 per 50px fallen; +5 per power-up; +10 per near-miss
class PixelDiveGame extends MiniGameBase {
  PixelDiveGame(int seed) : super(modeId: 'pixel_dive', sessionSeed: seed) {
    rng = math.Random(seed);
  }

  late math.Random rng;
  late ScoreText scoreText;
  late TextComponent depthText;
  
  // Game state
  int score = 0;
  double depth = 0;
  double lastScoredDepth = 0;
  
  // Game entities
  late Diver diver;
  late List<Obstacle> obstacles;
  late List<PowerUp> powerUps;
  
  // Difficulty curves (from planning document)
  final fallSpeedCurve = const CurveParam(start: 200, max: 500, perMinute: 150);
  final obstacleRateCurve = const CurveParam(start: 0.3, max: 0.8, perMinute: 0.25);
  
  // Difficulty manager
  late DifficultyManager difficultyManager;
  
  // Level generation
  double nextObstacleY = -200;
  double nextPowerUpY = -400;

  @override
  Future<void> loadAssets() async {
    // Load any required assets here
  }

  @override
  void setupWorld() {
    camera.viewfinder.visibleGameSize = Vector2(720, 1280);
    
    // Initialize difficulty manager
    difficultyManager = DifficultyManager();
    difficultyManager.addParameter('fall_speed', fallSpeedCurve);
    difficultyManager.addParameter('obstacle_rate', obstacleRateCurve);
    
    // Initialize collections
    obstacles = [];
    powerUps = [];
    
    // Create game entities
    _createGameEntities();
  }

  void _createGameEntities() {
    // Create diver
    diver = Diver(
      position: Vector2(360, 640),
      onObstacleHit: _onObstacleHit,
      onPowerUpCollected: _onPowerUpCollected,
      onNearMiss: _onNearMiss,
      onPowerUpRemove: (powerUp) => powerUps.remove(powerUp),
    );
    add(diver);

    // Generate initial obstacles and power-ups
    _generateInitialLevel();
  }

  void _generateInitialLevel() {
    // Generate starting obstacles
    for (int i = 0; i < 10; i++) {
      _generateObstacle();
    }
    
    // Generate starting power-ups
    for (int i = 0; i < 5; i++) {
      _generatePowerUp();
    }
  }

  void _generateObstacle() {
    final obstacleRate = difficultyManager.getValue('obstacle_rate');
    
    if (rng.nextDouble() > obstacleRate) {
      nextObstacleY -= 150;
      return;
    }
    
    // Random obstacle type and position
    final obstacleType = rng.nextInt(3); // 0: spike, 1: moving platform, 2: narrow gap
    
    switch (obstacleType) {
      case 0:
        _generateSpike();
        break;
      case 1:
        _generateMovingPlatform();
        break;
      case 2:
        _generateNarrowGap();
        break;
    }
    
    nextObstacleY -= 150 + rng.nextDouble() * 100;
  }

  void _generateSpike() {
    final x = 50 + rng.nextDouble() * 620;
    final obstacle = Obstacle(
      position: Vector2(x, nextObstacleY),
      type: ObstacleType.spike,
      width: 40,
      height: 40,
    );
    
    obstacles.add(obstacle);
    add(obstacle);
  }

  void _generateMovingPlatform() {
    final obstacle = Obstacle(
      position: Vector2(100, nextObstacleY),
      type: ObstacleType.movingPlatform,
      width: 120,
      height: 20,
      moveSpeed: 100 + rng.nextDouble() * 100,
    );
    
    obstacles.add(obstacle);
    add(obstacle);
  }

  void _generateNarrowGap() {
    final gapCenter = 200 + rng.nextDouble() * 320;
    final gapWidth = 80 + rng.nextDouble() * 40;
    
    // Left wall
    final leftWall = Obstacle(
      position: Vector2(0, nextObstacleY),
      type: ObstacleType.wall,
      width: gapCenter - gapWidth / 2,
      height: 20,
    );
    
    // Right wall
    final rightWall = Obstacle(
      position: Vector2(gapCenter + gapWidth / 2, nextObstacleY),
      type: ObstacleType.wall,
      width: 720 - (gapCenter + gapWidth / 2),
      height: 20,
    );
    
    obstacles.addAll([leftWall, rightWall]);
    add(leftWall);
    add(rightWall);
  }

  void _generatePowerUp() {
    if (rng.nextDouble() < 0.3) {
      final x = 100 + rng.nextDouble() * 520;
      final powerUp = PowerUp(
        position: Vector2(x, nextPowerUpY),
        type: rng.nextBool() ? PowerUpType.shield : PowerUpType.speedBoost,
      );
      
      powerUps.add(powerUp);
      add(powerUp);
    }
    
    nextPowerUpY -= 300 + rng.nextDouble() * 200;
  }

  @override
  void setupHUD() {
    // Score display
    scoreText = ScoreText(
      position: Vector2(16, 16),
      textStyle: const TextStyle(
        color: Colors.white,
        fontSize: 24,
        fontWeight: FontWeight.bold,
      ),
    );
    add(scoreText);

    // Depth display
    depthText = TextComponent(
      text: 'Depth: 0m',
      position: Vector2(16, 50),
      textRenderer: TextPaint(
        style: const TextStyle(
          color: Colors.cyan,
          fontSize: 20,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
    add(depthText);
  }

  @override
  void onStart() {
    score = 0;
    depth = 0;
    lastScoredDepth = 0;
    
    // Start difficulty progression
    difficultyManager.start();
    
    // Start the game
    startGame();
    
    Analytics.log('pixel_dive_start', {'session_seed': sessionSeed});
  }

  @override
  void onGameUpdate(double dt) {
    // Update fall speed based on difficulty
    final fallSpeed = difficultyManager.getValue('fall_speed');
    diver.updateFallSpeed(fallSpeed);
    
    // Update depth
    depth += fallSpeed * dt;
    depthText.text = 'Depth: ${(depth / 50).floor()}m';
    
    // Score for depth progress
    if (depth - lastScoredDepth >= 50) {
      lastScoredDepth = depth;
      score += 1;
      addScore(1);
      scoreText.updateScore(score);
    }
    
    // Generate new obstacles and power-ups
    if (diver.position.y < nextObstacleY + 1000) {
      _generateObstacle();
    }
    
    if (diver.position.y < nextPowerUpY + 1000) {
      _generatePowerUp();
    }
    
    // Clean up old entities
    _cleanupOldEntities();
    
    // Update camera to follow diver
    camera.viewfinder.position = Vector2(360, diver.position.y + 400);
  }

  void _cleanupOldEntities() {
    // Remove obstacles far above diver
    obstacles.removeWhere((obstacle) {
      if (obstacle.position.y > diver.position.y + 800) {
        obstacle.removeFromParent();
        return true;
      }
      return false;
    });
    
    // Remove power-ups far above diver
    powerUps.removeWhere((powerUp) {
      if (powerUp.position.y > diver.position.y + 800) {
        powerUp.removeFromParent();
        return true;
      }
      return false;
    });
  }

  // TODO: Implement input handling
  void handlePanUpdate(Vector2 delta) {
    if (isGameEnded) return;

    // Steer diver based on pan
    diver.steer(delta.x * 5); // Amplify steering
  }

  @override
  bool onSwipeEnd(Swipe swipe) {
    if (isGameEnded) return false;
    
    // Quick steering based on swipe
    if (swipe.isLeft) {
      diver.quickSteer(-200);
    } else if (swipe.isRight) {
      diver.quickSteer(200);
    }
    
    return true;
  }

  void _onObstacleHit() {
    // Diver hit obstacle - game over
    endGame(success: false, score: score);
    
    Analytics.log('obstacle_hit', {
      'final_score': score,
      'depth': depth,
    });
  }

  void _onPowerUpCollected(PowerUpType type) {
    score += 5;
    addScore(5);
    scoreText.updateScore(score);
    
    // Visual effects
    add(Effects.sparkle(diver.position, colors: [Colors.purple, Colors.pink]));
    add(Effects.textPopup(
      diver.position + Vector2(0, -30),
      '+5',
      color: Colors.purple,
    ));
    
    Analytics.log('powerup_collected', {
      'type': type.toString(),
      'score': score,
      'depth': depth,
    });
  }

  void _onNearMiss() {
    score += 10;
    addScore(10);
    scoreText.updateScore(score);
    
    // Visual effects
    add(Effects.textPopup(
      diver.position + Vector2(0, -30),
      'NEAR MISS +10',
      color: Colors.orange,
    ));
    
    Analytics.log('near_miss', {
      'score': score,
      'depth': depth,
    });
  }


  @override
  void reportScore(int score) {
    // Integration hook for TapVerse UI
  }

  @override
  void awardTokens(int tokens) {
    // Integration hook for TapVerse token system
  }
}

/// Diver component with steering physics
class Diver extends RectangleComponent with HasCollisionDetection, CollisionCallbacks {
  Diver({
    required super.position,
    required this.onObstacleHit,
    required this.onPowerUpCollected,
    required this.onNearMiss,
    required this.onPowerUpRemove,
  }) : super(size: Vector2(30, 30));

  final VoidCallback onObstacleHit;
  final Function(PowerUpType) onPowerUpCollected;
  final VoidCallback onNearMiss;
  final Function(PowerUp) onPowerUpRemove;

  Vector2 velocity = Vector2.zero();
  double fallSpeed = 200;
  bool hasShield = false;
  double shieldTimer = 0;
  double nearMissTimer = 0;
  static const double maxHorizontalSpeed = 300;
  static const double horizontalDrag = 0.9;

  @override
  Future<void> onLoad() async {
    add(RectangleHitbox());
    paint = Paint()..color = Colors.green;
  }

  @override
  void update(double dt) {
    super.update(dt);
    
    // Apply fall speed
    velocity.y = fallSpeed;
    
    // Apply horizontal drag
    velocity.x *= horizontalDrag;
    
    // Update position
    position += velocity * dt;
    
    // Keep within screen bounds
    position.x = position.x.clamp(15, 705);
    
    // Update shield
    if (hasShield) {
      shieldTimer -= dt;
      if (shieldTimer <= 0) {
        hasShield = false;
        paint = Paint()..color = Colors.green;
      }
    }
    
    // Update near miss timer
    if (nearMissTimer > 0) {
      nearMissTimer -= dt;
    }
  }

  void steer(double force) {
    velocity.x += force;
    velocity.x = velocity.x.clamp(-maxHorizontalSpeed, maxHorizontalSpeed);
  }

  void quickSteer(double impulse) {
    velocity.x = impulse;
  }

  void updateFallSpeed(double newFallSpeed) {
    fallSpeed = newFallSpeed;
  }

  void activateShield() {
    hasShield = true;
    shieldTimer = 3.0; // 3 seconds
    paint = Paint()..color = Colors.blue;
  }

  @override
  bool onCollisionStart(Set<Vector2> intersectionPoints, PositionComponent other) {
    if (other is Obstacle) {
      if (hasShield) {
        // Shield protects from one hit
        hasShield = false;
        shieldTimer = 0;
        paint = Paint()..color = Colors.green;
        return true;
      } else {
        onObstacleHit();
        return true;
      }
    } else if (other is PowerUp) {
      if (other.type == PowerUpType.shield) {
        activateShield();
      }
      
      onPowerUpCollected(other.type);
      onPowerUpRemove(other);
      other.removeFromParent();
      return true;
    }
    return false;
  }

  void checkNearMiss(Vector2 obstaclePosition, double obstacleSize) {
    if (nearMissTimer > 0) return; // Prevent spam
    
    final distance = (position - obstaclePosition).length;
    if (distance < obstacleSize + 40 && distance > obstacleSize + 15) {
      onNearMiss();
      nearMissTimer = 1.0; // 1 second cooldown
    }
  }
}

/// Obstacle types
enum ObstacleType { spike, movingPlatform, wall }

/// Obstacle component
class Obstacle extends RectangleComponent with HasCollisionDetection {
  Obstacle({
    required super.position,
    required this.type,
    required double width,
    required double height,
    this.moveSpeed = 0,
  }) : super(size: Vector2(width, height));

  final ObstacleType type;
  final double moveSpeed;
  double direction = 1;

  @override
  Future<void> onLoad() async {
    add(RectangleHitbox());
    
    switch (type) {
      case ObstacleType.spike:
        paint = Paint()..color = Colors.red;
        break;
      case ObstacleType.movingPlatform:
        paint = Paint()..color = Colors.orange;
        break;
      case ObstacleType.wall:
        paint = Paint()..color = Colors.grey;
        break;
    }
  }

  @override
  void update(double dt) {
    super.update(dt);
    
    if (type == ObstacleType.movingPlatform && moveSpeed > 0) {
      position.x += direction * moveSpeed * dt;
      
      // Bounce off screen edges
      if (position.x <= 0 || position.x >= 720 - size.x) {
        direction *= -1;
      }
    }
  }
}

/// Power-up types
enum PowerUpType { shield, speedBoost }

/// Power-up component
class PowerUp extends CircleComponent with HasCollisionDetection {
  PowerUp({
    required super.position,
    required this.type,
  }) : super(radius: 15);

  final PowerUpType type;

  @override
  Future<void> onLoad() async {
    add(CircleHitbox());
    
    switch (type) {
      case PowerUpType.shield:
        paint = Paint()..color = Colors.blue;
        break;
      case PowerUpType.speedBoost:
        paint = Paint()..color = Colors.yellow;
        break;
    }
  }
}
