import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/theme/neon_theme.dart';
import 'glassmorphism_components.dart';
import '../../../core/models/user_model.dart';
import '../providers/leaderboard_providers.dart';

/// Enhanced Player ID Card for leaderboard display with profile customization
class PlayerIDCard extends ConsumerWidget {
  final String playerId;
  final String displayName;
  final int score;
  final int rank;
  final String? gameId;
  final bool isCurrentUser;
  final bool isMiniCard;
  final Color? gameColor;
  final VoidCallback? onTap;
  final VoidCallback? onChallenge;

  const PlayerIDCard({
    super.key,
    required this.playerId,
    required this.displayName,
    required this.score,
    required this.rank,
    this.gameId,
    this.isCurrentUser = false,
    this.isMiniCard = false,
    this.gameColor,
    this.onTap,
    this.onChallenge,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Performance monitoring
    final performanceMonitor = ref.watch(performanceMonitorProvider);
    final buildStartTime = DateTime.now();

    // Fetch user profile data
    final userProfile = ref.watch(userProfileProvider(playerId));

    final result = userProfile.when(
      data: (profile) {
        if (isMiniCard) {
          return _buildMiniCard(context, ref, profile);
        } else {
          return _buildFullCard(context, ref, profile);
        }
      },
      loading: () => _buildLoadingCard(),
      error: (error, stack) {
        // Fallback to basic card without profile data
        if (isMiniCard) {
          return _buildMiniCard(context, ref, null);
        } else {
          return _buildFullCard(context, ref, null);
        }
      },
    );

    // Record build performance
    final buildTime = DateTime.now().difference(buildStartTime);
    performanceMonitor.recordWidgetBuild('PlayerIDCard', buildTime);

    return result;
  }

  Widget _buildLoadingCard() {
    return GlassmorphismContainer(
      padding: const EdgeInsets.all(16),
      margin: const EdgeInsets.all(8),
      child: const Center(
        child: CircularProgressIndicator(),
      ),
    );
  }

  Widget _buildMiniCard(BuildContext context, WidgetRef ref, UserModel? profile) {
    final skillTitle = ref.watch(userSkillTitleProvider(playerId));
    return GestureDetector(
      onTap: onTap,
      child: GlassmorphismContainer(
        padding: const EdgeInsets.all(12),
        margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
        glowColor: isCurrentUser ? TapVerseColors.neonCyan : gameColor,
        glowIntensity: isCurrentUser ? 0.4 : 0.2,
        child: Row(
          children: [
            // Rank
            Container(
              width: 32,
              height: 32,
              decoration: BoxDecoration(
                color: _getRankColor(),
                shape: BoxShape.circle,
                border: Border.all(
                  color: Colors.white.withValues(alpha: 0.3),
                  width: 1,
                ),
              ),
              child: Center(
                child: Text(
                  '$rank',
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 14,
                  ),
                ),
              ),
            ),
            
            const SizedBox(width: 12),
            
            // Avatar
            CircleAvatar(
              radius: 20,
              backgroundColor: gameColor ?? TapVerseColors.neonPurple,
              child: Text(
                _getInitials(displayName),
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                  fontSize: 14,
                ),
              ),
            ),
            
            const SizedBox(width: 12),
            
            // Player info
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    displayName,
                    style: TextStyle(
                      color: Colors.white,
                      fontWeight: isCurrentUser ? FontWeight.bold : FontWeight.normal,
                      fontSize: 16,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                  skillTitle.when(
                    data: (title) => Text(
                      title,
                      style: TextStyle(
                        color: Colors.white.withValues(alpha: 0.7),
                        fontSize: 12,
                      ),
                    ),
                    loading: () => Text(
                      'Loading...',
                      style: TextStyle(
                        color: Colors.white.withValues(alpha: 0.5),
                        fontSize: 12,
                      ),
                    ),
                    error: (_, __) => Text(
                      'Player',
                      style: TextStyle(
                        color: Colors.white.withValues(alpha: 0.7),
                        fontSize: 12,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            
            // Score
            Text(
              _formatScore(score),
              style: TextStyle(
                color: gameColor ?? TapVerseColors.neonCyan,
                fontWeight: FontWeight.bold,
                fontSize: 16,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFullCard(BuildContext context, WidgetRef ref, UserModel? profile) {
    final skillTitle = ref.watch(userSkillTitleProvider(playerId));
    final equippedBanner = ref.watch(userEquippedBannerProvider(playerId));

    return equippedBanner.when(
      data: (bannerPath) => _buildCardWithCustomBackground(
        context, ref, profile, skillTitle, bannerPath,
      ),
      loading: () => _buildCardWithCustomBackground(
        context, ref, profile, skillTitle, null,
      ),
      error: (_, __) => _buildCardWithCustomBackground(
        context, ref, profile, skillTitle, null,
      ),
    );
  }

  Widget _buildCardWithCustomBackground(
    BuildContext context,
    WidgetRef ref,
    UserModel? profile,
    AsyncValue<String> skillTitle,
    String? bannerPath,
  ) {
    Widget cardContent = GlassmorphismContainer(
      padding: const EdgeInsets.all(20),
      margin: const EdgeInsets.all(16),
      glowColor: isCurrentUser ? TapVerseColors.neonCyan : gameColor,
      glowIntensity: isCurrentUser ? 0.5 : 0.3,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Header with avatar and basic info
          Row(
            children: [
              // Large avatar
              Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      gameColor ?? TapVerseColors.neonPurple,
                      (gameColor ?? TapVerseColors.neonPurple).withValues(alpha: 0.7),
                    ],
                  ),
                  border: Border.all(
                    color: Colors.white.withValues(alpha: 0.3),
                    width: 2,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: (gameColor ?? TapVerseColors.neonPurple).withValues(alpha: 0.3),
                      blurRadius: 15,
                      spreadRadius: 2,
                    ),
                  ],
                ),
                child: Center(
                  child: Text(
                    _getInitials(displayName),
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 28,
                    ),
                  ),
                ),
              ),
              
              const SizedBox(width: 16),
              
              // Player info
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      displayName,
                      style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        fontSize: 24,
                      ),
                    ),
                    const SizedBox(height: 4),
                    skillTitle.when(
                      data: (title) => Text(
                        title,
                        style: TextStyle(
                          color: TapVerseColors.neonGold,
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      loading: () => Text(
                        'Loading...',
                        style: TextStyle(
                          color: TapVerseColors.neonGold.withValues(alpha: 0.5),
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      error: (_, __) => Text(
                        'Player',
                        style: TextStyle(
                          color: TapVerseColors.neonGold,
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Icon(
                          Icons.emoji_events,
                          color: _getRankColor(),
                          size: 20,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          'Rank #$rank',
                          style: TextStyle(
                            color: _getRankColor(),
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 20),
          
          // Stats section
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  Colors.white.withValues(alpha: 0.1),
                  Colors.white.withValues(alpha: 0.05),
                ],
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildStatItem('Score', _formatScore(score), gameColor ?? TapVerseColors.neonCyan),
                _buildStatItem('Tokens', _formatNumber(profile?.tokens ?? 0), TapVerseColors.neonGold),
                _buildStatItem('Games', '${profile?.totalGamesPlayed ?? 0}', TapVerseColors.neonPink),
              ],
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Action buttons
          Row(
            children: [
              if (isCurrentUser) ...[
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: gameId != null ? () {
                      // Navigate to the specific game
                      Navigator.of(context).pop(); // Close the popup first
                      // Navigate to game screen with the game ID
                      Navigator.of(context).pushNamed('/game', arguments: gameId);
                    } : null,
                    icon: const Icon(Icons.play_arrow),
                    label: const Text('Improve My Score'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: gameColor ?? TapVerseColors.neonCyan,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                  ),
                ),
              ] else ...[
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: onChallenge,
                    icon: const Icon(Icons.sports_esports),
                    label: const Text('Challenge Player'),
                    style: OutlinedButton.styleFrom(
                      foregroundColor: gameColor ?? TapVerseColors.neonCyan,
                      side: BorderSide(color: gameColor ?? TapVerseColors.neonCyan),
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                  ),
                ),
              ],
            ],
          ),
        ],
      ),
    );

    // Wrap with custom background if banner is equipped
    if (bannerPath != null) {
      return Container(
        margin: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          image: DecorationImage(
            image: AssetImage(bannerPath),
            fit: BoxFit.cover,
            opacity: 0.2,
          ),
        ),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Colors.black.withValues(alpha: 0.3),
                Colors.black.withValues(alpha: 0.1),
              ],
            ),
          ),
          child: cardContent,
        ),
      );
    }

    return cardContent;
  }

  Widget _buildStatItem(String label, String value, Color color) {
    return Column(
      children: [
        Text(
          value,
          style: TextStyle(
            color: color,
            fontWeight: FontWeight.bold,
            fontSize: 18,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: TextStyle(
            color: Colors.white.withValues(alpha: 0.7),
            fontSize: 12,
          ),
        ),
      ],
    );
  }

  Color _getRankColor() {
    if (rank <= 3) {
      switch (rank) {
        case 1:
          return TapVerseColors.neonGold;
        case 2:
          return TapVerseColors.neonSilver;
        case 3:
          return TapVerseColors.neonBronze;
        default:
          return Colors.grey;
      }
    }
    return Colors.grey;
  }

  String _getInitials(String name) {
    if (name.isEmpty) return '?';
    
    final words = name.trim().split(' ');
    if (words.length == 1) {
      return words[0][0].toUpperCase();
    } else {
      return '${words[0][0]}${words[1][0]}'.toUpperCase();
    }
  }

  String _formatScore(int score) {
    if (score >= 1000000) {
      return '${(score / 1000000).toStringAsFixed(1)}M';
    } else if (score >= 1000) {
      return '${(score / 1000).toStringAsFixed(1)}K';
    } else {
      return score.toString();
    }
  }

  String _formatNumber(int number) {
    if (number >= 1000000) {
      return '${(number / 1000000).toStringAsFixed(1)}M';
    } else if (number >= 1000) {
      return '${(number / 1000).toStringAsFixed(1)}K';
    } else {
      return number.toString();
    }
  }
}
