import 'dart:math';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/models/game_state.dart';
import '../../../core/providers/game_providers.dart';
import '../../../core/services/game_feedback_service.dart';
import '../../../shared/widgets/base_game_widget.dart';

class BoomSweepGame extends BaseGameWidget {
  static const GameConfig gameConfig = GameConfig(
    gameId: 'boom_sweep',
    gameName: 'BoomSweep',
    scoreMultiplier: 1,
    hasLives: false,
    hasLevels: false,
    hasTimer: false,
  );

  const BoomSweepGame({
    super.key,
    super.onGameComplete,
    super.onGameExit,
  }) : super(config: gameConfig);

  @override
  Widget buildGameContent(BuildContext context, WidgetRef ref, GameState gameState) {
    return const _BoomSweepGameContent();
  }
}

enum CellState { hidden, revealed, flagged }

class Cell {
  bool isMine;
  int neighborMines;
  CellState state;
  bool isExploded;

  Cell({
    this.isMine = false,
    this.neighborMines = 0,
    this.state = CellState.hidden,
    this.isExploded = false,
  });
}

class _BoomSweepGameContent extends ConsumerStatefulWidget {
  const _BoomSweepGameContent();

  @override
  ConsumerState<_BoomSweepGameContent> createState() => _BoomSweepGameContentState();
}

class _BoomSweepGameContentState extends ConsumerState<_BoomSweepGameContent>
    with TickerProviderStateMixin {
  
  // Game state
  late AnimationController _explosionController;
  late AnimationController _revealController;
  
  // Grid
  List<List<Cell>> _grid = [];
  final int _rows = 12;
  final int _cols = 8;
  final int _mineCount = 12;
  final double _cellSize = 45;
  final Random _random = Random();
  
  // Game mechanics
  bool _gameStarted = false;
  bool _gameWon = false;
  int _revealedCells = 0;
  int _flaggedMines = 0;
  Offset? _explosionPosition;
  
  // Constants
  static const double _gameWidth = 360; // 8 * 45
  static const double _gameHeight = 540; // 12 * 45

  @override
  void initState() {
    super.initState();
    
    _explosionController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    
    _revealController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    
    _initializeGrid();
  }

  @override
  void dispose() {
    _explosionController.dispose();
    _revealController.dispose();
    super.dispose();
  }

  void _initializeGrid() {
    _grid = List.generate(_rows, (row) =>
        List.generate(_cols, (col) => Cell()));
    
    _placeMines();
    _calculateNeighborMines();
  }

  void _placeMines() {
    int minesPlaced = 0;
    while (minesPlaced < _mineCount) {
      final row = _random.nextInt(_rows);
      final col = _random.nextInt(_cols);
      
      if (!_grid[row][col].isMine) {
        _grid[row][col].isMine = true;
        minesPlaced++;
      }
    }
  }

  void _calculateNeighborMines() {
    for (int row = 0; row < _rows; row++) {
      for (int col = 0; col < _cols; col++) {
        if (!_grid[row][col].isMine) {
          _grid[row][col].neighborMines = _countNeighborMines(row, col);
        }
      }
    }
  }

  int _countNeighborMines(int row, int col) {
    int count = 0;
    for (int dr = -1; dr <= 1; dr++) {
      for (int dc = -1; dc <= 1; dc++) {
        if (dr == 0 && dc == 0) continue;
        
        final newRow = row + dr;
        final newCol = col + dc;
        
        if (newRow >= 0 && newRow < _rows && 
            newCol >= 0 && newCol < _cols &&
            _grid[newRow][newCol].isMine) {
          count++;
        }
      }
    }
    return count;
  }

  void _onCellTap(int row, int col) {
    final gameState = ref.read(gameStateProvider(BoomSweepGame.gameConfig));
    if (!gameState.isPlaying || _gameWon) return;
    
    final cell = _grid[row][col];
    if (cell.state != CellState.hidden) return;
    
    _gameStarted = true;
    
    if (cell.isMine) {
      _explodeCell(row, col);
    } else {
      _revealCell(row, col);
      _checkWinCondition();
    }
  }

  void _onCellLongPress(int row, int col) {
    final gameState = ref.read(gameStateProvider(BoomSweepGame.gameConfig));
    if (!gameState.isPlaying || _gameWon) return;
    
    final cell = _grid[row][col];
    if (cell.state == CellState.revealed) return;
    
    setState(() {
      if (cell.state == CellState.hidden) {
        cell.state = CellState.flagged;
        if (cell.isMine) _flaggedMines++;
      } else if (cell.state == CellState.flagged) {
        cell.state = CellState.hidden;
        if (cell.isMine) _flaggedMines--;
      }
    });
    
    // Trigger feedback
    ref.read(gameFeedbackServiceProvider).triggerFeedback(GameEvent.buttonPress);
  }

  void _explodeCell(int row, int col) {
    setState(() {
      _grid[row][col].isExploded = true;
      _explosionPosition = Offset(
        col * _cellSize + _cellSize / 2,
        row * _cellSize + _cellSize / 2,
      );
    });
    
    // Reveal all mines
    for (int r = 0; r < _rows; r++) {
      for (int c = 0; c < _cols; c++) {
        if (_grid[r][c].isMine) {
          _grid[r][c].state = CellState.revealed;
        }
      }
    }
    
    // Trigger feedback
    ref.read(gameFeedbackServiceProvider).triggerFeedback(GameEvent.obstacleHit);
    
    // Play explosion animation
    _explosionController.forward();
    
    // Game over
    final gameNotifier = ref.read(gameStateProvider(BoomSweepGame.gameConfig).notifier);
    gameNotifier.endGame(reason: 'Hit a mine');
  }

  void _revealCell(int row, int col) {
    final cell = _grid[row][col];
    if (cell.state != CellState.hidden || cell.isMine) return;
    
    setState(() {
      cell.state = CellState.revealed;
      _revealedCells++;
    });
    
    // Add score
    final gameNotifier = ref.read(gameStateProvider(BoomSweepGame.gameConfig).notifier);
    gameNotifier.addScore(1);
    
    // Trigger feedback
    ref.read(gameFeedbackServiceProvider).triggerFeedback(GameEvent.scoreIncrease);
    
    // Auto-reveal empty neighbors
    if (cell.neighborMines == 0) {
      for (int dr = -1; dr <= 1; dr++) {
        for (int dc = -1; dc <= 1; dc++) {
          if (dr == 0 && dc == 0) continue;
          
          final newRow = row + dr;
          final newCol = col + dc;
          
          if (newRow >= 0 && newRow < _rows && 
              newCol >= 0 && newCol < _cols) {
            _revealCell(newRow, newCol);
          }
        }
      }
    }
    
    // Play reveal animation
    _revealController.forward().then((_) => _revealController.reset());
  }

  void _checkWinCondition() {
    final totalSafeCells = _rows * _cols - _mineCount;
    if (_revealedCells >= totalSafeCells) {
      setState(() {
        _gameWon = true;
      });
      
      // Add bonus score
      final gameNotifier = ref.read(gameStateProvider(BoomSweepGame.gameConfig).notifier);
      gameNotifier.addScore(50); // Bonus for winning
      
      // Game complete
      gameNotifier.endGame(reason: 'All safe cells revealed - You Win!');
    }
  }

  @override
  Widget build(BuildContext context) {
    final gameState = ref.watch(gameStateProvider(BoomSweepGame.gameConfig));
    
    return Container(
      width: _gameWidth,
      height: _gameHeight + 100, // Extra space for UI
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [Color(0xFF2c3e50), Color(0xFF34495e)],
        ),
      ),
      child: Column(
        children: [
          // Game info
          _buildGameInfo(gameState),
          
          // Grid
          Expanded(
            child: Stack(
              children: [
                _buildGrid(),
                
                // Explosion effect
                if (_explosionPosition != null) _buildExplosion(),
                
                // Instructions overlay
                if (!gameState.isPlaying) _buildInstructions(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGameInfo(GameState gameState) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.black54,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                const Icon(Icons.flag, color: Colors.red, size: 16),
                const SizedBox(width: 4),
                Text(
                  '${_mineCount - _flaggedMines}',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.black54,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                const Icon(Icons.star, color: Colors.yellow, size: 16),
                const SizedBox(width: 4),
                Text(
                  '${gameState.score}',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGrid() {
    return Container(
      padding: const EdgeInsets.all(8),
      child: GridView.builder(
        physics: const NeverScrollableScrollPhysics(),
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: _cols,
          childAspectRatio: 1,
          crossAxisSpacing: 1,
          mainAxisSpacing: 1,
        ),
        itemCount: _rows * _cols,
        itemBuilder: (context, index) {
          final row = index ~/ _cols;
          final col = index % _cols;
          return _buildCell(row, col);
        },
      ),
    );
  }

  Widget _buildCell(int row, int col) {
    final cell = _grid[row][col];
    
    return GestureDetector(
      onTap: () => _onCellTap(row, col),
      onLongPress: () => _onCellLongPress(row, col),
      child: Container(
        decoration: BoxDecoration(
          color: _getCellColor(cell),
          border: Border.all(color: Colors.grey[600]!, width: 1),
          borderRadius: BorderRadius.circular(4),
        ),
        child: Center(
          child: _getCellContent(cell),
        ),
      ),
    );
  }

  Color _getCellColor(Cell cell) {
    if (cell.isExploded) return Colors.red[700]!;
    if (cell.state == CellState.revealed) {
      if (cell.isMine) return Colors.red[400]!;
      return Colors.grey[300]!;
    }
    if (cell.state == CellState.flagged) return Colors.orange[300]!;
    return Colors.grey[500]!;
  }

  Widget _getCellContent(Cell cell) {
    if (cell.state == CellState.flagged) {
      return const Icon(Icons.flag, color: Colors.red, size: 20);
    }
    
    if (cell.state == CellState.revealed) {
      if (cell.isMine) {
        return Icon(
          cell.isExploded ? Icons.whatshot : Icons.circle,
          color: cell.isExploded ? Colors.yellow : Colors.black,
          size: 20,
        );
      }
      
      if (cell.neighborMines > 0) {
        return Text(
          '${cell.neighborMines}',
          style: TextStyle(
            color: _getNumberColor(cell.neighborMines),
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        );
      }
    }
    
    return const SizedBox.shrink();
  }

  Color _getNumberColor(int number) {
    switch (number) {
      case 1: return Colors.blue;
      case 2: return Colors.green;
      case 3: return Colors.red;
      case 4: return Colors.purple;
      case 5: return Colors.brown;
      case 6: return Colors.pink;
      case 7: return Colors.black;
      case 8: return Colors.grey;
      default: return Colors.black;
    }
  }

  Widget _buildExplosion() {
    return Positioned(
      left: _explosionPosition!.dx - 30,
      top: _explosionPosition!.dy - 30,
      child: ScaleTransition(
        scale: _explosionController,
        child: Container(
          width: 60,
          height: 60,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            gradient: RadialGradient(
              colors: [Colors.yellow, Colors.orange, Colors.red, Colors.transparent],
              stops: const [0.0, 0.3, 0.6, 1.0],
            ),
          ),
          child: const Center(
            child: Text(
              '💥',
              style: TextStyle(fontSize: 30),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildInstructions() {
    return Container(
      color: Colors.black54,
      child: const Center(
        child: Padding(
          padding: EdgeInsets.all(16),
          child: Text(
            'Tap to reveal cells!\nLong press to flag mines.\nReveal all safe cells to win!\n\nNumbers show nearby mines.',
            textAlign: TextAlign.center,
            style: TextStyle(
              color: Colors.white,
              fontSize: 16,
            ),
          ),
        ),
      ),
    );
  }
}
