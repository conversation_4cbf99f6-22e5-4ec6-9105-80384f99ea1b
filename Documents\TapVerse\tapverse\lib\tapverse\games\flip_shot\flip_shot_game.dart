import 'dart:math' as math;
import 'package:flame/components.dart';
import 'package:flame/collisions.dart';
import 'package:flame/events.dart';
import 'package:flame/game.dart';
import 'package:flutter/material.dart';
import '../../core/mini_game_base.dart';
import '../../core/hud.dart';
import '../../core/effects.dart';
import '../../core/analytics.dart';
import '../../core/difficulty.dart';

/// FlipShot - Ricochet shooting game with trajectory preview
/// Controls: Hold to aim (trajectory preview), release to shoot; quick tap fires default
/// Scoring: +10 per target; +10 for multi-hit single bullet
class FlipShotGame extends MiniGameBase {
  FlipShotGame(int seed) : super(modeId: 'flip_shot', sessionSeed: seed) {
    rng = math.Random(seed);
  }

  late math.Random rng;
  late ScoreText scoreText;
  late TextComponent ammoText;
  
  // Game state
  int score = 0;
  int ammo = 5;
  int targetsRemaining = 5;
  
  // Game entities
  late Shooter shooter;
  late List<Target> targets;
  late List<Wall> walls;
  late List<Bullet> bullets;
  
  // Aiming state
  bool isAiming = false;
  Vector2 aimDirection = Vector2(1, 0);
  List<Vector2> trajectoryPreview = [];
  
  // Difficulty curves (from planning document)
  final targetCountCurve = const CurveParam(start: 5, max: 12, perMinute: 3.5);
  
  // Difficulty manager
  late DifficultyManager difficultyManager;

  @override
  Future<void> loadAssets() async {
    // Load any required assets here
  }

  @override
  void setupWorld() {
    camera.viewfinder.visibleGameSize = Vector2(720, 1280);
    
    // Initialize difficulty manager
    difficultyManager = DifficultyManager();
    difficultyManager.addParameter('target_count', targetCountCurve);
    
    // Initialize collections
    targets = [];
    walls = [];
    bullets = [];
    
    // Create game entities
    _createGameEntities();
  }

  void _createGameEntities() {
    // Create shooter
    shooter = Shooter(position: Vector2(360, 1200));
    add(shooter);

    // Create walls for ricochets
    _createWalls();
    
    // Generate initial level
    _generateLevel();
  }

  void _createWalls() {
    // Border walls
    walls.addAll([
      Wall(position: Vector2(0, 0), width: 20, height: 1280), // Left
      Wall(position: Vector2(700, 0), width: 20, height: 1280), // Right
      Wall(position: Vector2(0, 0), width: 720, height: 20), // Top
      Wall(position: Vector2(0, 1260), width: 720, height: 20), // Bottom
    ]);
    
    // Interior walls for ricochets
    walls.addAll([
      Wall(position: Vector2(200, 300), width: 20, height: 200),
      Wall(position: Vector2(500, 500), width: 20, height: 200),
      Wall(position: Vector2(100, 800), width: 200, height: 20),
      Wall(position: Vector2(400, 900), width: 200, height: 20),
    ]);
    
    for (final wall in walls) {
      add(wall);
    }
  }

  void _generateLevel() {
    final targetCount = difficultyManager.getValue('target_count').round();
    
    // Clear existing targets
    for (final target in targets) {
      target.removeFromParent();
    }
    targets.clear();
    
    // Generate new targets
    for (int i = 0; i < targetCount; i++) {
      _generateTarget();
    }
    
    targetsRemaining = targetCount;
    ammo = math.max(3, (targetCount * 0.6).round()); // Limited ammo
  }

  void _generateTarget() {
    Vector2 position;
    int attempts = 0;
    
    do {
      position = Vector2(
        50 + rng.nextDouble() * 620,
        100 + rng.nextDouble() * 800,
      );
      attempts++;
    } while (attempts < 20 && _isPositionBlocked(position));
    
    final target = Target(
      position: position,
      onHit: _onTargetHit,
    );
    
    targets.add(target);
    add(target);
  }

  bool _isPositionBlocked(Vector2 position) {
    // Check if position overlaps with walls or other targets
    for (final wall in walls) {
      if (wall.toRect().contains(position.toOffset())) {
        return true;
      }
    }
    
    for (final target in targets) {
      if ((target.position - position).length < 60) {
        return true;
      }
    }
    
    return false;
  }

  @override
  void setupHUD() {
    // Score display
    scoreText = ScoreText(
      position: Vector2(16, 16),
      textStyle: const TextStyle(
        color: Colors.white,
        fontSize: 24,
        fontWeight: FontWeight.bold,
      ),
    );
    add(scoreText);

    // Ammo display
    ammoText = TextComponent(
      text: 'Ammo: $ammo',
      position: Vector2(16, 50),
      textRenderer: TextPaint(
        style: const TextStyle(
          color: Colors.yellow,
          fontSize: 20,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
    add(ammoText);
  }

  @override
  void onStart() {
    score = 0;
    
    // Start difficulty progression
    difficultyManager.start();
    
    // Start the game
    startGame();
    
    Analytics.log('flip_shot_start', {'session_seed': sessionSeed});
  }

  @override
  void onGameUpdate(double dt) {
    // Update ammo display
    ammoText.text = 'Ammo: $ammo';
    
    // Update trajectory preview if aiming
    if (isAiming) {
      _updateTrajectoryPreview();
    }
    
    // Check win condition
    if (targetsRemaining <= 0) {
      _levelComplete();
    }
    
    // Check lose condition
    if (ammo <= 0 && bullets.isEmpty && targetsRemaining > 0) {
      _gameOver();
    }
    
    // Clean up finished bullets
    bullets.removeWhere((bullet) => bullet.isFinished);
  }

  void _updateTrajectoryPreview() {
    trajectoryPreview.clear();
    
    // Calculate trajectory with up to 3 bounces
    Vector2 pos = shooter.position.clone();
    Vector2 dir = aimDirection.normalized();
    const double step = 20.0;
    int bounces = 0;
    
    for (int i = 0; i < 100 && bounces < 3; i++) {
      pos += dir * step;
      
      // Check wall collisions
      for (final wall in walls) {
        if (wall.toRect().contains(pos.toOffset())) {
          // Calculate reflection
          final normal = _getWallNormal(wall, pos);
          dir = _reflect(dir, normal);
          bounces++;
          break;
        }
      }
      
      trajectoryPreview.add(pos.clone());
      
      // Stop if out of bounds
      if (pos.x < 0 || pos.x > 720 || pos.y < 0 || pos.y > 1280) {
        break;
      }
    }
  }

  Vector2 _getWallNormal(Wall wall, Vector2 hitPoint) {
    // Simplified normal calculation
    if (wall.size.x > wall.size.y) {
      return Vector2(0, hitPoint.y < wall.position.y + wall.size.y / 2 ? -1 : 1);
    } else {
      return Vector2(hitPoint.x < wall.position.x + wall.size.x / 2 ? -1 : 1, 0);
    }
  }

  Vector2 _reflect(Vector2 velocity, Vector2 normal) {
    return velocity - normal * (2 * velocity.dot(normal));
  }

  // TODO: Implement input handling
  void handlePanStart(Vector2 position) {
    if (isGameEnded || ammo <= 0) return;

    isAiming = true;
    aimDirection = (position - shooter.position).normalized();
  }

  void handlePanUpdate(Vector2 position) {
    if (!isAiming) return;

    aimDirection = (position - shooter.position).normalized();
  }

  void handlePanEnd(Vector2 position) {
    if (!isAiming) return;

    isAiming = false;
    _shoot();
  }

  @override
  bool onTapDown(TapDownInfo info) {
    if (isGameEnded || ammo <= 0) return false;
    
    // Quick tap fires in default direction
    aimDirection = Vector2(0, -1); // Straight up
    _shoot();
    return true;
  }

  void _shoot() {
    if (ammo <= 0) return;
    
    ammo--;
    
    final bullet = Bullet(
      position: shooter.position.clone(),
      direction: aimDirection.normalized(),
      onTargetHit: _onBulletTargetHit,
      onWallHit: _onBulletWallHit,
      onFinished: _onBulletFinished,
    );
    
    bullets.add(bullet);
    add(bullet);
    
    Analytics.log('bullet_fired', {
      'direction_x': aimDirection.x,
      'direction_y': aimDirection.y,
      'ammo_remaining': ammo,
    });
  }

  void _onBulletTargetHit(Bullet bullet, Target target) {
    // Target hit by bullet
    bullet.addHit();
    _onTargetHit(target);
  }

  void _onBulletWallHit(Bullet bullet, Wall wall) {
    // Bullet bounced off wall
    add(Effects.burst(bullet.position, color: Colors.orange, particleCount: 5));
  }

  void _onBulletFinished(Bullet bullet) {
    // Bullet finished - check for multi-hit bonus
    if (bullet.hitCount > 1) {
      score += 10; // Multi-hit bonus
      addScore(10);
      scoreText.updateScore(score);
      
      add(Effects.textPopup(
        bullet.position,
        'MULTI-HIT +10',
        color: Colors.orange,
      ));
    }
    
    bullets.remove(bullet);
  }

  void _onTargetHit(Target target) {
    targetsRemaining--;
    score += 10;
    addScore(10);
    scoreText.updateScore(score);
    
    // Visual effects
    add(Effects.explosion(target.position));
    add(Effects.textPopup(
      target.position,
      '+10',
      color: Colors.green,
    ));
    
    targets.remove(target);
    target.removeFromParent();
    
    Analytics.log('target_hit', {
      'targets_remaining': targetsRemaining,
      'score': score,
    });
  }

  void _levelComplete() {
    // Generate next level
    _generateLevel();
    
    Analytics.log('level_complete', {
      'score': score,
    });
  }

  void _gameOver() {
    endGame(success: false, score: score);
    
    Analytics.log('game_over', {
      'final_score': score,
      'targets_remaining': targetsRemaining,
    });
  }


  @override
  void render(Canvas canvas) {
    super.render(canvas);
    
    // Draw trajectory preview
    if (isAiming && trajectoryPreview.isNotEmpty) {
      final paint = Paint()
        ..color = Colors.white.withOpacity(0.5)
        ..strokeWidth = 2;
      
      for (int i = 0; i < trajectoryPreview.length - 1; i++) {
        canvas.drawLine(
          trajectoryPreview[i].toOffset(),
          trajectoryPreview[i + 1].toOffset(),
          paint,
        );
      }
    }
  }

  @override
  void reportScore(int score) {
    // Integration hook for TapVerse UI
  }

  @override
  void awardTokens(int tokens) {
    // Integration hook for TapVerse token system
  }
}

/// Shooter component
class Shooter extends CircleComponent {
  Shooter({required super.position}) : super(radius: 15);

  @override
  Future<void> onLoad() async {
    paint = Paint()..color = Colors.blue;
  }
}

/// Target component
class Target extends CircleComponent with HasCollisionDetection {
  Target({
    required super.position,
    required this.onHit,
  }) : super(radius: 25);

  final Function(Target) onHit;

  @override
  Future<void> onLoad() async {
    add(CircleHitbox());
    paint = Paint()..color = Colors.red;
  }
}

/// Wall component for ricochets
class Wall extends RectangleComponent with HasCollisionDetection {
  Wall({
    required super.position,
    required double width,
    required double height,
  }) : super(size: Vector2(width, height));

  @override
  Future<void> onLoad() async {
    add(RectangleHitbox());
    paint = Paint()..color = Colors.grey;
  }
}

/// Bullet component with ricochet physics
class Bullet extends CircleComponent with HasCollisionDetection, CollisionCallbacks {
  Bullet({
    required super.position,
    required this.direction,
    required this.onTargetHit,
    required this.onWallHit,
    required this.onFinished,
  }) : super(radius: 3);

  final Vector2 direction;
  final Function(Bullet, Target) onTargetHit;
  final Function(Bullet, Wall) onWallHit;
  final Function(Bullet) onFinished;

  Vector2 velocity = Vector2.zero();
  int bounces = 0;
  int hitCount = 0;
  bool isFinished = false;
  static const double speed = 400;
  static const int maxBounces = 3;

  @override
  Future<void> onLoad() async {
    add(CircleHitbox());
    paint = Paint()..color = Colors.yellow;
    velocity = direction * speed;
  }

  @override
  void update(double dt) {
    super.update(dt);
    
    if (isFinished) return;
    
    position += velocity * dt;
    
    // Check bounds
    if (position.x < 0 || position.x > 720 || position.y < 0 || position.y > 1280) {
      _finish();
    }
  }

  void addHit() {
    hitCount++;
  }

  void _finish() {
    isFinished = true;
    onFinished(this);
    removeFromParent();
  }

  @override
  bool onCollisionStart(Set<Vector2> intersectionPoints, PositionComponent other) {
    if (other is Target) {
      onTargetHit(this, other);
      return true;
    } else if (other is Wall && bounces < maxBounces) {
      bounces++;
      
      // Calculate reflection
      final normal = _getWallNormal(other, intersectionPoints.first);
      velocity = _reflect(velocity, normal);
      
      onWallHit(this, other);
      
      if (bounces >= maxBounces) {
        _finish();
      }
      
      return true;
    }
    return false;
  }

  Vector2 _getWallNormal(Wall wall, Vector2 hitPoint) {
    if (wall.size.x > wall.size.y) {
      return Vector2(0, hitPoint.y < wall.position.y + wall.size.y / 2 ? -1 : 1);
    } else {
      return Vector2(hitPoint.x < wall.position.x + wall.size.x / 2 ? -1 : 1, 0);
    }
  }

  Vector2 _reflect(Vector2 velocity, Vector2 normal) {
    return velocity - normal * (2 * velocity.dot(normal));
  }
}
