import 'dart:math';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../../../core/models/game_state.dart';
import '../../../core/providers/game_providers.dart';
import '../../../core/services/game_feedback_service.dart';
import '../../../shared/widgets/base_game_widget.dart';

class DashRushGame extends BaseGameWidget {
  static const GameConfig gameConfig = GameConfig(
    gameId: 'dash_rush',
    gameName: 'DashRush',
    scoreMultiplier: 1,
    hasLives: false,
    hasLevels: false,
    hasTimer: false,
  );

  const DashRushGame({
    super.key,
    super.onGameComplete,
    super.onGameExit,
  }) : super(config: gameConfig);

  @override
  Widget buildGameContent(BuildContext context, WidgetRef ref, GameState gameState) {
    return const _DashRushGameContent();
  }
}

class Obstacle {
  double x;
  final double y;
  final double width;
  final double height;
  final Color color;
  bool passed;

  Obstacle({
    required this.x,
    required this.y,
    required this.width,
    required this.height,
    required this.color,
    this.passed = false,
  });
}

class _DashRushGameContent extends ConsumerStatefulWidget {
  const _DashRushGameContent();

  @override
  ConsumerState<_DashRushGameContent> createState() => _DashRushGameContentState();
}

class _DashRushGameContentState extends ConsumerState<_DashRushGameContent>
    with TickerProviderStateMixin {
  
  // Game state
  late AnimationController _playerController;
  late AnimationController _speedController;
  
  // Player state
  double _playerX = 200;
  double _playerY = 400;
  final double _playerSize = 30;
  
  // Game mechanics
  final List<Obstacle> _obstacles = [];
  double _gameSpeed = 200;
  double _distance = 0;
  double _obstacleSpawnTimer = 0;
  final double _obstacleSpawnInterval = 1.5;
  final Random _random = Random();
  
  // Visual effects
  final List<Offset> _speedLines = [];
  bool _showNearMissEffect = false;
  
  // Constants
  static const double _gameWidth = 400;
  static const double _gameHeight = 600;
  static const double _groundY = 500;

  @override
  void initState() {
    super.initState();
    
    _playerController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );
    
    _speedController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _generateSpeedLines();
    _startGameLoop();
  }

  @override
  void dispose() {
    _playerController.dispose();
    _speedController.dispose();
    super.dispose();
  }

  void _generateSpeedLines() {
    _speedLines.clear();
    for (int i = 0; i < 20; i++) {
      _speedLines.add(Offset(
        _random.nextDouble() * _gameWidth,
        _random.nextDouble() * _gameHeight,
      ));
    }
  }

  void _startGameLoop() {
    const frameRate = 60;
    const frameDuration = Duration(milliseconds: 1000 ~/ frameRate);
    const deltaTime = 1.0 / frameRate;
    
    void gameLoop() {
      if (!mounted) return;
      
      final gameState = ref.read(gameStateProvider(DashRushGame.gameConfig));
      if (!gameState.isPlaying) {
        Future.delayed(frameDuration, gameLoop);
        return;
      }
      
      setState(() {
        // Update distance and speed
        _distance += _gameSpeed * deltaTime;
        _gameSpeed += 5 * deltaTime; // Gradually increase speed
        
        // Update score based on distance
        final newScore = (_distance / 10).floor();
        final gameNotifier = ref.read(gameStateProvider(DashRushGame.gameConfig).notifier);
        if (newScore > gameState.score) {
          gameNotifier.setScore(newScore);
        }
        
        // Spawn obstacles
        _obstacleSpawnTimer += deltaTime;
        if (_obstacleSpawnTimer >= _obstacleSpawnInterval) {
          _spawnObstacle();
          _obstacleSpawnTimer = 0;
        }
        
        // Update obstacles
        for (final obstacle in _obstacles) {
          obstacle.x -= _gameSpeed * deltaTime;
          
          // Check if player passed obstacle
          if (!obstacle.passed && obstacle.x + obstacle.width < _playerX) {
            obstacle.passed = true;
            _checkNearMiss(obstacle);
          }
        }
        
        // Update speed lines
        for (int i = 0; i < _speedLines.length; i++) {
          _speedLines[i] = Offset(
            _speedLines[i].dx - _gameSpeed * deltaTime * 0.5,
            _speedLines[i].dy,
          );
          
          if (_speedLines[i].dx < 0) {
            _speedLines[i] = Offset(_gameWidth, _random.nextDouble() * _gameHeight);
          }
        }
        
        // Check collisions
        _checkCollisions();
        
        // Remove off-screen obstacles
        _obstacles.removeWhere((obstacle) => obstacle.x < -obstacle.width);
      });
      
      Future.delayed(frameDuration, gameLoop);
    }
    
    gameLoop();
  }

  void _spawnObstacle() {
    final obstacleTypes = [
      // Ground obstacles
      {'y': _groundY - 40, 'width': 30.0, 'height': 40.0, 'color': Colors.red},
      {'y': _groundY - 60, 'width': 40.0, 'height': 60.0, 'color': Colors.orange},
      // Air obstacles
      {'y': 300.0, 'width': 50.0, 'height': 20.0, 'color': Colors.purple},
      {'y': 250.0, 'width': 60.0, 'height': 30.0, 'color': Colors.blue},
    ];
    
    final obstacleData = obstacleTypes[_random.nextInt(obstacleTypes.length)];
    
    _obstacles.add(Obstacle(
      x: _gameWidth + 50,
      y: obstacleData['y'] as double,
      width: obstacleData['width'] as double,
      height: obstacleData['height'] as double,
      color: obstacleData['color'] as Color,
    ));
  }

  void _checkNearMiss(Obstacle obstacle) {
    final playerLeft = _playerX - _playerSize / 2;
    final playerRight = _playerX + _playerSize / 2;
    final playerTop = _playerY - _playerSize / 2;
    final playerBottom = _playerY + _playerSize / 2;
    
    final obstacleLeft = obstacle.x;
    final obstacleRight = obstacle.x + obstacle.width;
    final obstacleTop = obstacle.y;
    final obstacleBottom = obstacle.y + obstacle.height;
    
    // Check if it was a near miss (within 20 pixels)
    final horizontalDistance = min(
      (playerLeft - obstacleRight).abs(),
      (playerRight - obstacleLeft).abs(),
    );
    
    final verticalDistance = min(
      (playerTop - obstacleBottom).abs(),
      (playerBottom - obstacleTop).abs(),
    );
    
    if (horizontalDistance < 20 || verticalDistance < 20) {
      // Near miss!
      setState(() {
        _showNearMissEffect = true;
      });
      
      ref.read(gameFeedbackServiceProvider).triggerFeedback(GameEvent.targetHit);
      
      _speedController.forward().then((_) {
        _speedController.reset();
        setState(() {
          _showNearMissEffect = false;
        });
      });
    }
  }

  void _checkCollisions() {
    final playerLeft = _playerX - _playerSize / 2;
    final playerRight = _playerX + _playerSize / 2;
    final playerTop = _playerY - _playerSize / 2;
    final playerBottom = _playerY + _playerSize / 2;
    
    for (final obstacle in _obstacles) {
      final obstacleLeft = obstacle.x;
      final obstacleRight = obstacle.x + obstacle.width;
      final obstacleTop = obstacle.y;
      final obstacleBottom = obstacle.y + obstacle.height;
      
      if (playerRight > obstacleLeft &&
          playerLeft < obstacleRight &&
          playerBottom > obstacleTop &&
          playerTop < obstacleBottom) {
        // Collision detected
        final gameNotifier = ref.read(gameStateProvider(DashRushGame.gameConfig).notifier);
        gameNotifier.endGame(reason: 'Hit obstacle');
        
        ref.read(gameFeedbackServiceProvider).triggerFeedback(GameEvent.obstacleHit);
        return;
      }
    }
  }

  void _onPanEnd(DragEndDetails details) {
    final gameState = ref.read(gameStateProvider(DashRushGame.gameConfig));
    if (!gameState.isPlaying) return;
    
    final velocity = details.velocity.pixelsPerSecond;
    final dx = velocity.dx.abs();
    final dy = velocity.dy.abs();
    
    setState(() {
      if (dx > dy) {
        // Horizontal swipe
        if (velocity.dx > 0) {
          // Swipe right
          _playerX = (_playerX + 80).clamp(_playerSize / 2, _gameWidth - _playerSize / 2);
        } else {
          // Swipe left
          _playerX = (_playerX - 80).clamp(_playerSize / 2, _gameWidth - _playerSize / 2);
        }
      } else {
        // Vertical swipe
        if (velocity.dy > 0) {
          // Swipe down
          _playerY = (_playerY + 80).clamp(_playerSize / 2, _groundY - _playerSize / 2);
        } else {
          // Swipe up
          _playerY = (_playerY - 80).clamp(_playerSize / 2, _gameHeight - _playerSize / 2);
        }
      }
    });
    
    // Trigger feedback
    ref.read(gameFeedbackServiceProvider).triggerFeedback(GameEvent.buttonPress);
    
    // Play movement animation
    _playerController.forward().then((_) => _playerController.reset());
  }

  @override
  Widget build(BuildContext context) {
    final gameState = ref.watch(gameStateProvider(DashRushGame.gameConfig));
    
    return Container(
      width: _gameWidth,
      height: _gameHeight,
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [Color(0xFF87CEEB), Color(0xFFFFE4B5)],
        ),
      ),
      child: GestureDetector(
        onPanEnd: _onPanEnd,
        child: Stack(
          children: [
            // Speed lines background
            _buildSpeedLines(),
            
            // Ground
            _buildGround(),
            
            // Obstacles
            ..._obstacles.map((obstacle) => _buildObstacle(obstacle)),
            
            // Player
            _buildPlayer(),
            
            // Near miss effect
            if (_showNearMissEffect) _buildNearMissEffect(),
            
            // Game info
            _buildGameInfo(gameState),
            
            // Instructions
            if (!gameState.isPlaying) _buildInstructions(),
          ],
        ),
      ),
    );
  }

  Widget _buildSpeedLines() {
    return Stack(
      children: _speedLines.map((line) => Positioned(
        left: line.dx,
        top: line.dy,
        child: Container(
          width: 20,
          height: 2,
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.3),
            borderRadius: BorderRadius.circular(1),
          ),
        ),
      )).toList(),
    );
  }

  Widget _buildGround() {
    return Positioned(
      bottom: 0,
      left: 0,
      right: 0,
      child: Container(
        height: _gameHeight - _groundY,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [Colors.brown[400]!, Colors.brown[600]!],
          ),
        ),
      ),
    );
  }

  Widget _buildObstacle(Obstacle obstacle) {
    return Positioned(
      left: obstacle.x,
      top: obstacle.y,
      child: Container(
        width: obstacle.width,
        height: obstacle.height,
        decoration: BoxDecoration(
          color: obstacle.color,
          borderRadius: BorderRadius.circular(4),
          border: Border.all(color: Colors.black, width: 1),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.3),
              blurRadius: 4,
              offset: const Offset(2, 2),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPlayer() {
    return Positioned(
      left: _playerX - _playerSize / 2,
      top: _playerY - _playerSize / 2,
      child: ScaleTransition(
        scale: Tween<double>(begin: 1.0, end: 1.2).animate(_playerController),
        child: Container(
          width: _playerSize,
          height: _playerSize,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            gradient: RadialGradient(
              colors: [Colors.cyan[300]!, Colors.blue[600]!],
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.cyan.withOpacity(0.6),
                blurRadius: 8,
                spreadRadius: 2,
              ),
            ],
          ),
          child: const Center(
            child: Text(
              '🏃',
              style: TextStyle(fontSize: 20),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildNearMissEffect() {
    return Positioned(
      left: _playerX - 40,
      top: _playerY - 40,
      child: ScaleTransition(
        scale: _speedController,
        child: Container(
          width: 80,
          height: 80,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            gradient: RadialGradient(
              colors: [Colors.yellow, Colors.orange, Colors.transparent],
              stops: const [0.0, 0.5, 1.0],
            ),
          ),
          child: const Center(
            child: Text(
              'CLOSE!',
              style: TextStyle(
                color: Colors.white,
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildGameInfo(GameState gameState) {
    return Positioned(
      top: 30,
      left: 20,
      right: 20,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.black54,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              children: [
                const Text(
                  'Distance',
                  style: TextStyle(color: Colors.white, fontSize: 12),
                ),
                Text(
                  '${_distance.toInt()}m',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.black54,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              children: [
                const Text(
                  'Speed',
                  style: TextStyle(color: Colors.white, fontSize: 12),
                ),
                Text(
                  '${_gameSpeed.toInt()}',
                  style: const TextStyle(
                    color: Colors.cyan,
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInstructions() {
    return Positioned(
      bottom: 100,
      left: 0,
      right: 0,
      child: Center(
        child: Container(
          padding: EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.black54,
            borderRadius: BorderRadius.all(Radius.circular(12)),
          ),
          child: Text(
            'Swipe to dodge obstacles!\nUp/Down to jump/duck.\nLeft/Right to move sideways.\nSpeed increases over time!',
            textAlign: TextAlign.center,
            style: TextStyle(
              color: Colors.white,
              fontSize: 16,
            ),
          ),
        ),
      ),
    );
  }
}
