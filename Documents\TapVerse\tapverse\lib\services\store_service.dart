import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';

class StoreService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static final FirebaseAuth _auth = FirebaseAuth.instance;

  /// Get all store items
  static Future<List<Map<String, dynamic>>> getStoreItems() async {
    try {
      final snapshot = await _firestore.collection('store').doc('items').get();
      if (snapshot.exists) {
        final data = snapshot.data() as Map<String, dynamic>;
        return data.entries.map((entry) => {
          'id': entry.key,
          ...entry.value as Map<String, dynamic>,
        }).toList();
      }
      return [];
    } catch (e) {
      print('Error getting store items: $e');
      return [];
    }
  }

  /// Get all boost items
  static Future<List<Map<String, dynamic>>> getBoostItems() async {
    try {
      final snapshot = await _firestore.collection('store').doc('boosts').get();
      if (snapshot.exists) {
        final data = snapshot.data() as Map<String, dynamic>;
        return data.entries.map((entry) => {
          'id': entry.key,
          ...entry.value as Map<String, dynamic>,
        }).toList();
      }
      return [];
    } catch (e) {
      print('Error getting boost items: $e');
      return [];
    }
  }

  /// Get user's owned items
  static Future<List<String>> getUserOwnedItems() async {
    try {
      final user = _auth.currentUser;
      if (user == null) return [];

      final snapshot = await _firestore
          .collection('users')
          .doc(user.uid)
          .collection('ownedItems')
          .get();

      return snapshot.docs.map((doc) => doc.id).toList();
    } catch (e) {
      print('Error getting owned items: $e');
      return [];
    }
  }

  /// Get user's token balance
  static Future<int> getUserTokens() async {
    try {
      final user = _auth.currentUser;
      if (user == null) return 0;

      final snapshot = await _firestore
          .collection('users')
          .doc(user.uid)
          .get();

      if (snapshot.exists) {
        final data = snapshot.data() as Map<String, dynamic>;
        return data['tokens'] ?? 0;
      }
      return 0;
    } catch (e) {
      print('Error getting user tokens: $e');
      return 0;
    }
  }

  /// Temporary purchase method (CLIENT-SIDE ONLY - NOT SECURE)
  /// This will be replaced with Cloud Functions later
  static Future<Map<String, dynamic>> purchaseItem({
    required String itemId,
    required String itemType,
    required int cost,
  }) async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        return {'success': false, 'error': 'User not authenticated'};
      }

      // WARNING: This is client-side only and not secure!
      // Users can modify their token balance directly
      // This should be replaced with Cloud Functions

      final userRef = _firestore.collection('users').doc(user.uid);
      
      return await _firestore.runTransaction((transaction) async {
        // Check if user already owns this item
        final ownedItemRef = userRef.collection('ownedItems').doc(itemId);
        final ownedItemDoc = await transaction.get(ownedItemRef);
        
        if (ownedItemDoc.exists) {
          throw Exception('User already owns this item');
        }

        // Get user's current token balance
        final userDoc = await transaction.get(userRef);
        final userData = userDoc.data() ?? {};
        final currentTokens = userData['tokens'] ?? 0;

        if (currentTokens < cost) {
          throw Exception('Insufficient tokens');
        }

        // Deduct tokens (CLIENT-SIDE - NOT SECURE)
        transaction.update(userRef, {
          'tokens': currentTokens - cost,
          'updatedAt': FieldValue.serverTimestamp(),
        });

        // Add item to owned items
        transaction.set(ownedItemRef, {
          'itemId': itemId,
          'itemType': itemType,
          'purchasedAt': FieldValue.serverTimestamp(),
          'cost': cost,
        });

        // Log purchase history
        final purchaseRef = userRef.collection('purchaseHistory').doc();
        transaction.set(purchaseRef, {
          'itemId': itemId,
          'itemType': itemType,
          'cost': cost,
          'purchasedAt': FieldValue.serverTimestamp(),
        });

        return {
          'success': true,
          'newTokenBalance': currentTokens - cost,
          'itemId': itemId,
          'itemType': itemType,
        };
      });
    } catch (e) {
      return {
        'success': false,
        'error': e.toString(),
      };
    }
  }

  /// Award tokens for game completion (CLIENT-SIDE - NOT SECURE)
  static Future<Map<String, dynamic>> awardTokens({
    required String gameId,
    required int score,
    required int tokensEarned,
  }) async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        return {'success': false, 'error': 'User not authenticated'};
      }

      final userRef = _firestore.collection('users').doc(user.uid);
      
      return await _firestore.runTransaction((transaction) async {
        final userDoc = await transaction.get(userRef);
        final userData = userDoc.data() ?? {};
        final currentTokens = userData['tokens'] ?? 0;

        // Update user tokens
        transaction.set(userRef, {
          'tokens': currentTokens + tokensEarned,
          'updatedAt': FieldValue.serverTimestamp(),
        }, SetOptions(merge: true));

        // Update leaderboard only if score is higher (this is secure due to Firestore rules)
        final leaderboardRef = _firestore
            .collection('leaderboards')
            .doc(gameId)
            .collection('scores')
            .doc(user.uid);

        final leaderboardDoc = await transaction.get(leaderboardRef);
        if (leaderboardDoc.exists) {
          final existingScore = leaderboardDoc.data()?['score'] ?? 0;
          // Only update if the new score is higher
          if (score > existingScore) {
            transaction.update(leaderboardRef, {
              'displayName': user.displayName ?? 'Anonymous',
              'score': score,
              'timestamp': FieldValue.serverTimestamp(),
            });
          }
        } else {
          // First time submitting score for this user
          transaction.set(leaderboardRef, {
            'uid': user.uid,
            'displayName': user.displayName ?? 'Anonymous',
            'score': score,
            'timestamp': FieldValue.serverTimestamp(),
          });
        }

        return {
          'success': true,
          'tokensEarned': tokensEarned,
          'newTokenBalance': currentTokens + tokensEarned,
          'gameId': gameId,
          'score': score,
        };
      });
    } catch (e) {
      return {
        'success': false,
        'error': e.toString(),
      };
    }
  }

  /// Get active boost
  static Future<Map<String, dynamic>?> getActiveBoost() async {
    try {
      final user = _auth.currentUser;
      if (user == null) return null;

      final snapshot = await _firestore
          .collection('users')
          .doc(user.uid)
          .collection('boosts')
          .doc('activeBoost')
          .get();

      if (snapshot.exists) {
        final data = snapshot.data() as Map<String, dynamic>;
        final expiresAt = data['expiresAt'] as Timestamp?;
        
        // Check if boost is still active
        if (expiresAt != null && expiresAt.toDate().isAfter(DateTime.now())) {
          return data;
        } else {
          // Boost expired, remove it
          await snapshot.reference.delete();
          return null;
        }
      }
      return null;
    } catch (e) {
      print('Error getting active boost: $e');
      return null;
    }
  }

  /// Activate a boost (CLIENT-SIDE - LIMITED SECURITY)
  static Future<Map<String, dynamic>> activateBoost({
    required String boostId,
    required int durationMinutes,
  }) async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        return {'success': false, 'error': 'User not authenticated'};
      }

      final activeBoostRef = _firestore
          .collection('users')
          .doc(user.uid)
          .collection('boosts')
          .doc('activeBoost');

      final expiresAt = DateTime.now().add(Duration(minutes: durationMinutes));

      await activeBoostRef.set({
        'boostId': boostId,
        'activatedAt': FieldValue.serverTimestamp(),
        'expiresAt': Timestamp.fromDate(expiresAt),
        'duration': durationMinutes,
      });

      return {
        'success': true,
        'boostId': boostId,
        'expiresAt': expiresAt.toIso8601String(),
      };
    } catch (e) {
      return {
        'success': false,
        'error': e.toString(),
      };
    }
  }
}
