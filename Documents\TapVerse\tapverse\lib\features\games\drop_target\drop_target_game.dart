import 'dart:math';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../../../core/models/game_state.dart';
import '../../../core/providers/game_providers.dart';
import '../../../core/services/game_feedback_service.dart';
import '../../../shared/widgets/base_game_widget.dart';

class DropTargetGame extends BaseGameWidget {
  static const GameConfig gameConfig = GameConfig(
    gameId: 'drop_target',
    gameName: 'DropTarget',
    scoreMultiplier: 2,
    hasLives: false,
    hasLevels: false,
    hasTimer: false,
  );

  const DropTargetGame({
    super.key,
    super.onGameComplete,
    super.onGameExit,
  }) : super(config: gameConfig);

  @override
  Widget buildGameContent(BuildContext context, WidgetRef ref, GameState gameState) {
    return const _DropTargetGameContent();
  }
}

class DroppedItem {
  Offset position;
  final double speed;
  bool hasLanded;
  bool inTarget;

  DroppedItem({
    required this.position,
    required this.speed,
    this.hasLanded = false,
    this.inTarget = false,
  });
}

class _DropTargetGameContent extends ConsumerStatefulWidget {
  const _DropTargetGameContent();

  @override
  ConsumerState<_DropTargetGameContent> createState() => _DropTargetGameContentState();
}

class _DropTargetGameContentState extends ConsumerState<_DropTargetGameContent>
    with TickerProviderStateMixin {
  
  // Game state
  late AnimationController _dropController;
  late AnimationController _hitController;
  late AnimationController _missController;
  
  // Drop mechanics
  final List<DroppedItem> _droppedItems = [];
  bool _canDrop = true;
  
  // Target state
  double _targetX = 200;
  double _targetDirection = 1;
  final double _targetY = 520;
  final double _targetWidth = 80;
  final double _targetHeight = 30;
  final double _targetSpeed = 100;
  
  // Game mechanics
  int _hits = 0;
  int _misses = 0;
  final int _maxMisses = 5;
  bool _showHitEffect = false;
  bool _showMissEffect = false;
  Offset? _effectPosition;
  
  // Constants
  static const double _gameWidth = 400;
  static const double _gameHeight = 600;
  static const double _itemSize = 20;
  static const double _dropHeight = 50;

  @override
  void initState() {
    super.initState();
    
    _dropController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );
    
    _hitController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );
    
    _missController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _startTargetMovement();
    _startGameLoop();
  }

  @override
  void dispose() {
    _dropController.dispose();
    _hitController.dispose();
    _missController.dispose();
    super.dispose();
  }

  void _startTargetMovement() {
    const frameRate = 60;
    const frameDuration = Duration(milliseconds: 1000 ~/ frameRate);
    const deltaTime = 1.0 / frameRate;
    
    void moveTarget() {
      if (!mounted) return;
      
      setState(() {
        _targetX += _targetDirection * _targetSpeed * deltaTime;
        
        // Bounce off walls
        if (_targetX <= _targetWidth / 2) {
          _targetX = _targetWidth / 2;
          _targetDirection = 1;
        } else if (_targetX >= _gameWidth - _targetWidth / 2) {
          _targetX = _gameWidth - _targetWidth / 2;
          _targetDirection = -1;
        }
      });
      
      Future.delayed(frameDuration, moveTarget);
    }
    
    moveTarget();
  }

  void _startGameLoop() {
    const frameRate = 60;
    const frameDuration = Duration(milliseconds: 1000 ~/ frameRate);
    const deltaTime = 1.0 / frameRate;
    
    void gameLoop() {
      if (!mounted) return;
      
      final gameState = ref.read(gameStateProvider(DropTargetGame.gameConfig));
      if (!gameState.isPlaying) {
        Future.delayed(frameDuration, gameLoop);
        return;
      }
      
      setState(() {
        // Update dropped items
        for (final item in _droppedItems) {
          if (!item.hasLanded) {
            item.position = Offset(
              item.position.dx,
              item.position.dy + item.speed * deltaTime,
            );
            
            // Check if item reached target level
            if (item.position.dy >= _targetY - _itemSize / 2) {
              item.hasLanded = true;
              _checkTargetHit(item);
            }
          }
        }
        
        // Remove old items
        _droppedItems.removeWhere((item) => 
          item.hasLanded && item.position.dy > _gameHeight + 50);
      });
      
      Future.delayed(frameDuration, gameLoop);
    }
    
    gameLoop();
  }

  void _checkTargetHit(DroppedItem item) {
    final itemLeft = item.position.dx - _itemSize / 2;
    final itemRight = item.position.dx + _itemSize / 2;
    final targetLeft = _targetX - _targetWidth / 2;
    final targetRight = _targetX + _targetWidth / 2;
    
    if (itemRight >= targetLeft && itemLeft <= targetRight) {
      // Hit!
      item.inTarget = true;
      _handleHit(item);
    } else {
      // Miss!
      _handleMiss(item);
    }
  }

  void _handleHit(DroppedItem item) {
    setState(() {
      _hits++;
      _showHitEffect = true;
      _effectPosition = item.position;
    });
    
    // Add score
    final gameNotifier = ref.read(gameStateProvider(DropTargetGame.gameConfig).notifier);
    gameNotifier.addScore(10);
    
    // Trigger feedback
    ref.read(gameFeedbackServiceProvider).triggerFeedback(GameEvent.scoreIncrease);
    
    // Play hit animation
    _hitController.forward().then((_) {
      _hitController.reset();
      setState(() {
        _showHitEffect = false;
      });
    });
  }

  void _handleMiss(DroppedItem item) {
    setState(() {
      _misses++;
      _showMissEffect = true;
      _effectPosition = item.position;
    });
    
    // Trigger feedback
    ref.read(gameFeedbackServiceProvider).triggerFeedback(GameEvent.obstacleHit);
    
    // Play miss animation
    _missController.forward().then((_) {
      _missController.reset();
      setState(() {
        _showMissEffect = false;
      });
    });
    
    // Check game over
    if (_misses >= _maxMisses) {
      final gameNotifier = ref.read(gameStateProvider(DropTargetGame.gameConfig).notifier);
      gameNotifier.endGame(reason: 'Too many misses');
    }
  }

  void _onTap(TapUpDetails details) {
    final gameState = ref.read(gameStateProvider(DropTargetGame.gameConfig));
    if (!gameState.isPlaying || !_canDrop) return;
    
    final tapX = details.localPosition.dx;
    
    // Drop item from tap position
    _droppedItems.add(DroppedItem(
      position: Offset(tapX, _dropHeight),
      speed: 200 + Random().nextDouble() * 100, // 200-300 speed
    ));
    
    setState(() {
      _canDrop = false;
    });
    
    // Trigger feedback
    ref.read(gameFeedbackServiceProvider).triggerFeedback(GameEvent.buttonPress);
    
    // Play drop animation
    _dropController.forward().then((_) {
      _dropController.reset();
      setState(() {
        _canDrop = true;
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    final gameState = ref.watch(gameStateProvider(DropTargetGame.gameConfig));
    
    return Container(
      width: _gameWidth,
      height: _gameHeight,
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [Color(0xFF87CEEB), Color(0xFF98FB98)],
        ),
      ),
      child: GestureDetector(
        onTapUp: _onTap,
        child: Stack(
          children: [
            // Background
            _buildBackground(),
            
            // Drop zone indicator
            _buildDropZone(),
            
            // Moving target
            _buildTarget(),
            
            // Dropped items
            ..._droppedItems.map((item) => _buildDroppedItem(item)),
            
            // Hit effect
            if (_showHitEffect && _effectPosition != null) _buildHitEffect(),
            
            // Miss effect
            if (_showMissEffect && _effectPosition != null) _buildMissEffect(),
            
            // Game info
            _buildGameInfo(gameState),
            
            // Instructions
            if (!gameState.isPlaying) _buildInstructions(),
          ],
        ),
      ),
    );
  }

  Widget _buildBackground() {
    return Positioned.fill(
      child: CustomPaint(
        painter: _BackgroundPainter(),
      ),
    );
  }

  Widget _buildDropZone() {
    return Positioned(
      top: _dropHeight - 20,
      left: 0,
      right: 0,
      child: Container(
        height: 40,
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.3),
          border: Border.all(color: Colors.white, width: 2),
        ),
        child: const Center(
          child: Text(
            'TAP TO DROP',
            style: TextStyle(
              color: Colors.white,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTarget() {
    return Positioned(
      left: _targetX - _targetWidth / 2,
      top: _targetY - _targetHeight / 2,
      child: Container(
        width: _targetWidth,
        height: _targetHeight,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [Colors.orange[400]!, Colors.red[600]!],
          ),
          borderRadius: BorderRadius.circular(15),
          border: Border.all(color: Colors.white, width: 2),
          boxShadow: [
            BoxShadow(
              color: Colors.orange.withOpacity(0.5),
              blurRadius: 8,
              spreadRadius: 2,
            ),
          ],
        ),
        child: const Center(
          child: Text(
            '🎯',
            style: TextStyle(fontSize: 20),
          ),
        ),
      ),
    );
  }

  Widget _buildDroppedItem(DroppedItem item) {
    return Positioned(
      left: item.position.dx - _itemSize / 2,
      top: item.position.dy - _itemSize / 2,
      child: Container(
        width: _itemSize,
        height: _itemSize,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          gradient: RadialGradient(
            colors: item.inTarget 
                ? [Colors.green[400]!, Colors.green[700]!]
                : [Colors.blue[400]!, Colors.blue[700]!],
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.3),
              blurRadius: 4,
              offset: const Offset(2, 2),
            ),
          ],
        ),
        child: const Center(
          child: Text(
            '💧',
            style: TextStyle(fontSize: 12),
          ),
        ),
      ),
    );
  }

  Widget _buildHitEffect() {
    return Positioned(
      left: _effectPosition!.dx - 30,
      top: _effectPosition!.dy - 30,
      child: ScaleTransition(
        scale: _hitController,
        child: Container(
          width: 60,
          height: 60,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            gradient: RadialGradient(
              colors: [Colors.green, Colors.lightGreen, Colors.transparent],
              stops: const [0.0, 0.5, 1.0],
            ),
          ),
          child: const Center(
            child: Text(
              'HIT!',
              style: TextStyle(
                color: Colors.white,
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildMissEffect() {
    return Positioned(
      left: _effectPosition!.dx - 30,
      top: _effectPosition!.dy - 30,
      child: ScaleTransition(
        scale: _missController,
        child: Container(
          width: 60,
          height: 60,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            gradient: RadialGradient(
              colors: [Colors.red, Colors.redAccent, Colors.transparent],
              stops: const [0.0, 0.5, 1.0],
            ),
          ),
          child: const Center(
            child: Text(
              'MISS',
              style: TextStyle(
                color: Colors.white,
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildGameInfo(GameState gameState) {
    return Positioned(
      top: 30,
      left: 20,
      right: 20,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.black54,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              children: [
                const Text(
                  'Hits',
                  style: TextStyle(color: Colors.white, fontSize: 12),
                ),
                Text(
                  '$_hits',
                  style: const TextStyle(
                    color: Colors.green,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.black54,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              children: [
                const Text(
                  'Score',
                  style: TextStyle(color: Colors.white, fontSize: 12),
                ),
                Text(
                  '${gameState.score}',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.black54,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              children: [
                const Text(
                  'Misses',
                  style: TextStyle(color: Colors.white, fontSize: 12),
                ),
                Text(
                  '$_misses/$_maxMisses',
                  style: TextStyle(
                    color: _misses >= _maxMisses - 1 ? Colors.red : Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInstructions() {
    return Positioned(
      bottom: 100,
      left: 0,
      right: 0,
      child: Center(
        child: Container(
          padding: EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.black54,
            borderRadius: BorderRadius.all(Radius.circular(12)),
          ),
          child: Text(
            'Tap anywhere to drop items!\nTry to land them in the moving target.\nDon\'t miss too many times!',
            textAlign: TextAlign.center,
            style: TextStyle(
              color: Colors.white,
              fontSize: 16,
            ),
          ),
        ),
      ),
    );
  }
}

class _BackgroundPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    // Draw clouds
    final cloudPaint = Paint()
      ..color = Colors.white.withOpacity(0.6)
      ..style = PaintingStyle.fill;
    
    canvas.drawCircle(Offset(80, 100), 20, cloudPaint);
    canvas.drawCircle(Offset(100, 100), 25, cloudPaint);
    canvas.drawCircle(Offset(120, 100), 20, cloudPaint);
    
    canvas.drawCircle(Offset(300, 150), 15, cloudPaint);
    canvas.drawCircle(Offset(315, 150), 20, cloudPaint);
    canvas.drawCircle(Offset(330, 150), 15, cloudPaint);
    
    // Draw trajectory guides
    final guidePaint = Paint()
      ..color = Colors.white.withOpacity(0.2)
      ..strokeWidth = 1
      ..style = PaintingStyle.stroke;
    
    for (double x = 50; x < size.width; x += 50) {
      canvas.drawLine(
        Offset(x, 50),
        Offset(x, size.height - 100),
        guidePaint,
      );
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
