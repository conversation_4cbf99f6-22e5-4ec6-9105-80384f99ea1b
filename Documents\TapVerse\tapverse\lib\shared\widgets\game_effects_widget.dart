import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../../core/providers/app_providers.dart';
import '../../core/services/animation_service.dart';
import '../../core/services/vibration_service.dart';

/// Widget for displaying combined game effects (animation + sound + vibration)
class GameEffectsWidget extends ConsumerWidget {
  final Widget child;
  final bool enabled;

  const GameEffectsWidget({
    super.key,
    required this.child,
    this.enabled = true,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Stack(
      children: [
        child,
        if (enabled) const Positioned.fill(child: _EffectsOverlay()),
      ],
    );
  }
}

class _EffectsOverlay extends ConsumerWidget {
  const _EffectsOverlay();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return const SizedBox.shrink();
  }
}

/// Score effect that combines animation, sound, and vibration
class ScoreEffectWidget extends ConsumerStatefulWidget {
  final int score;
  final Offset position;
  final VoidCallback? onComplete;
  final bool isCombo;
  final int comboMultiplier;

  const ScoreEffectWidget({
    super.key,
    required this.score,
    required this.position,
    this.onComplete,
    this.isCombo = false,
    this.comboMultiplier = 1,
  });

  @override
  ConsumerState<ScoreEffectWidget> createState() => _ScoreEffectWidgetState();
}

class _ScoreEffectWidgetState extends ConsumerState<ScoreEffectWidget>
    with TickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;
  late Animation<double> _opacityAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    
    _controller = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 0.5,
      end: widget.isCombo ? 1.5 : 1.2,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: const Interval(0.0, 0.3, curve: Curves.elasticOut),
    ));

    _opacityAnimation = Tween<double>(
      begin: 1.0,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: const Interval(0.7, 1.0, curve: Curves.easeOut),
    ));

    _slideAnimation = Tween<Offset>(
      begin: Offset.zero,
      end: const Offset(0, -2),
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeOut,
    ));

    _triggerEffects();
    _controller.forward().then((_) {
      widget.onComplete?.call();
    });
  }

  void _triggerEffects() {
    final soundService = ref.read(soundServiceProvider);
    final vibrationService = ref.read(vibrationServiceProvider);
    final animationService = ref.read(animationServiceProvider);

    // Play sound based on score type
    if (widget.isCombo) {
      soundService.playChime();
      vibrationService.onScore();
    } else {
      soundService.playScore();
      vibrationService.vibrate(VibrationType.success);
    }

    // Show particle animation
    if (widget.isCombo) {
      animationService.showAnimationOverlay(
        context,
        AnimationType.particleBurst,
        position: widget.position,
        width: 150,
        height: 150,
      );
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Positioned(
      left: widget.position.dx,
      top: widget.position.dy,
      child: AnimatedBuilder(
        animation: _controller,
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: Transform.translate(
              offset: _slideAnimation.value * 50,
              child: Opacity(
                opacity: _opacityAnimation.value,
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: widget.isCombo ? Colors.purple : Colors.green,
                    borderRadius: BorderRadius.circular(20),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.3),
                        blurRadius: 8,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: Text(
                    widget.isCombo 
                        ? '+${widget.score} x${widget.comboMultiplier}'
                        : '+${widget.score}',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}

/// Explosion effect for collisions or destructions
class ExplosionEffectWidget extends ConsumerStatefulWidget {
  final Offset position;
  final Color color;
  final double size;
  final VoidCallback? onComplete;

  const ExplosionEffectWidget({
    super.key,
    required this.position,
    this.color = Colors.orange,
    this.size = 100,
    this.onComplete,
  });

  @override
  ConsumerState<ExplosionEffectWidget> createState() => _ExplosionEffectWidgetState();
}

class _ExplosionEffectWidgetState extends ConsumerState<ExplosionEffectWidget>
    with TickerProviderStateMixin {
  late AnimationController _controller;

  @override
  void initState() {
    super.initState();
    
    _controller = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _triggerEffects();
    _controller.forward().then((_) {
      widget.onComplete?.call();
    });
  }

  void _triggerEffects() {
    final soundService = ref.read(soundServiceProvider);
    final vibrationService = ref.read(vibrationServiceProvider);
    final animationService = ref.read(animationServiceProvider);

    // Play explosion sound and vibration
    soundService.playExplosion();
    vibrationService.vibrate(VibrationType.heavy);

    // Show explosion animation
    animationService.showAnimationOverlay(
      context,
      AnimationType.explosion,
      position: widget.position,
      width: widget.size,
      height: widget.size,
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Positioned(
      left: widget.position.dx - widget.size / 2,
      top: widget.position.dy - widget.size / 2,
      child: AnimatedBuilder(
        animation: _controller,
        builder: (context, child) {
          return Container(
            width: widget.size,
            height: widget.size,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: widget.color.withOpacity(1 - _controller.value),
            ),
          ).animate()
            .scale(
              begin: const Offset(0.1, 0.1),
              end: const Offset(2.0, 2.0),
              duration: const Duration(milliseconds: 400),
            )
            .fadeOut(
              begin: 0.8,
              duration: const Duration(milliseconds: 400),
            );
        },
      ),
    );
  }
}

/// Power-up collection effect
class PowerUpEffectWidget extends ConsumerStatefulWidget {
  final Offset position;
  final String powerUpType;
  final VoidCallback? onComplete;

  const PowerUpEffectWidget({
    super.key,
    required this.position,
    required this.powerUpType,
    this.onComplete,
  });

  @override
  ConsumerState<PowerUpEffectWidget> createState() => _PowerUpEffectWidgetState();
}

class _PowerUpEffectWidgetState extends ConsumerState<PowerUpEffectWidget>
    with TickerProviderStateMixin {
  late AnimationController _controller;

  @override
  void initState() {
    super.initState();
    
    _controller = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _triggerEffects();
    _controller.forward().then((_) {
      widget.onComplete?.call();
    });
  }

  void _triggerEffects() {
    final soundService = ref.read(soundServiceProvider);
    final vibrationService = ref.read(vibrationServiceProvider);
    final animationService = ref.read(animationServiceProvider);

    // Play power-up sound and vibration
    soundService.playPowerUp();
    vibrationService.onPowerUp();

    // Show power-up animation
    animationService.showAnimationOverlay(
      context,
      AnimationType.powerUpCollect,
      position: widget.position,
      width: 120,
      height: 120,
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Positioned(
      left: widget.position.dx,
      top: widget.position.dy,
      child: Container(
        width: 60,
        height: 60,
        decoration: const BoxDecoration(
          shape: BoxShape.circle,
          gradient: RadialGradient(
            colors: [Colors.yellow, Colors.orange],
          ),
        ),
        child: const Icon(
          Icons.star,
          color: Colors.white,
          size: 30,
        ),
      ).animate()
        .scale(
          begin: const Offset(1.0, 1.0),
          end: const Offset(1.5, 1.5),
          duration: const Duration(milliseconds: 300),
        )
        .then()
        .fadeOut(duration: const Duration(milliseconds: 700)),
    );
  }
}
