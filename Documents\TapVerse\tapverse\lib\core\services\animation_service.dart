import 'package:flutter/material.dart';
import 'package:lottie/lottie.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Animation types for different game events
enum AnimationType {
  // Score and achievement animations
  scoreIncrease,
  perfectScore,
  comboMultiplier,
  newHighScore,
  levelUp,
  achievement,
  
  // Action animations
  explosion,
  particleBurst,
  sparkles,
  confetti,
  fireworks,
  
  // Game-specific animations
  basketballSwish,
  soccerGoal,
  ballBounce,
  balloonPop,
  coinCollect,
  powerUpCollect,
  
  // UI animations
  buttonPress,
  pageTransition,
  loading,
  success,
  error,
  warning,
  
  // Environmental animations
  stars,
  hearts,
  lightning,
  smoke,
  water,
  fire,
}

/// Animation configuration
class AnimationConfig {
  final String assetPath;
  final Duration duration;
  final bool repeat;
  final double scale;
  final Alignment alignment;
  final bool enabled;

  const AnimationConfig({
    required this.assetPath,
    this.duration = const Duration(seconds: 2),
    this.repeat = false,
    this.scale = 1.0,
    this.alignment = Alignment.center,
    this.enabled = true,
  });

  AnimationConfig copyWith({
    String? assetPath,
    Duration? duration,
    bool? repeat,
    double? scale,
    Alignment? alignment,
    bool? enabled,
  }) {
    return AnimationConfig(
      assetPath: assetPath ?? this.assetPath,
      duration: duration ?? this.duration,
      repeat: repeat ?? this.repeat,
      scale: scale ?? this.scale,
      alignment: alignment ?? this.alignment,
      enabled: enabled ?? this.enabled,
    );
  }
}

/// Service for managing Lottie animations
class AnimationService {
  static final AnimationService _instance = AnimationService._internal();
  factory AnimationService() => _instance;
  AnimationService._internal();

  // Default animation configurations
  static const Map<AnimationType, AnimationConfig> _defaultConfigs = {
    AnimationType.scoreIncrease: AnimationConfig(
      assetPath: 'assets/animations/score_increase.json',
      duration: Duration(milliseconds: 800),
    ),
    AnimationType.perfectScore: AnimationConfig(
      assetPath: 'assets/animations/perfect_score.json',
      duration: Duration(milliseconds: 1200),
    ),
    AnimationType.comboMultiplier: AnimationConfig(
      assetPath: 'assets/animations/combo_multiplier.json',
      duration: Duration(milliseconds: 600),
    ),
    AnimationType.newHighScore: AnimationConfig(
      assetPath: 'assets/animations/new_high_score.json',
      duration: Duration(milliseconds: 2000),
    ),
    AnimationType.levelUp: AnimationConfig(
      assetPath: 'assets/animations/level_up.json',
      duration: Duration(milliseconds: 1500),
    ),
    AnimationType.achievement: AnimationConfig(
      assetPath: 'assets/animations/achievement.json',
      duration: Duration(milliseconds: 1800),
    ),
    AnimationType.explosion: AnimationConfig(
      assetPath: 'assets/animations/explosion.json',
      duration: Duration(milliseconds: 1000),
    ),
    AnimationType.particleBurst: AnimationConfig(
      assetPath: 'assets/animations/particle_burst.json',
      duration: Duration(milliseconds: 800),
    ),
    AnimationType.sparkles: AnimationConfig(
      assetPath: 'assets/animations/sparkles.json',
      duration: Duration(milliseconds: 1200),
    ),
    AnimationType.confetti: AnimationConfig(
      assetPath: 'assets/animations/confetti.json',
      duration: Duration(milliseconds: 2000),
    ),
    AnimationType.fireworks: AnimationConfig(
      assetPath: 'assets/animations/fireworks.json',
      duration: Duration(milliseconds: 2500),
    ),
    AnimationType.basketballSwish: AnimationConfig(
      assetPath: 'assets/animations/basketball_swish.json',
      duration: Duration(milliseconds: 1000),
    ),
    AnimationType.soccerGoal: AnimationConfig(
      assetPath: 'assets/animations/soccer_goal.json',
      duration: Duration(milliseconds: 1200),
    ),
    AnimationType.ballBounce: AnimationConfig(
      assetPath: 'assets/animations/ball_bounce.json',
      duration: Duration(milliseconds: 600),
    ),
    AnimationType.balloonPop: AnimationConfig(
      assetPath: 'assets/animations/balloon_pop.json',
      duration: Duration(milliseconds: 400),
    ),
    AnimationType.coinCollect: AnimationConfig(
      assetPath: 'assets/animations/coin_collect.json',
      duration: Duration(milliseconds: 800),
    ),
    AnimationType.powerUpCollect: AnimationConfig(
      assetPath: 'assets/animations/power_up_collect.json',
      duration: Duration(milliseconds: 1000),
    ),
    AnimationType.buttonPress: AnimationConfig(
      assetPath: 'assets/animations/button_press.json',
      duration: Duration(milliseconds: 200),
    ),
    AnimationType.pageTransition: AnimationConfig(
      assetPath: 'assets/animations/page_transition.json',
      duration: Duration(milliseconds: 500),
    ),
    AnimationType.loading: AnimationConfig(
      assetPath: 'assets/animations/loading.json',
      duration: Duration(milliseconds: 1000),
      repeat: true,
    ),
    AnimationType.success: AnimationConfig(
      assetPath: 'assets/animations/success.json',
      duration: Duration(milliseconds: 1000),
    ),
    AnimationType.error: AnimationConfig(
      assetPath: 'assets/animations/error.json',
      duration: Duration(milliseconds: 800),
    ),
    AnimationType.warning: AnimationConfig(
      assetPath: 'assets/animations/warning.json',
      duration: Duration(milliseconds: 600),
    ),
    AnimationType.stars: AnimationConfig(
      assetPath: 'assets/animations/stars.json',
      duration: Duration(milliseconds: 1500),
    ),
    AnimationType.hearts: AnimationConfig(
      assetPath: 'assets/animations/hearts.json',
      duration: Duration(milliseconds: 1200),
    ),
    AnimationType.lightning: AnimationConfig(
      assetPath: 'assets/animations/lightning.json',
      duration: Duration(milliseconds: 400),
    ),
    AnimationType.smoke: AnimationConfig(
      assetPath: 'assets/animations/smoke.json',
      duration: Duration(milliseconds: 2000),
    ),
    AnimationType.water: AnimationConfig(
      assetPath: 'assets/animations/water.json',
      duration: Duration(milliseconds: 1500),
    ),
    AnimationType.fire: AnimationConfig(
      assetPath: 'assets/animations/fire.json',
      duration: Duration(milliseconds: 1800),
    ),
  };

  final Map<AnimationType, AnimationConfig> _customConfigs = {};
  bool _animationsEnabled = true;

  bool get animationsEnabled => _animationsEnabled;

  Future<void> initialize() async {
    await _loadSettings();
  }

  Future<void> _loadSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      _animationsEnabled = prefs.getBool('animations_enabled') ?? true;
    } catch (e) {
      // Handle error silently
    }
  }

  Future<void> _saveSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('animations_enabled', _animationsEnabled);
    } catch (e) {
      // Handle error silently
    }
  }

  /// Create an animation widget
  Widget createAnimation(
    AnimationType type, {
    AnimationConfig? customConfig,
    VoidCallback? onComplete,
    double? width,
    double? height,
    BoxFit? fit,
  }) {
    if (!_animationsEnabled) {
      return const SizedBox.shrink();
    }

    final config = customConfig ?? _customConfigs[type] ?? _defaultConfigs[type];
    if (config == null || !config.enabled) {
      return const SizedBox.shrink();
    }

    return Transform.scale(
      scale: config.scale,
      child: Align(
        alignment: config.alignment,
        child: Lottie.asset(
          config.assetPath,
          width: width,
          height: height,
          fit: fit ?? BoxFit.contain,
          repeat: config.repeat,
          onLoaded: (composition) {
            if (onComplete != null && !config.repeat) {
              Future.delayed(config.duration, onComplete);
            }
          },
        ),
      ),
    );
  }

  /// Show animation overlay
  void showAnimationOverlay(
    BuildContext context,
    AnimationType type, {
    AnimationConfig? customConfig,
    VoidCallback? onComplete,
    Offset? position,
    double? width,
    double? height,
  }) {
    if (!_animationsEnabled) {
      onComplete?.call();
      return;
    }

    final config = customConfig ?? _customConfigs[type] ?? _defaultConfigs[type];
    if (config == null || !config.enabled) {
      onComplete?.call();
      return;
    }

    final overlay = Overlay.of(context);
    late OverlayEntry overlayEntry;

    overlayEntry = OverlayEntry(
      builder: (context) => Positioned(
        left: position?.dx ?? (MediaQuery.of(context).size.width / 2 - (width ?? 100) / 2),
        top: position?.dy ?? (MediaQuery.of(context).size.height / 2 - (height ?? 100) / 2),
        child: IgnorePointer(
          child: createAnimation(
            type,
            customConfig: customConfig,
            width: width ?? 100,
            height: height ?? 100,
            onComplete: () {
              overlayEntry.remove();
              onComplete?.call();
            },
          ),
        ),
      ),
    );

    overlay.insert(overlayEntry);

    // Auto-remove after duration if not repeating
    if (!config.repeat) {
      Future.delayed(config.duration + const Duration(milliseconds: 100), () {
        if (overlayEntry.mounted) {
          overlayEntry.remove();
        }
      });
    }
  }

  /// Set custom animation configuration
  void setAnimationConfig(AnimationType type, AnimationConfig config) {
    _customConfigs[type] = config;
  }

  /// Get animation configuration
  AnimationConfig getAnimationConfig(AnimationType type) {
    return _customConfigs[type] ?? _defaultConfigs[type] ?? 
           const AnimationConfig(assetPath: '', enabled: false);
  }

  /// Enable/disable animations
  Future<void> setAnimationsEnabled(bool enabled) async {
    _animationsEnabled = enabled;
    await _saveSettings();
  }

  /// Reset all custom configurations
  void resetConfigurations() {
    _customConfigs.clear();
  }

  /// Disable animation for a specific type
  void disableAnimation(AnimationType type) {
    _customConfigs[type] = const AnimationConfig(assetPath: '', enabled: false);
  }

  /// Enable animation for a specific type
  void enableAnimation(AnimationType type) {
    _customConfigs[type] = _defaultConfigs[type] ?? 
                          const AnimationConfig(assetPath: '');
  }
}
