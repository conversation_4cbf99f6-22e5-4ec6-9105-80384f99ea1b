import 'dart:math' as math;
import 'package:flame/components.dart';
import 'package:flame/collisions.dart';
import 'package:flutter/material.dart';
import '../../core/mini_game_base.dart';
import '../../core/hud.dart';
import '../../core/effects.dart';
import '../../core/analytics.dart';
import '../../core/difficulty.dart';

/// RocketRider - Thrust-based space navigation
/// Controls: Hold to thrust upward; release for gravity; steer with tilt/swipe
/// Scoring: +1 per fuel pickup; +10 per asteroid field cleared; +20 per perfect fuel efficiency
class RocketRiderGame extends MiniGameBase {
  RocketRiderGame(int seed) : super(modeId: 'rocket_rider', sessionSeed: seed) {
    rng = math.Random(seed);
  }

  late math.Random rng;
  late ScoreText scoreText;
  late ProgressBar fuelBar;
  
  // Game state
  int score = 0;
  int asteroidFieldsCleared = 0;
  
  // Game entities
  late Rocket rocket;
  late List<Asteroid> asteroids;
  late List<FuelPickup> fuelPickups;
  
  // Difficulty curves
  final asteroidDensityCurve = const CurveParam(start: 0.3, max: 0.7, perMinute: 0.2);
  final asteroidSpeedCurve = const CurveParam(start: 100, max: 250, perMinute: 75);
  
  late DifficultyManager difficultyManager;
  
  // Level generation
  double nextAsteroidY = -200;
  double nextFuelY = -400;

  @override
  Future<void> loadAssets() async {}

  @override
  void setupWorld() {
    camera.viewfinder.visibleGameSize = Vector2(720, 1280);
    
    difficultyManager = DifficultyManager();
    difficultyManager.addParameter('asteroid_density', asteroidDensityCurve);
    difficultyManager.addParameter('asteroid_speed', asteroidSpeedCurve);
    
    asteroids = [];
    fuelPickups = [];
    
    _createGameEntities();
  }

  void _createGameEntities() {
    // Create rocket
    rocket = Rocket(
      position: Vector2(360, 1000),
      onAsteroidHit: _onAsteroidHit,
      onFuelPickup: _onFuelPickup,
      onFuelEmpty: _onFuelEmpty,
      onFuelPickupRemove: (pickup) => fuelPickups.remove(pickup),
    );
    add(rocket);

    // Generate initial level
    _generateInitialLevel();
  }

  void _generateInitialLevel() {
    for (int i = 0; i < 10; i++) {
      _generateAsteroidField();
      _generateFuelPickup();
    }
  }

  void _generateAsteroidField() {
    final density = difficultyManager.getValue('asteroid_density');
    final asteroidCount = (5 * density).round();
    
    for (int i = 0; i < asteroidCount; i++) {
      final asteroid = Asteroid(
        position: Vector2(
          50 + rng.nextDouble() * 620,
          nextAsteroidY - i * 80,
        ),
        size: 30 + rng.nextDouble() * 40,
        speed: difficultyManager.getValue('asteroid_speed'),
      );
      
      asteroids.add(asteroid);
      add(asteroid);
    }
    
    nextAsteroidY -= 400;
  }

  void _generateFuelPickup() {
    if (rng.nextDouble() < 0.4) {
      final fuel = FuelPickup(
        position: Vector2(
          100 + rng.nextDouble() * 520,
          nextFuelY,
        ),
      );
      
      fuelPickups.add(fuel);
      add(fuel);
    }
    
    nextFuelY -= 300;
  }

  @override
  void setupHUD() {
    scoreText = ScoreText(position: Vector2(16, 16));
    add(scoreText);

    fuelBar = ProgressBar(
      position: Vector2(16, 50),
      size: Vector2(200, 20),
      backgroundColor: Colors.red,
      foregroundColor: Colors.green,
    );
    add(fuelBar);
  }

  @override
  void onStart() {
    score = 0;
    asteroidFieldsCleared = 0;
    
    difficultyManager.start();
    startGame();
    
    Analytics.log('rocket_rider_start', {'session_seed': sessionSeed});
  }

  @override
  void onGameUpdate(double dt) {
    // Update fuel bar
    fuelBar.updateProgress(rocket.fuel / 100.0);
    
    // Generate new content
    if (rocket.position.y < nextAsteroidY + 1000) {
      _generateAsteroidField();
    }
    
    if (rocket.position.y < nextFuelY + 800) {
      _generateFuelPickup();
    }
    
    // Clean up old entities
    _cleanupOldEntities();
    
    // Update camera
    camera.viewfinder.position = Vector2(360, rocket.position.y + 400);
  }

  void _cleanupOldEntities() {
    asteroids.removeWhere((asteroid) {
      if (asteroid.position.y > rocket.position.y + 800) {
        asteroid.removeFromParent();
        return true;
      }
      return false;
    });
    
    fuelPickups.removeWhere((fuel) {
      if (fuel.position.y > rocket.position.y + 800) {
        fuel.removeFromParent();
        return true;
      }
      return false;
    });
  }

  // TODO: Implement input handling
  void handlePanStart(Vector2 position) {
    rocket.startThrust();
  }

  void handlePanEnd(Vector2 position) {
    rocket.stopThrust();
  }

  void handlePanUpdate(Vector2 delta) {
    rocket.steer(delta.x * 3);
  }

  void _onAsteroidHit() {
    endGame(success: false, score: score);
    
    Analytics.log('asteroid_hit', {
      'final_score': score,
      'fields_cleared': asteroidFieldsCleared,
    });
  }

  void _onFuelPickup() {
    score += 1;
    addScore(1);
    scoreText.updateScore(score);
    
    add(Effects.sparkle(rocket.position, colors: [Colors.blue, Colors.cyan]));
    
    Analytics.log('fuel_pickup', {'score': score});
  }

  void _onFuelEmpty() {
    endGame(success: false, score: score);
    
    Analytics.log('fuel_empty', {
      'final_score': score,
      'fields_cleared': asteroidFieldsCleared,
    });
  }


  @override
  void reportScore(int score) {}

  @override
  void awardTokens(int tokens) {}
}

class Rocket extends RectangleComponent with HasCollisionDetection, CollisionCallbacks {
  Rocket({
    required super.position,
    required this.onAsteroidHit,
    required this.onFuelPickup,
    required this.onFuelEmpty,
    required this.onFuelPickupRemove,
  }) : super(size: Vector2(30, 50));

  final VoidCallback onAsteroidHit;
  final VoidCallback onFuelPickup;
  final VoidCallback onFuelEmpty;
  final Function(FuelPickup) onFuelPickupRemove;

  Vector2 velocity = Vector2.zero();
  double fuel = 100.0;
  bool isThrusting = false;
  
  static const double thrustForce = 800;
  static const double gravity = 400;
  static const double fuelConsumption = 20; // per second
  static const double maxSpeed = 300;

  @override
  Future<void> onLoad() async {
    add(RectangleHitbox());
    paint = Paint()..color = Colors.orange;
  }

  @override
  void update(double dt) {
    super.update(dt);
    
    // Apply thrust or gravity
    if (isThrusting && fuel > 0) {
      velocity.y -= thrustForce * dt;
      fuel -= fuelConsumption * dt;
      
      if (fuel <= 0) {
        fuel = 0;
        onFuelEmpty();
      }
    } else {
      velocity.y += gravity * dt;
    }
    
    // Limit speed
    velocity.y = velocity.y.clamp(-maxSpeed, maxSpeed);
    velocity.x = velocity.x.clamp(-maxSpeed, maxSpeed);
    
    // Apply drag
    velocity.x *= 0.95;
    
    // Update position
    position += velocity * dt;
    
    // Keep within screen bounds
    position.x = position.x.clamp(0, 720 - size.x);
  }

  void startThrust() {
    isThrusting = true;
  }

  void stopThrust() {
    isThrusting = false;
  }

  void steer(double force) {
    velocity.x += force;
  }

  void addFuel(double amount) {
    fuel = (fuel + amount).clamp(0, 100);
  }

  @override
  void render(Canvas canvas) {
    super.render(canvas);
    
    // Draw thrust effect
    if (isThrusting && fuel > 0) {
      final thrustPaint = Paint()..color = Colors.yellow;
      canvas.drawRect(
        Rect.fromLTWH(size.x / 4, size.y, size.x / 2, 20),
        thrustPaint,
      );
    }
  }

  @override
  bool onCollisionStart(Set<Vector2> intersectionPoints, PositionComponent other) {
    if (other is Asteroid) {
      onAsteroidHit();
      return true;
    } else if (other is FuelPickup) {
      addFuel(25);
      onFuelPickup();
      onFuelPickupRemove(other);
      other.removeFromParent();
      return true;
    }
    return false;
  }
}

class Asteroid extends CircleComponent with HasCollisionDetection {
  Asteroid({
    required super.position,
    required double size,
    required this.speed,
  }) : super(radius: size / 2);

  final double speed;
  double rotationSpeed = 0;

  @override
  Future<void> onLoad() async {
    add(CircleHitbox());
    paint = Paint()..color = Colors.grey;
    rotationSpeed = (math.Random().nextDouble() - 0.5) * 4;
  }

  @override
  void update(double dt) {
    super.update(dt);
    position.y += speed * dt;
    angle += rotationSpeed * dt;
  }
}

class FuelPickup extends CircleComponent with HasCollisionDetection {
  FuelPickup({required super.position}) : super(radius: 15);

  double pulseTimer = 0;

  @override
  Future<void> onLoad() async {
    add(CircleHitbox());
    paint = Paint()..color = Colors.blue;
  }

  @override
  void update(double dt) {
    super.update(dt);
    
    // Pulsing effect
    pulseTimer += dt * 3;
    final pulse = (math.sin(pulseTimer) + 1) / 2;
    paint = Paint()..color = Colors.blue.withOpacity(0.5 + pulse * 0.5);
  }
}
