import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/providers/app_providers.dart';
import '../../../core/services/settings_service.dart';
import 'game_card.dart';

class GamesPopupDialog extends ConsumerWidget {
  const GamesPopupDialog({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final gameNavigation = ref.watch(gameNavigationServiceProvider);
    final gridLayoutNotifier = ref.watch(gridLayoutProvider.notifier);
    final allGames = gameNavigation.allGames;
    final currentLayout = ref.watch(gridLayoutProvider);

    return Dialog.fullscreen(
      child: Scaffold(
        appBar: AppBar(
          title: const Text('All Games'),
          centerTitle: true,
          backgroundColor: Theme.of(context).colorScheme.primaryContainer,
          leading: IconButton(
            icon: const Icon(Icons.close),
            onPressed: () => Navigator.of(context).pop(),
          ),
          actions: [
            // Grid layout selector
            PopupMenuButton<GridLayout>(
              icon: Icon(_getLayoutIcon(currentLayout)),
              tooltip: 'Change grid layout',
              onSelected: (GridLayout layout) {
                gridLayoutNotifier.setLayout(layout);
              },
              itemBuilder: (context) => [
                PopupMenuItem(
                  value: GridLayout.compact,
                  child: Row(
                    children: [
                      Icon(_getLayoutIcon(GridLayout.compact)),
                      const SizedBox(width: 8),
                      const Text('Compact (3 columns)'),
                      if (currentLayout == GridLayout.compact)
                        const Padding(
                          padding: EdgeInsets.only(left: 8),
                          child: Icon(Icons.check, size: 16),
                        ),
                    ],
                  ),
                ),
                PopupMenuItem(
                  value: GridLayout.normal,
                  child: Row(
                    children: [
                      Icon(_getLayoutIcon(GridLayout.normal)),
                      const SizedBox(width: 8),
                      const Text('Normal (2 columns)'),
                      if (currentLayout == GridLayout.normal)
                        const Padding(
                          padding: EdgeInsets.only(left: 8),
                          child: Icon(Icons.check, size: 16),
                        ),
                    ],
                  ),
                ),
                PopupMenuItem(
                  value: GridLayout.large,
                  child: Row(
                    children: [
                      Icon(_getLayoutIcon(GridLayout.large)),
                      const SizedBox(width: 8),
                      const Text('Large (1 column)'),
                      if (currentLayout == GridLayout.large)
                        const Padding(
                          padding: EdgeInsets.only(left: 8),
                          child: Icon(Icons.check, size: 16),
                        ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
        body: allGames.isEmpty
            ? const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.games_outlined,
                      size: 64,
                      color: Colors.grey,
                    ),
                    SizedBox(height: 16),
                    Text(
                      'No games available',
                      style: TextStyle(
                        fontSize: 18,
                        color: Colors.grey,
                      ),
                    ),
                  ],
                ),
              )
            : Padding(
                padding: const EdgeInsets.all(16),
                child: GridView.builder(
                  gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: _getCrossAxisCount(currentLayout),
                    childAspectRatio: _getChildAspectRatio(currentLayout),
                    crossAxisSpacing: 16,
                    mainAxisSpacing: 16,
                  ),
                  itemCount: allGames.length,
                  itemBuilder: (context, index) {
                    final game = allGames[index];
                    return GameCard(
                      gameInfo: game,
                      layout: _getGameCardLayout(currentLayout),
                      onTap: () async {
                        Navigator.of(context).pop();
                        await gameNavigation.navigateToGameWithPaywall(context, game.type, ref: ref);
                      },
                      onPinToggle: () => _showPinDialog(context, ref, game),
                    );
                  },
                ),
              ),
      ),
    );
  }

  void _showPinDialog(BuildContext context, WidgetRef ref, game) async {
    final pinnedGamesService = ref.read(pinnedGamesServiceProvider);
    final isPinned = await pinnedGamesService.isGamePinned(game.id);

    if (!context.mounted) return;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(isPinned ? 'Unpin Game' : 'Pin Game'),
        content: Text(
          isPinned
              ? 'Remove "${game.name}" from pinned games?'
              : 'Pin "${game.name}" to the main menu for quick access?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              if (isPinned) {
                await pinnedGamesService.unpinGame(game.id);
              } else {
                await pinnedGamesService.pinGame(game.id);
              }

              if (context.mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(
                      isPinned
                          ? '${game.name} unpinned'
                          : '${game.name} pinned to main menu',
                    ),
                  ),
                );
              }
            },
            child: Text(isPinned ? 'Unpin' : 'Pin'),
          ),
        ],
      ),
    );
  }

  // Helper methods for grid layout
  IconData _getLayoutIcon(GridLayout layout) {
    switch (layout) {
      case GridLayout.compact:
        return Icons.view_module;
      case GridLayout.normal:
        return Icons.view_column;
      case GridLayout.large:
        return Icons.view_agenda;
    }
  }

  int _getCrossAxisCount(GridLayout layout) {
    switch (layout) {
      case GridLayout.compact:
        return 3;
      case GridLayout.normal:
        return 2;
      case GridLayout.large:
        return 1;
    }
  }

  double _getChildAspectRatio(GridLayout layout) {
    switch (layout) {
      case GridLayout.compact:
        return 0.8; // Taller for compact view with title below
      case GridLayout.normal:
        return 0.8; // Standard ratio
      case GridLayout.large:
        return 2.5; // Wide cards for single column
    }
  }

  GameCardLayout _getGameCardLayout(GridLayout layout) {
    switch (layout) {
      case GridLayout.compact:
        return GameCardLayout.small; // Logo with title underneath
      case GridLayout.normal:
        return GameCardLayout.medium; // Standard card
      case GridLayout.large:
        return GameCardLayout.large; // Full detailed card
    }
  }
}
