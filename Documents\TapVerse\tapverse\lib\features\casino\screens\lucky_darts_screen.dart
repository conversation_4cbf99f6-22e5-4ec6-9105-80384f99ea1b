import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'dart:math' as math;
import '../../../core/providers/app_providers.dart';
import '../../../core/models/casino_models.dart';


class LuckyDartsScreen extends ConsumerStatefulWidget {
  const LuckyDartsScreen({super.key});

  @override
  ConsumerState<LuckyDartsScreen> createState() => _LuckyDartsScreenState();
}

class _LuckyDartsScreenState extends ConsumerState<LuckyDartsScreen>
    with TickerProviderStateMixin {
  static const int entryCost = 40;
  
  bool _isPlaying = false;
  bool _canPlay = true;
  DartResult? _lastResult;
  
  late AnimationController _dartController;
  late AnimationController _resultController;

  // Dart zone probabilities and rewards according to specifications
  static const Map<DartZone, double> zoneProbabilities = {
    DartZone.bullseye: 0.02,   // 2%
    DartZone.inner: 0.08,      // 8%
    DartZone.middle: 0.10,     // 10%
    DartZone.outer: 0.25,      // 25%
    DartZone.miss: 0.55,       // 55%
  };

  static const Map<DartZone, int> zoneRewards = {
    DartZone.bullseye: 300,
    DartZone.inner: 150,
    DartZone.middle: 100,
    DartZone.outer: 50,
    DartZone.miss: 0,
  };

  @override
  void initState() {
    super.initState();
    _dartController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    _resultController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    _checkCanPlay();
  }

  @override
  void dispose() {
    _dartController.dispose();
    _resultController.dispose();
    super.dispose();
  }

  Future<void> _checkCanPlay() async {
    final casinoService = ref.read(casinoServiceProvider);
    final canPlay = await casinoService.canPlayGame(entryCost);
    if (mounted) {
      setState(() {
        _canPlay = canPlay;
      });
    }
  }

  Future<void> _throwDart() async {
    if (_isPlaying || !_canPlay) return;

    final casinoService = ref.read(casinoServiceProvider);
    final soundService = ref.read(soundServiceProvider);
    final vibrationService = ref.read(vibrationServiceProvider);

    // Check if user can afford to play
    final canPlay = await casinoService.canPlayGame(entryCost);
    if (!canPlay) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Insufficient tokens to play!'),
            backgroundColor: Colors.red,
          ),
        );
      }
      return;
    }

    // Deduct entry cost
    final success = await casinoService.deductTokens(entryCost, 'lucky_darts');
    if (!success) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Failed to process payment!'),
            backgroundColor: Colors.red,
          ),
        );
      }
      return;
    }

    setState(() {
      _isPlaying = true;
      _lastResult = null;
    });

    // Play throw sound
    await soundService.playButtonClick();
    await vibrationService.onButtonTap();

    // Animate dart throw
    _dartController.reset();
    await _dartController.forward();

    // Simulate dart throw result based on weighted probabilities
    final dartZone = _selectDartZone();
    final accuracy = _calculateAccuracy(dartZone);
    final tokensWon = zoneRewards[dartZone]!;

    // Award tokens if won
    if (tokensWon > 0) {
      await casinoService.awardTokens(tokensWon, 'lucky_darts', 'Lucky darts ${dartZone.name}');
    }

    // Update statistics
    await casinoService.updateCasinoStats(
      gameId: 'lucky_darts',
      isWin: tokensWon > 0,
      tokensWon: tokensWon,
      tokensSpent: entryCost,
    );

    // Create result
    final result = DartResult(
      zone: dartZone,
      tokensWon: tokensWon,
      accuracy: accuracy,
    );

    setState(() {
      _lastResult = result;
      _isPlaying = false;
    });

    // Play result sound and vibration
    if (tokensWon > 0) {
      await soundService.playVictory();
      await vibrationService.onButtonTap();
    } else {
      await soundService.playDefeat();
      await vibrationService.onButtonTap();
    }

    // Show result animation
    _resultController.reset();
    await _resultController.forward();

    // Check if user can still play
    await _checkCanPlay();
  }

  DartZone _selectDartZone() {
    final random = math.Random().nextDouble();
    double cumulativeProbability = 0.0;

    for (final entry in zoneProbabilities.entries) {
      cumulativeProbability += entry.value;
      if (random <= cumulativeProbability) {
        return entry.key;
      }
    }

    // Fallback to miss (should never happen)
    return DartZone.miss;
  }

  double _calculateAccuracy(DartZone zone) {
    // Calculate accuracy based on zone hit
    switch (zone) {
      case DartZone.bullseye:
        return 1.0;
      case DartZone.inner:
        return 0.8 + math.Random().nextDouble() * 0.2;
      case DartZone.middle:
        return 0.6 + math.Random().nextDouble() * 0.2;
      case DartZone.outer:
        return 0.4 + math.Random().nextDouble() * 0.2;
      case DartZone.miss:
        return math.Random().nextDouble() * 0.4;
    }
  }

  @override
  Widget build(BuildContext context) {
    final userTokens = ref.watch(userTokensProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text(
          '🎯 Lucky Darts',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        backgroundColor: Colors.green.shade800,
        foregroundColor: Colors.white,
        actions: [
          Padding(
            padding: const EdgeInsets.only(right: 16.0),
            child: userTokens.when(
              data: (tokens) => Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: Colors.black.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Icon(Icons.toll, color: Colors.amber, size: 16),
                    const SizedBox(width: 4),
                    Text('$tokens', style: const TextStyle(fontWeight: FontWeight.bold)),
                  ],
                ),
              ),
              loading: () => const CircularProgressIndicator(),
              error: (_, __) => const Icon(Icons.error),
            ),
          ),
        ],
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Colors.green.shade800,
              Colors.green.shade600,
            ],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              // Game info
              Container(
                padding: const EdgeInsets.all(20),
                child: Column(
                  children: [
                    Text(
                      'Aim for the Bullseye!',
                      style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Cost: $entryCost tokens per throw',
                      style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                        color: Colors.white.withOpacity(0.9),
                      ),
                    ),
                  ],
                ),
              ),

              // Dart board
              Expanded(
                child: Center(
                  child: SizedBox(
                    width: 250,
                    height: 250,
                    child: Stack(
                      alignment: Alignment.center,
                      children: [
                        // Dart board
                        CustomPaint(
                          size: const Size(250, 250),
                          painter: DartBoardPainter(lastResult: _lastResult),
                        ),

                        // Dart animation
                        if (_isPlaying)
                          AnimatedBuilder(
                            animation: _dartController,
                            builder: (context, child) {
                              return Transform.translate(
                                offset: Offset(
                                  0,
                                  -100 + (_dartController.value * 100),
                                ),
                                child: const Icon(
                                  Icons.navigation,
                                  color: Colors.brown,
                                  size: 24,
                                ),
                              );
                            },
                          ),
                      ],
                    ),
                  ),
                ),
              ),

              // Prize table
              Container(
                margin: const EdgeInsets.all(20),
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.1),
                      blurRadius: 8,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Prize Table',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 12),
                    ...DartZone.values.map((zone) => Padding(
                      padding: const EdgeInsets.symmetric(vertical: 2),
                      child: Row(
                        children: [
                          Container(
                            width: 16,
                            height: 16,
                            decoration: BoxDecoration(
                              color: zone.zoneColor,
                              shape: BoxShape.circle,
                            ),
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              zone.zoneName,
                              style: Theme.of(context).textTheme.bodyMedium,
                            ),
                          ),
                          Text(
                            '${zoneRewards[zone]} tokens',
                            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    )),
                  ],
                ),
              ),

              // Result display
              if (_lastResult != null)
                Container(
                  margin: const EdgeInsets.symmetric(horizontal: 20),
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.1),
                        blurRadius: 8,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: Column(
                    children: [
                      Text(
                        _lastResult!.tokensWon > 0 ? '🎯 Hit!' : '😔 Miss!',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: _lastResult!.tokensWon > 0 ? Colors.green : Colors.red,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        '${_lastResult!.zone.zoneName} - ${_lastResult!.tokensWon} tokens',
                        style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ).animate(controller: _resultController)
                 .fadeIn()
                 .scale(begin: const Offset(0.8, 0.8)),

              // Throw button
              Container(
                padding: const EdgeInsets.all(20),
                child: SizedBox(
                  width: double.infinity,
                  height: 56,
                  child: ElevatedButton(
                    onPressed: _canPlay && !_isPlaying ? _throwDart : null,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.white,
                      foregroundColor: Colors.green.shade800,
                      disabledBackgroundColor: Colors.grey.shade300,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(28),
                      ),
                    ),
                    child: _isPlaying
                        ? const CircularProgressIndicator()
                        : Text(
                            _canPlay ? 'THROW DART ($entryCost tokens)' : 'Insufficient Tokens',
                            style: const TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

// Extension methods for DartZone
extension DartZoneExtension on DartZone {
  String get zoneName {
    switch (this) {
      case DartZone.miss:
        return 'Miss';
      case DartZone.outer:
        return 'Outer Ring';
      case DartZone.middle:
        return 'Middle Ring';
      case DartZone.inner:
        return 'Inner Ring';
      case DartZone.bullseye:
        return 'Bullseye';
    }
  }

  Color get zoneColor {
    switch (this) {
      case DartZone.miss:
        return Colors.grey;
      case DartZone.outer:
        return Colors.blue;
      case DartZone.middle:
        return Colors.green;
      case DartZone.inner:
        return Colors.orange;
      case DartZone.bullseye:
        return Colors.red;
    }
  }
}

// Custom painter for the dart board
class DartBoardPainter extends CustomPainter {
  final DartResult? lastResult;

  DartBoardPainter({this.lastResult});

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width / 2;

    // Draw dart board rings from outside to inside
    final rings = [
      (radius * 1.0, DartZone.miss),
      (radius * 0.8, DartZone.outer),
      (radius * 0.6, DartZone.middle),
      (radius * 0.4, DartZone.inner),
      (radius * 0.2, DartZone.bullseye),
    ];

    for (final ring in rings) {
      final paint = Paint()
        ..color = ring.$2.zoneColor
        ..style = PaintingStyle.fill;

      // Highlight the hit zone
      if (lastResult != null && ring.$2 == lastResult!.zone) {
        paint.color = paint.color.withOpacity(0.8);
      }

      canvas.drawCircle(center, ring.$1, paint);

      // Draw border
      final borderPaint = Paint()
        ..color = Colors.white
        ..style = PaintingStyle.stroke
        ..strokeWidth = 2;

      canvas.drawCircle(center, ring.$1, borderPaint);
    }

    // Draw dart if there's a result
    if (lastResult != null) {
      final dartPaint = Paint()
        ..color = Colors.brown
        ..style = PaintingStyle.fill;

      // Calculate dart position based on zone
      final dartRadius = _getDartRadius(lastResult!.zone, radius);
      final angle = math.Random().nextDouble() * 2 * math.pi;
      final dartX = center.dx + dartRadius * math.cos(angle);
      final dartY = center.dy + dartRadius * math.sin(angle);

      canvas.drawCircle(Offset(dartX, dartY), 4, dartPaint);
    }
  }

  double _getDartRadius(DartZone zone, double maxRadius) {
    switch (zone) {
      case DartZone.bullseye:
        return maxRadius * 0.1;
      case DartZone.inner:
        return maxRadius * 0.3;
      case DartZone.middle:
        return maxRadius * 0.5;
      case DartZone.outer:
        return maxRadius * 0.7;
      case DartZone.miss:
        return maxRadius * 0.9;
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return true;
  }
}
