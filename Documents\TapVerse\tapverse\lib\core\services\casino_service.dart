import 'package:cloud_firestore/cloud_firestore.dart';
import 'auth_service.dart';
import 'firestore_service.dart';

class CasinoService {
  final FirestoreService _firestoreService;
  final AuthService _authService;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  CasinoService(this._firestoreService, this._authService);

  /// Check if user is authenticated (not anonymous)
  bool get isUserAuthenticated {
    final user = _authService.currentUser;
    return user != null && !user.isAnonymous;
  }

  /// Get user's current token balance
  Future<int> getUserTokens() async {
    final user = _authService.currentUser;
    if (user == null) return 0;

    try {
      final userDoc = await _firestore.collection('users').doc(user.uid).get();
      if (userDoc.exists) {
        return userDoc.data()?['tokens'] ?? 0;
      }
      return 0;
    } catch (e) {
      print('Error getting user tokens: $e');
      return 0;
    }
  }

  /// Check if user has enough tokens to play a casino game
  Future<bool> canPlayGame(int requiredTokens) async {
    if (!isUserAuthenticated) return false;
    
    final currentTokens = await getUserTokens();
    return currentTokens >= requiredTokens;
  }

  /// Deduct tokens from user account (for game entry)
  Future<bool> deductTokens(int amount, String gameId) async {
    if (!isUserAuthenticated) return false;

    final user = _authService.currentUser!;
    
    try {
      return await _firestore.runTransaction((transaction) async {
        final userRef = _firestore.collection('users').doc(user.uid);
        final userDoc = await transaction.get(userRef);
        
        if (!userDoc.exists) return false;
        
        final currentTokens = userDoc.data()?['tokens'] ?? 0;
        if (currentTokens < amount) return false;
        
        // Deduct tokens
        transaction.update(userRef, {
          'tokens': currentTokens - amount,
        });
        
        // Log the transaction
        final transactionRef = _firestore
            .collection('users')
            .doc(user.uid)
            .collection('casinoTransactions')
            .doc();
            
        transaction.set(transactionRef, {
          'type': 'debit',
          'amount': amount,
          'gameId': gameId,
          'timestamp': FieldValue.serverTimestamp(),
          'description': 'Game entry fee for $gameId',
        });
        
        return true;
      });
    } catch (e) {
      print('Error deducting tokens: $e');
      return false;
    }
  }

  /// Award tokens to user account (for game winnings)
  Future<bool> awardTokens(int amount, String gameId, String description) async {
    if (!isUserAuthenticated) return false;

    final user = _authService.currentUser!;
    
    try {
      return await _firestore.runTransaction((transaction) async {
        final userRef = _firestore.collection('users').doc(user.uid);
        final userDoc = await transaction.get(userRef);
        
        if (!userDoc.exists) return false;
        
        final currentTokens = userDoc.data()?['tokens'] ?? 0;
        
        // Award tokens
        transaction.update(userRef, {
          'tokens': currentTokens + amount,
        });
        
        // Log the transaction
        final transactionRef = _firestore
            .collection('users')
            .doc(user.uid)
            .collection('casinoTransactions')
            .doc();
            
        transaction.set(transactionRef, {
          'type': 'credit',
          'amount': amount,
          'gameId': gameId,
          'timestamp': FieldValue.serverTimestamp(),
          'description': description,
        });
        
        return true;
      });
    } catch (e) {
      print('Error awarding tokens: $e');
      return false;
    }
  }

  /// Update casino statistics for user
  Future<void> updateCasinoStats({
    required String gameId,
    required bool isWin,
    required int tokensWon,
    required int tokensSpent,
  }) async {
    if (!isUserAuthenticated) return;

    final user = _authService.currentUser!;
    
    try {
      final statsRef = _firestore
          .collection('users')
          .doc(user.uid)
          .collection('casinoStats')
          .doc(gameId);
          
      await _firestore.runTransaction((transaction) async {
        final statsDoc = await transaction.get(statsRef);
        
        if (statsDoc.exists) {
          final data = statsDoc.data()!;
          transaction.update(statsRef, {
            'gamesPlayed': (data['gamesPlayed'] ?? 0) + 1,
            'wins': (data['wins'] ?? 0) + (isWin ? 1 : 0),
            'losses': (data['losses'] ?? 0) + (isWin ? 0 : 1),
            'totalTokensWon': (data['totalTokensWon'] ?? 0) + tokensWon,
            'totalTokensSpent': (data['totalTokensSpent'] ?? 0) + tokensSpent,
            'lastPlayed': FieldValue.serverTimestamp(),
          });
        } else {
          transaction.set(statsRef, {
            'gameId': gameId,
            'gamesPlayed': 1,
            'wins': isWin ? 1 : 0,
            'losses': isWin ? 0 : 1,
            'totalTokensWon': tokensWon,
            'totalTokensSpent': tokensSpent,
            'firstPlayed': FieldValue.serverTimestamp(),
            'lastPlayed': FieldValue.serverTimestamp(),
          });
        }
      });
    } catch (e) {
      print('Error updating casino stats: $e');
    }
  }

  /// Get casino statistics for a specific game
  Future<Map<String, dynamic>?> getCasinoStats(String gameId) async {
    if (!isUserAuthenticated) return null;

    final user = _authService.currentUser!;
    
    try {
      final statsDoc = await _firestore
          .collection('users')
          .doc(user.uid)
          .collection('casinoStats')
          .doc(gameId)
          .get();
          
      return statsDoc.exists ? statsDoc.data() : null;
    } catch (e) {
      print('Error getting casino stats: $e');
      return null;
    }
  }

  /// Get all casino statistics for user
  Future<List<Map<String, dynamic>>> getAllCasinoStats() async {
    if (!isUserAuthenticated) return [];

    final user = _authService.currentUser!;
    
    try {
      final statsQuery = await _firestore
          .collection('users')
          .doc(user.uid)
          .collection('casinoStats')
          .get();
          
      return statsQuery.docs.map((doc) => {
        'id': doc.id,
        ...doc.data(),
      }).toList();
    } catch (e) {
      print('Error getting all casino stats: $e');
      return [];
    }
  }

  /// Get casino transaction history
  Future<List<Map<String, dynamic>>> getCasinoTransactions({int limit = 50}) async {
    if (!isUserAuthenticated) return [];

    final user = _authService.currentUser!;
    
    try {
      final transactionsQuery = await _firestore
          .collection('users')
          .doc(user.uid)
          .collection('casinoTransactions')
          .orderBy('timestamp', descending: true)
          .limit(limit)
          .get();
          
      return transactionsQuery.docs.map((doc) => {
        'id': doc.id,
        ...doc.data(),
      }).toList();
    } catch (e) {
      print('Error getting casino transactions: $e');
      return [];
    }
  }
}
