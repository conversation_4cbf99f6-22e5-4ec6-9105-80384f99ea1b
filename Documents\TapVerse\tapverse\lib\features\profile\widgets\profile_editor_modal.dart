import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../../../core/models/user_model.dart';
import '../../../core/models/profile_customization.dart';
import '../../../core/services/profile_service.dart';
import '../../../core/theme/neon_theme.dart';

class ProfileEditorModal extends StatefulWidget {
  final User user;
  final UserModel userModel;
  final VoidCallback onSave;

  const ProfileEditorModal({
    super.key,
    required this.user,
    required this.userModel,
    required this.onSave,
  });

  @override
  State<ProfileEditorModal> createState() => _ProfileEditorModalState();
}

class _ProfileEditorModalState extends State<ProfileEditorModal>
    with TickerProviderStateMixin {
  late TabController _tabController;
  late AnimationController _slideController;
  late Animation<Offset> _slideAnimation;
  late PageController _pageController;

  // Profile customization state
  late ProfileCustomization _originalCustomization;
  late ProfileCustomization _currentCustomization;
  bool _hasChanges = false;
  int _currentTabIndex = 0;

  @override
  void initState() {
    super.initState();

    // Initialize customization from user model
    _originalCustomization = widget.userModel.getProfileCustomization();

    // Update unlocked items based on user's achievements
    final profileService = ProfileService();
    final unlockedItems = profileService.getUnlockedItems(widget.userModel);
    _originalCustomization = _originalCustomization.copyWith(unlockedItems: unlockedItems);

    _currentCustomization = _originalCustomization;

    _tabController = TabController(length: 5, vsync: this);
    _pageController = PageController();

    _slideController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeOutCubic,
    ));

    _slideController.forward();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _pageController.dispose();
    _slideController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SlideTransition(
      position: _slideAnimation,
      child: Container(
        height: MediaQuery.of(context).size.height,
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              NeonTheme.darkBackground,
              NeonTheme.surfaceBackground,
            ],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              // Top Bar
              _buildTopBar(),

              // Live Preview Area (top third)
              Expanded(
                flex: 1,
                child: Container(
                  margin: const EdgeInsets.all(20),
                  child: _buildLivePreview(),
                ),
              ),

              // Tab Navigation Bar
              _buildTabNavigationBar(),

              // Tab Content (bottom two thirds)
              Expanded(
                flex: 2,
                child: PageView(
                  controller: _pageController,
                  onPageChanged: (index) {
                    setState(() {
                      _currentTabIndex = index;
                      _tabController.animateTo(index);
                    });
                  },
                  children: [
                    _buildProfilePictureTab(),
                    _buildSkillTitleTab(),
                    _buildBannerTab(),
                    _buildGlowColorTab(),
                    _buildPrivacyTab(),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTopBar() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            NeonTheme.cardBackground.withValues(alpha: 0.8),
            NeonTheme.surfaceBackground.withValues(alpha: 0.6),
          ],
        ),
        border: Border(
          bottom: BorderSide(
            color: NeonTheme.neonBlue.withValues(alpha: 0.3),
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          // Save button (left)
          Container(
            decoration: BoxDecoration(
              gradient: _hasChanges
                  ? LinearGradient(colors: [NeonTheme.neonGreen, NeonTheme.neonCyan])
                  : null,
              color: _hasChanges ? null : Colors.grey.withValues(alpha: 0.3),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                borderRadius: BorderRadius.circular(20),
                onTap: _hasChanges ? _saveChanges : null,
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.save,
                        color: _hasChanges ? Colors.white : Colors.grey,
                        size: 18,
                      ),
                      const SizedBox(width: 6),
                      Text(
                        'Save',
                        style: TextStyle(
                          color: _hasChanges ? Colors.white : Colors.grey,
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),

          const Spacer(),

          // Title (center)
          Text(
            'Edit Profile',
            style: NeonTheme.createNeonText(
              color: Colors.white,
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),

          const Spacer(),

          // Close button (right)
          Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(colors: [Colors.red.withValues(alpha: 0.8), Colors.red.withValues(alpha: 0.6)]),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                borderRadius: BorderRadius.circular(20),
                onTap: _closeModal,
                child: const Padding(
                  padding: EdgeInsets.all(8),
                  child: Icon(
                    Icons.close,
                    color: Colors.white,
                    size: 20,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTabNavigationBar() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            NeonTheme.cardBackground.withValues(alpha: 0.8),
            NeonTheme.surfaceBackground.withValues(alpha: 0.6),
          ],
        ),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: NeonTheme.neonBlue.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: TabBar(
        controller: _tabController,
        onTap: (index) {
          _pageController.animateToPage(
            index,
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeInOut,
          );
        },
        isScrollable: true,
        tabAlignment: TabAlignment.start,
        indicator: BoxDecoration(
          gradient: LinearGradient(
            colors: [NeonTheme.neonBlue, NeonTheme.neonPurple],
          ),
          borderRadius: BorderRadius.circular(16),
        ),
        labelColor: Colors.white,
        unselectedLabelColor: Colors.white.withValues(alpha: 0.6),
        labelStyle: const TextStyle(fontWeight: FontWeight.bold, fontSize: 12),
        unselectedLabelStyle: const TextStyle(fontSize: 12),
        tabs: const [
          Tab(
            icon: Icon(Icons.account_circle, size: 20),
            text: 'Avatar',
          ),
          Tab(
            icon: Icon(Icons.star, size: 20),
            text: 'Title',
          ),
          Tab(
            icon: Icon(Icons.wallpaper, size: 20),
            text: 'Banner',
          ),
          Tab(
            icon: Icon(Icons.color_lens, size: 20),
            text: 'Glow',
          ),
          Tab(
            icon: Icon(Icons.privacy_tip, size: 20),
            text: 'Privacy',
          ),
        ],
      ),
    );
  }

  Widget _buildLivePreview() {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            NeonTheme.cardBackground.withValues(alpha: 0.8),
            NeonTheme.surfaceBackground.withValues(alpha: 0.6),
          ],
        ),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: _currentCustomization.glowColor.withValues(alpha: 0.6),
          width: 2,
        ),
        boxShadow: [
          BoxShadow(
            color: _currentCustomization.glowColor.withValues(alpha: 0.4),
            blurRadius: 15,
            spreadRadius: 1,
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Text(
              'Live Preview',
              style: NeonTheme.createNeonText(
                color: Colors.white.withValues(alpha: 0.7),
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 16),
            // Simplified profile preview card
            _buildPreviewCard(),
          ],
        ),
      ),
    );
  }

  Widget _buildPreviewCard() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            _currentCustomization.glowColor.withValues(alpha: 0.2),
            _currentCustomization.glowColor.withValues(alpha: 0.1),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: _currentCustomization.glowColor.withValues(alpha: 0.4),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          // Profile picture preview
          Container(
            width: 50,
            height: 50,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [_currentCustomization.glowColor, _currentCustomization.glowColor.withValues(alpha: 0.7)],
              ),
              shape: BoxShape.circle,
            ),
            child: Icon(
              _getProfilePictureIcon(_currentCustomization.profilePicture),
              color: Colors.white,
              size: 24,
            ),
          ),

          const SizedBox(width: 12),

          // User info preview
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.userModel.displayName,
                  style: NeonTheme.createNeonText(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  _currentCustomization.skillTitle,
                  style: NeonTheme.createNeonText(
                    color: _currentCustomization.glowColor,
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),

          // Glow indicator
          Container(
            width: 12,
            height: 12,
            decoration: BoxDecoration(
              color: _currentCustomization.glowColor,
              shape: BoxShape.circle,
              boxShadow: [
                BoxShadow(
                  color: _currentCustomization.glowColor.withValues(alpha: 0.6),
                  blurRadius: 6,
                  spreadRadius: 2,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProfilePictureTab() {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Choose Profile Picture',
            style: NeonTheme.createNeonText(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          Text(
            'Unlock new profile pictures by completing achievements!',
            style: NeonTheme.createNeonText(
              color: Colors.white.withValues(alpha: 0.7),
              fontSize: 14,
            ),
          ),
          const SizedBox(height: 20),
          Expanded(
            child: GridView.builder(
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 3,
                crossAxisSpacing: 16,
                mainAxisSpacing: 16,
                childAspectRatio: 1,
              ),
              itemCount: _getAvailableProfilePictures().length,
              itemBuilder: (context, index) {
                final picture = _getAvailableProfilePictures()[index];
                return _buildProfilePictureOption(picture);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSkillTitleTab() {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Choose Skill Title',
            style: NeonTheme.createNeonText(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          Text(
            'Earn titles by reaching milestones and achievements!',
            style: NeonTheme.createNeonText(
              color: Colors.white.withValues(alpha: 0.7),
              fontSize: 14,
            ),
          ),
          const SizedBox(height: 20),
          Expanded(
            child: ListView.builder(
              itemCount: _getAvailableSkillTitles().length,
              itemBuilder: (context, index) {
                final title = _getAvailableSkillTitles()[index];
                return _buildSkillTitleOption(title);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBannerTab() {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Choose Profile Banner',
            style: NeonTheme.createNeonText(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          Text(
            'Unlock banners through special events and achievements!',
            style: NeonTheme.createNeonText(
              color: Colors.white.withValues(alpha: 0.7),
              fontSize: 14,
            ),
          ),
          const SizedBox(height: 20),
          Expanded(
            child: GridView.builder(
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                crossAxisSpacing: 16,
                mainAxisSpacing: 16,
                childAspectRatio: 2,
              ),
              itemCount: _getAvailableBanners().length,
              itemBuilder: (context, index) {
                final banner = _getAvailableBanners()[index];
                return _buildBannerOption(banner);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGlowColorTab() {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Color & Effects',
            style: NeonTheme.createNeonText(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          Text(
            'Customize your profile card\'s glow and particle effects!',
            style: NeonTheme.createNeonText(
              color: Colors.white.withValues(alpha: 0.7),
              fontSize: 14,
            ),
          ),
          const SizedBox(height: 20),

          // Glow Color Section
          Text(
            'Glow Color',
            style: NeonTheme.createNeonText(
              color: Colors.white,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          SizedBox(
            height: 80,
            child: GridView.builder(
              scrollDirection: Axis.horizontal,
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 1,
                crossAxisSpacing: 16,
                mainAxisSpacing: 16,
                childAspectRatio: 1,
              ),
              itemCount: _getAvailableGlowColors().length,
              itemBuilder: (context, index) {
                final color = _getAvailableGlowColors()[index];
                return _buildGlowColorOption(color);
              },
            ),
          ),

          const SizedBox(height: 24),

          // Particle Intensity Section
          Text(
            'Particle Intensity',
            style: NeonTheme.createNeonText(
              color: Colors.white,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          _buildParticleIntensitySlider(),

          const SizedBox(height: 20),

          // Preview section
          Text(
            'Effect Preview',
            style: NeonTheme.createNeonText(
              color: Colors.white,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          Expanded(
            child: _buildEffectPreview(),
          ),
        ],
      ),
    );
  }

  Widget _buildPrivacyTab() {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Privacy & Display',
            style: NeonTheme.createNeonText(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          Text(
            'Control what information is visible on your profile.',
            style: NeonTheme.createNeonText(
              color: Colors.white.withValues(alpha: 0.7),
              fontSize: 14,
            ),
          ),
          const SizedBox(height: 20),
          _buildPrivacyOption(
            'Show Statistics on Leaderboards',
            'Display your game statistics on public leaderboards',
            _currentCustomization.privacySettings.showStats,
            (value) => _updateCustomization(
              _currentCustomization.copyWith(
                privacySettings: _currentCustomization.privacySettings.copyWith(showStats: value),
              ),
            ),
          ),
          const SizedBox(height: 16),
          _buildPrivacyOption(
            'Allow Full Profile View',
            'Allow other players to view your complete profile',
            _currentCustomization.privacySettings.showFullProfile,
            (value) => _updateCustomization(
              _currentCustomization.copyWith(
                privacySettings: _currentCustomization.privacySettings.copyWith(showFullProfile: value),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Helper methods for getting available options
  List<Map<String, dynamic>> _getAvailableProfilePictures() {
    final unlocked = _currentCustomization.unlockedItems.avatars;
    return [
      {'id': 'default', 'name': 'Default', 'unlocked': unlocked.contains('default'), 'icon': Icons.person},
      {'id': 'gamer', 'name': 'Gamer', 'unlocked': unlocked.contains('gamer'), 'icon': Icons.games},
      {'id': 'star', 'name': 'Star Player', 'unlocked': unlocked.contains('star'), 'icon': Icons.star, 'requirement': 'Score 1000+ in any game'},
      {'id': 'crown', 'name': 'Champion', 'unlocked': unlocked.contains('crown'), 'icon': Icons.emoji_events, 'requirement': 'Win 10 games'},
      {'id': 'diamond', 'name': 'VIP', 'unlocked': unlocked.contains('diamond'), 'icon': Icons.diamond, 'requirement': 'Earn 1000 tokens'},
      {'id': 'fire', 'name': 'Streak Master', 'unlocked': unlocked.contains('fire'), 'icon': Icons.local_fire_department, 'requirement': 'Play 7 days in a row'},
      {'id': 'rocket', 'name': 'Speed Demon', 'unlocked': unlocked.contains('rocket'), 'icon': Icons.rocket_launch, 'requirement': 'Complete 5 games under 30 seconds'},
      {'id': 'brain', 'name': 'Genius', 'unlocked': unlocked.contains('brain'), 'icon': Icons.psychology, 'requirement': 'Score perfect in 3 puzzle games'},
    ];
  }

  List<Map<String, dynamic>> _getAvailableSkillTitles() {
    final unlocked = _currentCustomization.unlockedItems.titles;
    return [
      {'id': 'Player', 'name': 'Player', 'unlocked': unlocked.contains('Player'), 'color': NeonTheme.neonBlue},
      {'id': 'Gamer', 'name': 'Gamer', 'unlocked': unlocked.contains('Gamer'), 'color': NeonTheme.neonGreen},
      {'id': 'Arcade King', 'name': 'Arcade King', 'unlocked': unlocked.contains('Arcade King'), 'color': NeonTheme.neonAmber, 'requirement': 'Win 25 arcade games'},
      {'id': 'Token Hoarder', 'name': 'Token Hoarder', 'unlocked': unlocked.contains('Token Hoarder'), 'color': NeonTheme.neonPink, 'requirement': 'Collect 5000+ tokens'},
      {'id': 'Speed Runner', 'name': 'Speed Runner', 'unlocked': unlocked.contains('Speed Runner'), 'color': NeonTheme.neonCyan, 'requirement': 'Complete 10 games under 30 seconds'},
      {'id': 'Puzzle Master', 'name': 'Puzzle Master', 'unlocked': unlocked.contains('Puzzle Master'), 'color': NeonTheme.neonPurple, 'requirement': 'Solve 50 puzzles'},
      {'id': 'High Scorer', 'name': 'High Scorer', 'unlocked': unlocked.contains('High Scorer'), 'color': const Color(0xFFFFD700), 'requirement': 'Get top 10 in any leaderboard'},
      {'id': 'Game Master', 'name': 'Game Master', 'unlocked': unlocked.contains('Game Master'), 'color': const Color(0xFFFF4500), 'requirement': 'Play 15 different games'},
      {'id': 'Legend', 'name': 'Legend', 'unlocked': unlocked.contains('Legend'), 'color': const Color(0xFFDC143C), 'requirement': 'Unlock all achievements'},
    ];
  }

  List<Map<String, dynamic>> _getAvailableBanners() {
    final unlocked = _currentCustomization.unlockedItems.banners;
    return [
      {'id': 'default', 'name': 'Default', 'unlocked': unlocked.contains('default'), 'gradient': [NeonTheme.neonBlue, NeonTheme.neonPurple], 'animated': false},
      {'id': 'neon_wave', 'name': 'Neon Wave', 'unlocked': unlocked.contains('neon_wave'), 'gradient': [NeonTheme.neonCyan, NeonTheme.neonBlue], 'animated': true, 'requirement': 'Play 50 games'},
      {'id': 'sunset', 'name': 'Sunset Glow', 'unlocked': unlocked.contains('sunset'), 'gradient': [const Color(0xFFFF6B35), const Color(0xFFFF8E53)], 'animated': false, 'requirement': 'Play during evening hours'},
      {'id': 'ocean', 'name': 'Ocean Depths', 'unlocked': unlocked.contains('ocean'), 'gradient': [const Color(0xFF006994), const Color(0xFF47B5FF)], 'animated': true, 'requirement': 'Score 2000+ total'},
      {'id': 'fire', 'name': 'Blazing Fire', 'unlocked': unlocked.contains('fire'), 'gradient': [const Color(0xFFFF4500), const Color(0xFFFF6347)], 'animated': true, 'requirement': 'Win 5 games in a row'},
      {'id': 'galaxy', 'name': 'Galaxy', 'unlocked': unlocked.contains('galaxy'), 'gradient': [const Color(0xFF4B0082), const Color(0xFF8A2BE2)], 'animated': true, 'requirement': 'Reach top 5 in leaderboard'},
      {'id': 'matrix', 'name': 'Matrix Code', 'unlocked': unlocked.contains('matrix'), 'gradient': [const Color(0xFF00FF00), const Color(0xFF32CD32)], 'animated': true, 'requirement': 'Complete 100 games'},
    ];
  }

  List<Color> _getAvailableGlowColors() {
    return [
      NeonTheme.neonBlue,
      NeonTheme.neonPurple,
      NeonTheme.neonPink,
      NeonTheme.neonAmber,
      NeonTheme.neonGreen,
      NeonTheme.neonCyan,
      Colors.red,
      Colors.orange,
    ];
  }

  // Widget builders for options
  Widget _buildProfilePictureOption(Map<String, dynamic> picture) {
    final isSelected = _currentCustomization.profilePicture == picture['id'];
    final isUnlocked = picture['unlocked'] as bool;

    return GestureDetector(
      onTap: isUnlocked ? () => _updateCustomization(
        _currentCustomization.copyWith(profilePicture: picture['id'])
      ) : () => _showUnlockRequirement(picture['name'], picture['requirement']),
      onLongPress: !isUnlocked ? () => _showUnlockRequirement(picture['name'], picture['requirement']) : null,
      child: Container(
        decoration: BoxDecoration(
          gradient: isSelected
              ? LinearGradient(colors: [NeonTheme.neonBlue, NeonTheme.neonPurple])
              : LinearGradient(
                  colors: isUnlocked
                      ? [Colors.white.withValues(alpha: 0.1), Colors.white.withValues(alpha: 0.05)]
                      : [Colors.grey.withValues(alpha: 0.1), Colors.grey.withValues(alpha: 0.05)],
                ),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: isSelected
                ? NeonTheme.neonBlue
                : isUnlocked
                    ? Colors.white.withValues(alpha: 0.3)
                    : Colors.grey.withValues(alpha: 0.3),
            width: 2,
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              picture['icon'],
              size: 32,
              color: isUnlocked ? Colors.white : Colors.grey,
            ),
            const SizedBox(height: 8),
            Text(
              picture['name'],
              style: TextStyle(
                color: isUnlocked ? Colors.white : Colors.grey,
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),
            if (!isUnlocked) ...[
              const SizedBox(height: 4),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.lock,
                    size: 12,
                    color: Colors.grey,
                  ),
                  const SizedBox(width: 4),
                  Flexible(
                    child: Text(
                      picture['requirement'] ?? 'Locked',
                      style: const TextStyle(
                        color: Colors.grey,
                        fontSize: 8,
                      ),
                      textAlign: TextAlign.center,
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildSkillTitleOption(Map<String, dynamic> title) {
    final isSelected = _currentCustomization.skillTitle == title['id'];
    final isUnlocked = title['unlocked'] as bool;

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: isUnlocked ? () => _updateCustomization(
            _currentCustomization.copyWith(skillTitle: title['id'])
          ) : () => _showUnlockRequirement(title['name'], title['requirement']),
          onLongPress: !isUnlocked ? () => _showUnlockRequirement(title['name'], title['requirement']) : null,
          borderRadius: BorderRadius.circular(12),
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              gradient: isSelected
                  ? LinearGradient(colors: [title['color'], title['color'].withValues(alpha: 0.7)])
                  : LinearGradient(
                      colors: isUnlocked
                          ? [Colors.white.withValues(alpha: 0.1), Colors.white.withValues(alpha: 0.05)]
                          : [Colors.grey.withValues(alpha: 0.1), Colors.grey.withValues(alpha: 0.05)],
                    ),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: isSelected
                    ? title['color']
                    : isUnlocked
                        ? Colors.white.withValues(alpha: 0.3)
                        : Colors.grey.withValues(alpha: 0.3),
                width: 1,
              ),
            ),
            child: Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title['name'],
                        style: TextStyle(
                          color: isUnlocked ? Colors.white : Colors.grey,
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      if (!isUnlocked && title['requirement'] != null) ...[
                        const SizedBox(height: 4),
                        Text(
                          'Unlock: ${title['requirement']}',
                          style: TextStyle(
                            color: Colors.grey,
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
                if (isSelected)
                  Icon(Icons.check_circle, color: title['color'])
                else if (!isUnlocked)
                  const Icon(Icons.lock, color: Colors.grey),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildBannerOption(Map<String, dynamic> banner) {
    final isSelected = _currentCustomization.banner == banner['id'];
    final isUnlocked = banner['unlocked'] as bool;

    return GestureDetector(
      onTap: isUnlocked ? () => _updateCustomization(
        _currentCustomization.copyWith(banner: banner['id'])
      ) : () => _showUnlockRequirement(banner['name'], banner['requirement']),
      onLongPress: !isUnlocked ? () => _showUnlockRequirement(banner['name'], banner['requirement']) : null,
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(colors: banner['gradient']),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isSelected
                ? Colors.white
                : isUnlocked
                    ? Colors.white.withValues(alpha: 0.3)
                    : Colors.grey.withValues(alpha: 0.3),
            width: isSelected ? 3 : 1,
          ),
        ),
        child: Stack(
          children: [
            if (!isUnlocked)
              Container(
                decoration: BoxDecoration(
                  color: Colors.black.withValues(alpha: 0.6),
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      if (banner['animated'] == true) ...[
                        const Icon(Icons.auto_awesome, color: Colors.white, size: 16),
                        const SizedBox(width: 4),
                      ],
                      if (isSelected)
                        const Icon(Icons.check_circle, color: Colors.white, size: 20)
                      else if (!isUnlocked)
                        const Icon(Icons.lock, color: Colors.white, size: 20),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Text(
                    banner['name'],
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  if (banner['animated'] == true) ...[
                    const SizedBox(height: 4),
                    Text(
                      'Animated',
                      style: TextStyle(
                        color: Colors.white.withValues(alpha: 0.7),
                        fontSize: 10,
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildGlowColorOption(Color color) {
    final isSelected = _currentCustomization.glowColor == color;

    return GestureDetector(
      onTap: () => _updateCustomization(
        _currentCustomization.copyWith(glowColor: color)
      ),
      child: Container(
        decoration: BoxDecoration(
          color: color,
          shape: BoxShape.circle,
          border: Border.all(
            color: isSelected ? Colors.white : Colors.transparent,
            width: 3,
          ),
          boxShadow: [
            BoxShadow(
              color: color.withValues(alpha: 0.4),
              blurRadius: 8,
              spreadRadius: 2,
            ),
          ],
        ),
        child: isSelected
            ? const Icon(Icons.check, color: Colors.white, size: 24)
            : null,
      ),
    );
  }

  Widget _buildPrivacyOption(
    String title,
    String description,
    bool value,
    ValueChanged<bool> onChanged,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Colors.white.withValues(alpha: 0.1),
            Colors.white.withValues(alpha: 0.05),
          ],
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  description,
                  style: TextStyle(
                    color: Colors.white.withValues(alpha: 0.7),
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
          Switch(
            value: value,
            onChanged: onChanged,
            activeColor: NeonTheme.neonGreen,
            activeTrackColor: NeonTheme.neonGreen.withValues(alpha: 0.3),
          ),
        ],
      ),
    );
  }

  void _closeModal() async {
    await _slideController.reverse();
    if (mounted) {
      Navigator.of(context).pop();
    }
  }

  void _updateCustomization(ProfileCustomization newCustomization) {
    setState(() {
      _currentCustomization = newCustomization;
      _hasChanges = _currentCustomization != _originalCustomization;
    });
  }

  Widget _buildParticleIntensitySlider() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            NeonTheme.cardBackground.withValues(alpha: 0.6),
            NeonTheme.surfaceBackground.withValues(alpha: 0.4),
          ],
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: _currentCustomization.glowColor.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: ParticleIntensity.values.map((intensity) {
              final isSelected = _currentCustomization.particleIntensity == intensity;
              return GestureDetector(
                onTap: () => _updateCustomization(
                  _currentCustomization.copyWith(particleIntensity: intensity),
                ),
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  decoration: BoxDecoration(
                    gradient: isSelected
                        ? LinearGradient(colors: [_currentCustomization.glowColor, _currentCustomization.glowColor.withValues(alpha: 0.7)])
                        : null,
                    color: isSelected ? null : Colors.white.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(
                      color: isSelected ? _currentCustomization.glowColor : Colors.white.withValues(alpha: 0.3),
                      width: 1,
                    ),
                  ),
                  child: Text(
                    intensity.displayName,
                    style: TextStyle(
                      color: isSelected ? Colors.white : Colors.white.withValues(alpha: 0.7),
                      fontSize: 14,
                      fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                    ),
                  ),
                ),
              );
            }).toList(),
          ),
          const SizedBox(height: 12),
          Text(
            'Current: ${_currentCustomization.particleIntensity.displayName} (${(_currentCustomization.particleIntensity.intensity * 100).toInt()}%)',
            style: TextStyle(
              color: Colors.white.withValues(alpha: 0.7),
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEffectPreview() {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            _currentCustomization.glowColor.withValues(alpha: 0.2),
            _currentCustomization.glowColor.withValues(alpha: 0.1),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: _currentCustomization.glowColor.withValues(alpha: 0.4),
          width: 2,
        ),
        boxShadow: [
          BoxShadow(
            color: _currentCustomization.glowColor.withValues(alpha: _currentCustomization.particleIntensity.intensity * 0.6),
            blurRadius: 20,
            spreadRadius: 4,
          ),
        ],
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [_currentCustomization.glowColor, _currentCustomization.glowColor.withValues(alpha: 0.7)],
                ),
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color: _currentCustomization.glowColor.withValues(alpha: _currentCustomization.particleIntensity.intensity),
                    blurRadius: 15,
                    spreadRadius: 3,
                  ),
                ],
              ),
              child: const Icon(
                Icons.auto_awesome,
                color: Colors.white,
                size: 30,
              ),
            ),
            const SizedBox(height: 16),
            Text(
              'Glow Preview',
              style: NeonTheme.createNeonText(
                color: _currentCustomization.glowColor,
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Intensity: ${_currentCustomization.particleIntensity.displayName}',
              style: TextStyle(
                color: Colors.white.withValues(alpha: 0.7),
                fontSize: 12,
              ),
            ),
          ],
        ),
      ),
    );
  }

  IconData _getProfilePictureIcon(String pictureId) {
    switch (pictureId) {
      case 'default':
        return Icons.person;
      case 'gamer':
        return Icons.games;
      case 'star':
        return Icons.star;
      case 'crown':
        return Icons.emoji_events;
      case 'diamond':
        return Icons.diamond;
      case 'fire':
        return Icons.local_fire_department;
      case 'rocket':
        return Icons.rocket_launch;
      case 'brain':
        return Icons.psychology;
      default:
        return Icons.person;
    }
  }

  void _showUnlockRequirement(String itemName, String? requirement) {
    if (requirement == null) return;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: NeonTheme.cardBackground,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
          side: BorderSide(
            color: _currentCustomization.glowColor.withValues(alpha: 0.5),
            width: 2,
          ),
        ),
        title: Row(
          children: [
            Icon(
              Icons.lock,
              color: _currentCustomization.glowColor,
              size: 24,
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                'Unlock $itemName',
                style: NeonTheme.createNeonText(
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Requirement:',
              style: TextStyle(
                color: Colors.white.withValues(alpha: 0.7),
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    _currentCustomization.glowColor.withValues(alpha: 0.2),
                    _currentCustomization.glowColor.withValues(alpha: 0.1),
                  ],
                ),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: _currentCustomization.glowColor.withValues(alpha: 0.3),
                  width: 1,
                ),
              ),
              child: Text(
                requirement,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                ),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(
              'Got it!',
              style: TextStyle(
                color: _currentCustomization.glowColor,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _saveChanges() async {
    // Show loading state
    setState(() {
      _hasChanges = false; // Disable save button during save
    });

    try {
      final profileService = ProfileService();
      final success = await profileService.saveProfileCustomization(_currentCustomization);

      if (success) {
        setState(() {
          _originalCustomization = _currentCustomization;
        });

        // Show success message
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  Icon(Icons.check_circle, color: NeonTheme.neonGreen),
                  const SizedBox(width: 8),
                  const Text('Profile saved successfully!'),
                ],
              ),
              backgroundColor: NeonTheme.cardBackground,
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
                side: BorderSide(color: NeonTheme.neonGreen.withValues(alpha: 0.5)),
              ),
            ),
          );
        }

        widget.onSave();
        _closeModal();
      } else {
        // Show error message
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  const Icon(Icons.error, color: Colors.red),
                  const SizedBox(width: 8),
                  const Text('Failed to save profile. Please try again.'),
                ],
              ),
              backgroundColor: NeonTheme.cardBackground,
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
                side: BorderSide(color: Colors.red.withValues(alpha: 0.5)),
              ),
            ),
          );
        }

        // Re-enable save button
        setState(() {
          _hasChanges = _currentCustomization != _originalCustomization;
        });
      }
    } catch (e) {
      print('Error saving profile: $e');

      // Show error message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.error, color: Colors.red),
                const SizedBox(width: 8),
                const Text('An error occurred. Please try again.'),
              ],
            ),
            backgroundColor: NeonTheme.cardBackground,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
              side: BorderSide(color: Colors.red.withValues(alpha: 0.5)),
            ),
          ),
        );
      }

      // Re-enable save button
      setState(() {
        _hasChanges = _currentCustomization != _originalCustomization;
      });
    }
  }
}
