import 'dart:math';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../../../core/models/game_state.dart';
import '../../../core/providers/game_providers.dart';
import '../../../core/services/game_feedback_service.dart';
import '../../../shared/widgets/base_game_widget.dart';

class HueDropGame extends BaseGameWidget {
  static const GameConfig gameConfig = GameConfig(
    gameId: 'hue_drop',
    gameName: 'HueDrop',
    scoreMultiplier: 2,
    hasLives: false,
    hasLevels: false,
    hasTimer: false,
  );

  const HueDropGame({
    super.key,
    super.onGameComplete,
    super.onGameExit,
  }) : super(config: gameConfig);

  @override
  Widget buildGameContent(BuildContext context, WidgetRef ref, GameState gameState) {
    return const _HueDropGameContent();
  }
}

class FallingBall {
  Offset position;
  final Color color;
  final double speed;
  bool isActive;

  FallingBall({
    required this.position,
    required this.color,
    required this.speed,
    this.isActive = true,
  });
}

class _HueDropGameContent extends ConsumerStatefulWidget {
  const _HueDropGameContent();

  @override
  ConsumerState<_HueDropGameContent> createState() => _HueDropGameContentState();
}

class _HueDropGameContentState extends ConsumerState<_HueDropGameContent>
    with TickerProviderStateMixin {
  
  // Game state
  late AnimationController _ringController;
  late AnimationController _matchController;
  late AnimationController _missController;
  
  // Color ring state
  double _ringRotation = 0;
  final List<Color> _ringColors = [
    Colors.red,
    Colors.orange,
    Colors.yellow,
    Colors.green,
    Colors.blue,
    Colors.purple,
  ];
  
  // Falling balls
  final List<FallingBall> _fallingBalls = [];
  final Random _random = Random();
  double _ballSpawnTimer = 0;
  double _ballSpawnInterval = 2.0; // seconds
  
  // Game mechanics
  final double _ringRadius = 80;
  final double _ballSize = 20;
  final double _ringCenterX = 200;
  final double _ringCenterY = 450;
  bool _showMatchEffect = false;
  bool _showMissEffect = false;
  
  // Constants
  static const double _gameWidth = 400;
  static const double _gameHeight = 600;

  @override
  void initState() {
    super.initState();
    
    _ringController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    
    _matchController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );
    
    _missController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _startGameLoop();
  }

  @override
  void dispose() {
    _ringController.dispose();
    _matchController.dispose();
    _missController.dispose();
    super.dispose();
  }

  void _startGameLoop() {
    const frameRate = 60;
    const frameDuration = Duration(milliseconds: 1000 ~/ frameRate);
    const deltaTime = 1.0 / frameRate;
    
    void gameLoop() {
      if (!mounted) return;
      
      final gameState = ref.read(gameStateProvider(HueDropGame.gameConfig));
      if (!gameState.isPlaying) {
        Future.delayed(frameDuration, gameLoop);
        return;
      }
      
      setState(() {
        // Update ball spawning
        _ballSpawnTimer += deltaTime;
        if (_ballSpawnTimer >= _ballSpawnInterval) {
          _spawnBall();
          _ballSpawnTimer = 0;
          // Gradually increase difficulty
          _ballSpawnInterval = (_ballSpawnInterval * 0.98).clamp(0.8, 2.0);
        }
        
        // Update falling balls
        for (final ball in _fallingBalls) {
          ball.position = Offset(
            ball.position.dx,
            ball.position.dy + ball.speed * deltaTime,
          );
          
          // Check if ball reached the ring
          final distanceToCenter = sqrt(
            pow(ball.position.dx - _ringCenterX, 2) +
            pow(ball.position.dy - _ringCenterY, 2)
          );
          
          if (distanceToCenter <= _ringRadius + _ballSize / 2 && 
              distanceToCenter >= _ringRadius - _ballSize / 2) {
            _checkColorMatch(ball);
            ball.isActive = false;
          }
          
          // Remove balls that fall off screen
          if (ball.position.dy > _gameHeight + 50) {
            ball.isActive = false;
          }
        }
        
        // Remove inactive balls
        _fallingBalls.removeWhere((ball) => !ball.isActive);
      });
      
      Future.delayed(frameDuration, gameLoop);
    }
    
    gameLoop();
  }

  void _spawnBall() {
    final color = _ringColors[_random.nextInt(_ringColors.length)];
    final x = _random.nextDouble() * (_gameWidth - _ballSize) + _ballSize / 2;
    final speed = 80 + _random.nextDouble() * 40; // 80-120 speed
    
    _fallingBalls.add(FallingBall(
      position: Offset(x, -_ballSize),
      color: color,
      speed: speed,
    ));
  }

  void _checkColorMatch(FallingBall ball) {
    // Calculate which segment of the ring the ball hit
    final angle = atan2(
      ball.position.dy - _ringCenterY,
      ball.position.dx - _ringCenterX,
    );
    
    // Normalize angle to 0-2π and account for ring rotation
    final normalizedAngle = (angle + pi + _ringRotation) % (2 * pi);
    final segmentIndex = (normalizedAngle / (2 * pi / _ringColors.length)).floor();
    final ringColor = _ringColors[segmentIndex % _ringColors.length];
    
    if (ball.color == ringColor) {
      // Match!
      _handleMatch();
    } else {
      // Mismatch - game over
      _handleMismatch();
    }
  }

  void _handleMatch() {
    // Add score
    final gameNotifier = ref.read(gameStateProvider(HueDropGame.gameConfig).notifier);
    gameNotifier.addScore(10);
    
    // Show match effect
    setState(() {
      _showMatchEffect = true;
    });
    
    // Trigger feedback
    ref.read(gameFeedbackServiceProvider).triggerFeedback(GameEvent.scoreIncrease);
    
    // Play match animation
    _matchController.forward().then((_) {
      _matchController.reset();
      setState(() {
        _showMatchEffect = false;
      });
    });
  }

  void _handleMismatch() {
    // Show miss effect
    setState(() {
      _showMissEffect = true;
    });
    
    // Trigger feedback
    ref.read(gameFeedbackServiceProvider).triggerFeedback(GameEvent.obstacleHit);
    
    // Play miss animation
    _missController.forward().then((_) {
      _missController.reset();
      setState(() {
        _showMissEffect = false;
      });
    });
    
    // Game over
    final gameNotifier = ref.read(gameStateProvider(HueDropGame.gameConfig).notifier);
    gameNotifier.endGame(reason: 'Color mismatch');
  }

  void _onTap() {
    final gameState = ref.read(gameStateProvider(HueDropGame.gameConfig));
    if (!gameState.isPlaying) return;
    
    setState(() {
      _ringRotation += pi / 3; // Rotate by 60 degrees (1/6 of circle)
    });
    
    // Trigger feedback
    ref.read(gameFeedbackServiceProvider).triggerFeedback(GameEvent.buttonPress);
    
    // Play rotation animation
    _ringController.forward().then((_) => _ringController.reset());
  }

  @override
  Widget build(BuildContext context) {
    final gameState = ref.watch(gameStateProvider(HueDropGame.gameConfig));
    
    return Container(
      width: _gameWidth,
      height: _gameHeight,
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [Color(0xFF1a1a2e), Color(0xFF16213e)],
        ),
      ),
      child: GestureDetector(
        onTap: _onTap,
        child: Stack(
          children: [
            // Background pattern
            _buildBackground(),
            
            // Falling balls
            ..._fallingBalls.map((ball) => _buildFallingBall(ball)),
            
            // Color ring
            _buildColorRing(),
            
            // Match effect
            if (_showMatchEffect) _buildMatchEffect(),
            
            // Miss effect
            if (_showMissEffect) _buildMissEffect(),
            
            // Score display
            _buildScoreDisplay(gameState),
            
            // Instructions
            if (!gameState.isPlaying) _buildInstructions(),
          ],
        ),
      ),
    );
  }

  Widget _buildBackground() {
    return Positioned.fill(
      child: CustomPaint(
        painter: _BackgroundPainter(),
      ),
    );
  }

  Widget _buildFallingBall(FallingBall ball) {
    return Positioned(
      left: ball.position.dx - _ballSize / 2,
      top: ball.position.dy - _ballSize / 2,
      child: Container(
        width: _ballSize,
        height: _ballSize,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          gradient: RadialGradient(
            colors: [ball.color.withOpacity(0.8), ball.color],
            stops: const [0.3, 1.0],
          ),
          boxShadow: [
            BoxShadow(
              color: ball.color.withOpacity(0.6),
              blurRadius: 8,
              spreadRadius: 2,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildColorRing() {
    return Positioned(
      left: _ringCenterX - _ringRadius - 20,
      top: _ringCenterY - _ringRadius - 20,
      child: ScaleTransition(
        scale: Tween<double>(begin: 1.0, end: 1.1).animate(_ringController),
        child: Transform.rotate(
          angle: _ringRotation,
          child: SizedBox(
            width: (_ringRadius + 20) * 2,
            height: (_ringRadius + 20) * 2,
            child: CustomPaint(
              painter: _ColorRingPainter(_ringColors, _ringRadius),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildMatchEffect() {
    return Positioned(
      left: _ringCenterX - 50,
      top: _ringCenterY - 50,
      child: ScaleTransition(
        scale: _matchController,
        child: Container(
          width: 100,
          height: 100,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            gradient: RadialGradient(
              colors: [Colors.green, Colors.lightGreen, Colors.transparent],
              stops: const [0.0, 0.5, 1.0],
            ),
          ),
          child: const Center(
            child: Text(
              '✓',
              style: TextStyle(
                color: Colors.white,
                fontSize: 40,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildMissEffect() {
    return Positioned(
      left: _ringCenterX - 50,
      top: _ringCenterY - 50,
      child: ScaleTransition(
        scale: _missController,
        child: Container(
          width: 100,
          height: 100,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            gradient: RadialGradient(
              colors: [Colors.red, Colors.redAccent, Colors.transparent],
              stops: const [0.0, 0.5, 1.0],
            ),
          ),
          child: const Center(
            child: Text(
              '✗',
              style: TextStyle(
                color: Colors.white,
                fontSize: 40,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildScoreDisplay(GameState gameState) {
    return Positioned(
      top: 50,
      left: 0,
      right: 0,
      child: Center(
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            color: Colors.black54,
            borderRadius: BorderRadius.circular(20),
          ),
          child: Text(
            'Score: ${gameState.score}',
            style: const TextStyle(
              color: Colors.white,
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildInstructions() {
    return Positioned(
      bottom: 100,
      left: 0,
      right: 0,
      child: Center(
        child: Container(
          padding: EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.black54,
            borderRadius: BorderRadius.all(Radius.circular(12)),
          ),
          child: Text(
            'Tap to rotate the color ring!\nMatch falling balls to the correct color.\nMismatch = Game Over!',
            textAlign: TextAlign.center,
            style: TextStyle(
              color: Colors.white,
              fontSize: 16,
            ),
          ),
        ),
      ),
    );
  }
}

class _BackgroundPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.white.withOpacity(0.1)
      ..strokeWidth = 1;
    
    // Draw subtle grid pattern
    for (double x = 0; x < size.width; x += 40) {
      canvas.drawLine(
        Offset(x, 0),
        Offset(x, size.height),
        paint,
      );
    }
    
    for (double y = 0; y < size.height; y += 40) {
      canvas.drawLine(
        Offset(0, y),
        Offset(size.width, y),
        paint,
      );
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class _ColorRingPainter extends CustomPainter {
  final List<Color> colors;
  final double radius;

  _ColorRingPainter(this.colors, this.radius);

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final segmentAngle = 2 * pi / colors.length;
    
    for (int i = 0; i < colors.length; i++) {
      final paint = Paint()
        ..color = colors[i]
        ..style = PaintingStyle.fill;
      
      final startAngle = i * segmentAngle - pi / 2;
      final sweepAngle = segmentAngle;
      
      canvas.drawArc(
        Rect.fromCircle(center: center, radius: radius),
        startAngle,
        sweepAngle,
        true,
        paint,
      );
      
      // Draw border
      final borderPaint = Paint()
        ..color = Colors.white
        ..style = PaintingStyle.stroke
        ..strokeWidth = 2;
      
      canvas.drawArc(
        Rect.fromCircle(center: center, radius: radius),
        startAngle,
        sweepAngle,
        true,
        borderPaint,
      );
    }
    
    // Draw center circle
    final centerPaint = Paint()
      ..color = Colors.black
      ..style = PaintingStyle.fill;
    
    canvas.drawCircle(center, radius * 0.3, centerPaint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
