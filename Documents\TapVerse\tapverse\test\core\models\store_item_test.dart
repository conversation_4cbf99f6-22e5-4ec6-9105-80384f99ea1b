import 'package:flutter_test/flutter_test.dart';
import 'package:tapverse/core/models/store_item.dart';

void main() {
  group('StoreItem', () {
    group('constructor', () {
      test('should create StoreItem with required fields', () {
        // Act
        final item = StoreItem(
          id: 'test_id',
          name: 'Test Item',
          type: StoreItemType.skin,
          price: 100,
          assetPath: 'test/path',
        );

        // Assert
        expect(item.id, equals('test_id'));
        expect(item.name, equals('Test Item'));
        expect(item.type, equals(StoreItemType.skin));
        expect(item.price, equals(100));
        expect(item.assetPath, equals('test/path'));
        expect(item.description, isNull);
        expect(item.isAvailable, isTrue);
      });

      test('should create StoreItem with all fields', () {
        // Act
        final item = StoreItem(
          id: 'test_id',
          name: 'Test Item',
          type: StoreItemType.effect,
          price: 250,
          assetPath: 'test/path',
          description: 'Test description',
          isAvailable: false,
        );

        // Assert
        expect(item.id, equals('test_id'));
        expect(item.name, equals('Test Item'));
        expect(item.type, equals(StoreItemType.effect));
        expect(item.price, equals(250));
        expect(item.assetPath, equals('test/path'));
        expect(item.description, equals('Test description'));
        expect(item.isAvailable, isFalse);
      });
    });

    group('formattedPrice', () {
      test('should return price as string for values under 1000', () {
        // Arrange
        final item = StoreItem(
          id: 'test',
          name: 'Test',
          type: StoreItemType.skin,
          price: 999,
          assetPath: 'path',
        );

        // Act & Assert
        expect(item.formattedPrice, equals('999'));
      });

      test('should return formatted price with K for values 1000 and above', () {
        // Arrange
        final item = StoreItem(
          id: 'test',
          name: 'Test',
          type: StoreItemType.skin,
          price: 1500,
          assetPath: 'path',
        );

        // Act & Assert
        expect(item.formattedPrice, equals('1.5K'));
      });

      test('should handle exact 1000 value', () {
        // Arrange
        final item = StoreItem(
          id: 'test',
          name: 'Test',
          type: StoreItemType.skin,
          price: 1000,
          assetPath: 'path',
        );

        // Act & Assert
        expect(item.formattedPrice, equals('1.0K'));
      });
    });

    group('typeDisplayName', () {
      test('should return correct display names for all types', () {
        expect(StoreItemType.skin.name, equals('skin'));
        expect(StoreItemType.effect.name, equals('effect'));
        expect(StoreItemType.powerup.name, equals('powerup'));
        expect(StoreItemType.theme.name, equals('theme'));
        expect(StoreItemType.sound.name, equals('sound'));
      });

      test('should return correct display name for skin', () {
        // Arrange
        final item = StoreItem(
          id: 'test',
          name: 'Test',
          type: StoreItemType.skin,
          price: 100,
          assetPath: 'path',
        );

        // Act & Assert
        expect(item.typeDisplayName, equals('Skin'));
      });

      test('should return correct display name for effect', () {
        // Arrange
        final item = StoreItem(
          id: 'test',
          name: 'Test',
          type: StoreItemType.effect,
          price: 100,
          assetPath: 'path',
        );

        // Act & Assert
        expect(item.typeDisplayName, equals('Effect'));
      });

      test('should return correct display name for powerup', () {
        // Arrange
        final item = StoreItem(
          id: 'test',
          name: 'Test',
          type: StoreItemType.powerup,
          price: 100,
          assetPath: 'path',
        );

        // Act & Assert
        expect(item.typeDisplayName, equals('Power-up'));
      });
    });

    group('isFree', () {
      test('should return true for price 0', () {
        // Arrange
        final item = StoreItem(
          id: 'test',
          name: 'Test',
          type: StoreItemType.skin,
          price: 0,
          assetPath: 'path',
        );

        // Act & Assert
        expect(item.isFree, isTrue);
      });

      test('should return false for price greater than 0', () {
        // Arrange
        final item = StoreItem(
          id: 'test',
          name: 'Test',
          type: StoreItemType.skin,
          price: 100,
          assetPath: 'path',
        );

        // Act & Assert
        expect(item.isFree, isFalse);
      });
    });

    group('copyWith', () {
      test('should create copy with updated fields', () {
        // Arrange
        final original = StoreItem(
          id: 'test_id',
          name: 'Original Name',
          type: StoreItemType.skin,
          price: 100,
          assetPath: 'original/path',
          description: 'Original description',
          isAvailable: true,
        );

        // Act
        final copy = original.copyWith(
          name: 'Updated Name',
          price: 200,
          isAvailable: false,
        );

        // Assert
        expect(copy.id, equals('test_id')); // unchanged
        expect(copy.name, equals('Updated Name')); // changed
        expect(copy.type, equals(StoreItemType.skin)); // unchanged
        expect(copy.price, equals(200)); // changed
        expect(copy.assetPath, equals('original/path')); // unchanged
        expect(copy.description, equals('Original description')); // unchanged
        expect(copy.isAvailable, isFalse); // changed
      });

      test('should create identical copy when no parameters provided', () {
        // Arrange
        final original = StoreItem(
          id: 'test_id',
          name: 'Test Name',
          type: StoreItemType.effect,
          price: 150,
          assetPath: 'test/path',
          description: 'Test description',
          isAvailable: false,
        );

        // Act
        final copy = original.copyWith();

        // Assert
        expect(copy.id, equals(original.id));
        expect(copy.name, equals(original.name));
        expect(copy.type, equals(original.type));
        expect(copy.price, equals(original.price));
        expect(copy.assetPath, equals(original.assetPath));
        expect(copy.description, equals(original.description));
        expect(copy.isAvailable, equals(original.isAvailable));
      });
    });

    group('equality', () {
      test('should be equal when all properties match', () {
        // Arrange
        final item1 = StoreItem(
          id: 'test_id',
          name: 'Test Item',
          type: StoreItemType.skin,
          price: 100,
          assetPath: 'test/path',
          description: 'Test description',
          isAvailable: true,
        );

        final item2 = StoreItem(
          id: 'test_id',
          name: 'Test Item',
          type: StoreItemType.skin,
          price: 100,
          assetPath: 'test/path',
          description: 'Test description',
          isAvailable: true,
        );

        // Act & Assert
        expect(item1, equals(item2));
        expect(item1.hashCode, equals(item2.hashCode));
      });

      test('should not be equal when properties differ', () {
        // Arrange
        final item1 = StoreItem(
          id: 'test_id',
          name: 'Test Item',
          type: StoreItemType.skin,
          price: 100,
          assetPath: 'test/path',
        );

        final item2 = StoreItem(
          id: 'test_id',
          name: 'Different Item',
          type: StoreItemType.skin,
          price: 100,
          assetPath: 'test/path',
        );

        // Act & Assert
        expect(item1, isNot(equals(item2)));
      });
    });

    group('toString', () {
      test('should return string representation with all properties', () {
        // Arrange
        final item = StoreItem(
          id: 'test_id',
          name: 'Test Item',
          type: StoreItemType.skin,
          price: 100,
          assetPath: 'test/path',
          description: 'Test description',
          isAvailable: true,
        );

        // Act
        final result = item.toString();

        // Assert
        expect(result, contains('test_id'));
        expect(result, contains('Test Item'));
        expect(result, contains('StoreItemType.skin'));
        expect(result, contains('100'));
        expect(result, contains('test/path'));
        expect(result, contains('Test description'));
        expect(result, contains('true'));
      });
    });
  });
}
