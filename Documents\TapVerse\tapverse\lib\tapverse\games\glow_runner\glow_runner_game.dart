import 'dart:math' as math;
import 'package:flame/components.dart';
import 'package:flame/collisions.dart';
import 'package:flutter/material.dart';
import '../../core/mini_game_base.dart';
import '../../core/hud.dart';
import '../../core/effects.dart';
import '../../core/analytics.dart';
import '../../core/difficulty.dart';

/// GlowRunner - Neon parkour with light trail mechanics
/// Controls: Tap to jump; double-tap for double jump; swipe down for slide
/// Scoring: +1 per 100px distance; +5 per light orb; +10 per perfect landing
class GlowRunnerGame extends MiniGameBase {
  GlowRunnerGame(int seed) : super(modeId: 'glow_runner', sessionSeed: seed) {
    rng = math.Random(seed);
  }

  late math.Random rng;
  late ScoreText scoreText;
  late TextComponent distanceText;
  
  // Game state
  int score = 0;
  double distance = 0;
  double lastScoredDistance = 0;
  
  // Game entities
  late Runner runner;
  late List<Platform> platforms;
  late List<LightOrb> lightOrbs;
  late List<Obstacle> obstacles;
  late LightTrail lightTrail;
  
  // Difficulty curves (from planning document)
  final runSpeedCurve = const CurveParam(start: 250, max: 450, perMinute: 100);
  final gapSizeCurve = const CurveParam(start: 80, max: 150, perMinute: 35);
  
  // Difficulty manager
  late DifficultyManager difficultyManager;
  
  // Level generation
  double nextPlatformX = 800;
  double nextOrbX = 600;

  @override
  Future<void> loadAssets() async {
    // Load any required assets here
  }

  @override
  void setupWorld() {
    camera.viewfinder.visibleGameSize = Vector2(720, 1280);
    
    // Initialize difficulty manager
    difficultyManager = DifficultyManager();
    difficultyManager.addParameter('run_speed', runSpeedCurve);
    difficultyManager.addParameter('gap_size', gapSizeCurve);
    
    // Initialize collections
    platforms = [];
    lightOrbs = [];
    obstacles = [];
    
    // Create game entities
    _createGameEntities();
  }

  void _createGameEntities() {
    // Create runner
    runner = Runner(
      position: Vector2(100, 1000),
      onObstacleHit: _onObstacleHit,
      onLightOrbCollected: _onLightOrbCollected,
      onPerfectLanding: _onPerfectLanding,
      onFall: _onFall,
      onLightOrbRemove: (orb) => lightOrbs.remove(orb),
    );
    add(runner);

    // Create light trail
    lightTrail = LightTrail(runner: runner);
    add(lightTrail);

    // Create starting platform
    final startPlatform = Platform(
      position: Vector2(0, 1100),
      width: 200,
      height: 20,
    );
    platforms.add(startPlatform);
    add(startPlatform);

    // Generate initial level
    _generateInitialLevel();
  }

  void _generateInitialLevel() {
    // Generate starting platforms
    for (int i = 0; i < 10; i++) {
      _generatePlatform();
    }
    
    // Generate starting orbs
    for (int i = 0; i < 5; i++) {
      _generateLightOrb();
    }
  }

  void _generatePlatform() {
    final gapSize = difficultyManager.getValue('gap_size');
    final platformWidth = 100 + rng.nextDouble() * 100;
    final platformHeight = 20.0;
    
    // Vary platform height
    final heightVariation = (rng.nextDouble() - 0.5) * 200;
    final platformY = 1100 + heightVariation;
    
    final platform = Platform(
      position: Vector2(nextPlatformX, platformY),
      width: platformWidth,
      height: platformHeight,
    );
    
    platforms.add(platform);
    add(platform);
    
    // Add obstacles occasionally
    if (rng.nextDouble() < 0.3) {
      _generateObstacle(Vector2(nextPlatformX + platformWidth / 2, platformY - 40));
    }
    
    nextPlatformX += platformWidth + gapSize;
  }

  void _generateObstacle(Vector2 position) {
    final obstacle = Obstacle(
      position: position,
      type: rng.nextBool() ? ObstacleType.spike : ObstacleType.barrier,
    );
    
    obstacles.add(obstacle);
    add(obstacle);
  }

  void _generateLightOrb() {
    final x = nextOrbX + rng.nextDouble() * 200;
    final y = 800 + rng.nextDouble() * 400;
    
    final orb = LightOrb(position: Vector2(x, y));
    lightOrbs.add(orb);
    add(orb);
    
    nextOrbX += 300 + rng.nextDouble() * 200;
  }

  @override
  void setupHUD() {
    // Score display
    scoreText = ScoreText(
      position: Vector2(16, 16),
      textStyle: const TextStyle(
        color: Colors.white,
        fontSize: 24,
        fontWeight: FontWeight.bold,
      ),
    );
    add(scoreText);

    // Distance display
    distanceText = TextComponent(
      text: 'Distance: 0m',
      position: Vector2(16, 50),
      textRenderer: TextPaint(
        style: const TextStyle(
          color: Colors.cyan,
          fontSize: 20,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
    add(distanceText);
  }

  @override
  void onStart() {
    score = 0;
    distance = 0;
    lastScoredDistance = 0;
    
    // Start difficulty progression
    difficultyManager.start();
    
    // Start the game
    startGame();
    
    Analytics.log('glow_runner_start', {'session_seed': sessionSeed});
  }

  @override
  void onGameUpdate(double dt) {
    // Update run speed based on difficulty
    final runSpeed = difficultyManager.getValue('run_speed');
    runner.updateRunSpeed(runSpeed);
    
    // Update distance
    distance += runSpeed * dt;
    distanceText.text = 'Distance: ${(distance / 100).floor()}m';
    
    // Score for distance progress
    if (distance - lastScoredDistance >= 100) {
      lastScoredDistance = distance;
      score += 1;
      addScore(1);
      scoreText.updateScore(score);
    }
    
    // Generate new platforms and orbs
    if (runner.position.x > nextPlatformX - 1000) {
      _generatePlatform();
    }
    
    if (runner.position.x > nextOrbX - 800) {
      _generateLightOrb();
    }
    
    // Clean up old entities
    _cleanupOldEntities();
    
    // Update camera to follow runner
    camera.viewfinder.position = Vector2(runner.position.x + 200, 640);
  }

  void _cleanupOldEntities() {
    // Remove platforms far behind runner
    platforms.removeWhere((platform) {
      if (platform.position.x < runner.position.x - 500) {
        platform.removeFromParent();
        return true;
      }
      return false;
    });
    
    // Remove orbs far behind runner
    lightOrbs.removeWhere((orb) {
      if (orb.position.x < runner.position.x - 500) {
        orb.removeFromParent();
        return true;
      }
      return false;
    });
    
    // Remove obstacles far behind runner
    obstacles.removeWhere((obstacle) {
      if (obstacle.position.x < runner.position.x - 500) {
        obstacle.removeFromParent();
        return true;
      }
      return false;
    });
  }

  // TODO: Implement input handling
  void handleTapDown(Vector2 position) {
    if (isGameEnded) return;

    runner.jump();
  }

  void handleDoubleTap(Vector2 position) {
    if (isGameEnded) return;

    runner.doubleJump();
  }

  @override
  bool onSwipeEnd(Swipe swipe) {
    if (isGameEnded) return false;
    
    if (swipe.isDown) {
      runner.slide();
    }
    
    return true;
  }

  void _onObstacleHit() {
    // Runner hit obstacle - game over
    endGame(success: false, score: score);
    
    Analytics.log('obstacle_hit', {
      'final_score': score,
      'distance': distance,
    });
  }

  void _onLightOrbCollected() {
    score += 5;
    addScore(5);
    scoreText.updateScore(score);
    
    // Visual effects
    add(Effects.glow(runner.position, color: Colors.cyan, radius: 50));
    add(Effects.textPopup(
      runner.position + Vector2(0, -30),
      '+5',
      color: Colors.cyan,
    ));
    
    Analytics.log('light_orb_collected', {
      'score': score,
      'distance': distance,
    });
  }

  void _onPerfectLanding() {
    score += 10;
    addScore(10);
    scoreText.updateScore(score);
    
    // Visual effects
    add(Effects.burst(runner.position + Vector2(0, 25), color: Colors.amber));
    add(Effects.textPopup(
      runner.position + Vector2(0, -30),
      'PERFECT +10',
      color: Colors.amber,
    ));
    
    Analytics.log('perfect_landing', {
      'score': score,
      'distance': distance,
    });
  }

  void _onFall() {
    // Runner fell - game over
    endGame(success: false, score: score);
    
    Analytics.log('runner_fell', {
      'final_score': score,
      'distance': distance,
    });
  }


  @override
  void reportScore(int score) {
    // Integration hook for TapVerse UI
  }

  @override
  void awardTokens(int tokens) {
    // Integration hook for TapVerse token system
  }
}

/// Runner component with parkour mechanics
class Runner extends RectangleComponent with HasCollisionDetection, CollisionCallbacks {
  Runner({
    required super.position,
    required this.onObstacleHit,
    required this.onLightOrbCollected,
    required this.onPerfectLanding,
    required this.onFall,
    required this.onLightOrbRemove,
  }) : super(size: Vector2(30, 30));

  final VoidCallback onObstacleHit;
  final VoidCallback onLightOrbCollected;
  final VoidCallback onPerfectLanding;
  final VoidCallback onFall;
  final Function(LightOrb) onLightOrbRemove;

  Vector2 velocity = Vector2.zero();
  bool isGrounded = false;
  bool canDoubleJump = false;
  bool isSliding = false;
  double slideTimer = 0;
  double runSpeed = 250;
  double coyoteTime = 0;
  
  static const double jumpForce = 600;
  static const double doubleJumpForce = 400;
  static const double gravity = 1500;
  static const double maxCoyoteTime = 0.15;
  static const double slideTime = 0.5;

  @override
  Future<void> onLoad() async {
    add(RectangleHitbox());
    paint = Paint()..color = Colors.white;
  }

  @override
  void update(double dt) {
    super.update(dt);
    
    // Apply gravity
    velocity.y += gravity * dt;
    
    // Horizontal movement
    velocity.x = runSpeed;
    
    // Update coyote time
    if (!isGrounded) {
      coyoteTime += dt;
    } else {
      coyoteTime = 0;
      canDoubleJump = true;
    }
    
    // Update sliding
    if (isSliding) {
      slideTimer -= dt;
      if (slideTimer <= 0) {
        isSliding = false;
        size.y = 30; // Return to normal height
        paint = Paint()..color = Colors.white;
      }
    }
    
    // Update position
    position += velocity * dt;
    
    // Check if fell too far
    if (position.y > 1500) {
      onFall();
    }
    
    // Reset grounded state (will be set by collision)
    isGrounded = false;
  }

  void jump() {
    if ((isGrounded || coyoteTime <= maxCoyoteTime) && !isSliding) {
      velocity.y = -jumpForce;
      coyoteTime = maxCoyoteTime + 0.1; // Prevent double jump
    }
  }

  void doubleJump() {
    if (canDoubleJump && !isGrounded && !isSliding) {
      velocity.y = -doubleJumpForce;
      canDoubleJump = false;
      
      // Visual effect
      add(Effects.burst(position, color: Colors.blue));
    }
  }

  void slide() {
    if (isGrounded && !isSliding) {
      isSliding = true;
      slideTimer = slideTime;
      size.y = 15; // Lower height while sliding
      paint = Paint()..color = Colors.orange;
    }
  }

  void updateRunSpeed(double newRunSpeed) {
    runSpeed = newRunSpeed;
  }

  @override
  bool onCollisionStart(Set<Vector2> intersectionPoints, PositionComponent other) {
    if (other is Platform && velocity.y > 0) {
      // Landing on platform
      velocity.y = 0;
      position.y = other.position.y - size.y;
      isGrounded = true;
      
      // Check for perfect landing (landing near center of platform)
      final platformCenter = other.position.x + other.size.x / 2;
      final runnerCenter = position.x + size.x / 2;
      final distanceFromCenter = (runnerCenter - platformCenter).abs();
      
      if (distanceFromCenter < 20) { // Within 20 pixels of center
        onPerfectLanding();
      }
      
      return true;
    } else if (other is Obstacle && !isSliding) {
      onObstacleHit();
      return true;
    } else if (other is LightOrb) {
      onLightOrbCollected();
      onLightOrbRemove(other);
      other.removeFromParent();
      return true;
    }
    return false;
  }
}

/// Platform component
class Platform extends RectangleComponent with HasCollisionDetection {
  Platform({
    required super.position,
    required double width,
    required double height,
  }) : super(size: Vector2(width, height));

  @override
  Future<void> onLoad() async {
    add(RectangleHitbox());
    paint = Paint()..color = Colors.purple;
  }
}

/// Light orb collectible
class LightOrb extends CircleComponent with HasCollisionDetection {
  LightOrb({required super.position}) : super(radius: 20);

  double pulseTimer = 0;

  @override
  Future<void> onLoad() async {
    add(CircleHitbox());
    paint = Paint()..color = Colors.cyan;
  }

  @override
  void update(double dt) {
    super.update(dt);
    
    // Pulsing effect
    pulseTimer += dt * 3;
    final pulse = (math.sin(pulseTimer) + 1) / 2; // 0 to 1
    paint = Paint()..color = Colors.cyan.withOpacity(0.5 + pulse * 0.5);
  }
}

/// Obstacle types
enum ObstacleType { spike, barrier }

/// Obstacle component
class Obstacle extends RectangleComponent with HasCollisionDetection {
  Obstacle({
    required super.position,
    required this.type,
  }) : super(size: type == ObstacleType.spike ? Vector2(30, 40) : Vector2(20, 60));

  final ObstacleType type;

  @override
  Future<void> onLoad() async {
    add(RectangleHitbox());
    
    switch (type) {
      case ObstacleType.spike:
        paint = Paint()..color = Colors.red;
        break;
      case ObstacleType.barrier:
        paint = Paint()..color = Colors.orange;
        break;
    }
  }
}

/// Light trail effect following the runner
class LightTrail extends Component {
  LightTrail({required this.runner});

  final Runner runner;
  final List<Vector2> trailPoints = [];
  static const int maxTrailLength = 20;

  @override
  void update(double dt) {
    super.update(dt);
    
    // Add current position to trail
    trailPoints.add(runner.position.clone());
    
    // Limit trail length
    if (trailPoints.length > maxTrailLength) {
      trailPoints.removeAt(0);
    }
  }

  @override
  void render(Canvas canvas) {
    if (trailPoints.length < 2) return;
    
    // Draw trail with fading opacity
    for (int i = 1; i < trailPoints.length; i++) {
      final opacity = i / trailPoints.length;
      final paint = Paint()
        ..color = Colors.white.withOpacity(opacity * 0.5)
        ..strokeWidth = 3;
      
      canvas.drawLine(
        trailPoints[i - 1].toOffset(),
        trailPoints[i].toOffset(),
        paint,
      );
    }
  }
}
