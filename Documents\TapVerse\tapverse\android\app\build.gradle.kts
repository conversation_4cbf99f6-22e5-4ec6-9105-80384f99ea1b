plugins {
    id("com.android.application")
    id("kotlin-android")
    // The Flutter Gradle Plugin must be applied after the Android and Kotlin Gradle plugins.
    id("dev.flutter.flutter-gradle-plugin")
    // Add the Google services Gradle plugin
    id("com.google.gms.google-services")
    // Add Firebase Crashlytics plugin
    id("com.google.firebase.crashlytics")
}

android {
    namespace = "luutopia.apps"
    compileSdk = flutter.compileSdkVersion
    ndkVersion = "27.0.12077973"

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_11
        targetCompatibility = JavaVersion.VERSION_11
    }

    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_11.toString()
    }

    defaultConfig {
        applicationId = "luutopia.apps"
        minSdk = 23
        targetSdk = 34
        versionCode = 1
        versionName = "1.0.0"

        // Enable multidex for large apps
        multiDexEnabled = true

        // Proguard configuration
        proguardFiles(getDefaultProguardFile("proguard-android-optimize.txt"), "proguard-rules.pro")
    }

    signingConfigs {
        create("release") {
            // TODO: Configure release signing
            // keyAlias = "your-key-alias"
            // keyPassword = "your-key-password"
            // storeFile = file("path/to/your/keystore.jks")
            // storePassword = "your-store-password"
        }
    }

    buildTypes {
        debug {
            isDebuggable = true
            // Remove applicationIdSuffix to match Firebase config
            // applicationIdSuffix = ".debug"
            versionNameSuffix = "-debug"
        }

        release {
            isMinifyEnabled = true
            isShrinkResources = true
            isDebuggable = false

            // TODO: Uncomment when you have a release keystore
            // signingConfig = signingConfigs.getByName("release")
            signingConfig = signingConfigs.getByName("debug")

            proguardFiles(getDefaultProguardFile("proguard-android-optimize.txt"), "proguard-rules.pro")
        }
    }
}

flutter {
    source = "../.."
}

dependencies {
    // Import the Firebase BoM
    implementation(platform("com.google.firebase:firebase-bom:34.0.0"))

    // Multidex support
    implementation("androidx.multidex:multidex:2.0.1")
}
