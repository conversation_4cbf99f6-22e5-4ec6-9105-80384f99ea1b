import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/providers/app_providers.dart';

class GameCustomizationWidget extends ConsumerWidget {
  final String gameId;
  final Widget child;

  const GameCustomizationWidget({
    super.key,
    required this.gameId,
    required this.child,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final equippedTheme = ref.watch(equippedThemeProvider);
    
    return equippedTheme.when(
      data: (themeId) {
        if (themeId != null) {
          return _buildThemedChild(context, themeId);
        }
        return child;
      },
      loading: () => child,
      error: (_, __) => child,
    );
  }

  Widget _buildThemedChild(BuildContext context, String themeId) {
    // Apply theme based on equipped theme item
    return Container(
      decoration: _getThemeDecoration(themeId),
      child: child,
    );
  }

  BoxDecoration? _getThemeDecoration(String themeId) {
    // Map theme IDs to decorations
    switch (themeId) {
      case 'item_10': // Neon Theme
        return BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Colors.purple.withValues(alpha: 0.3),
              Colors.cyan.withValues(alpha: 0.3),
              Colors.pink.withValues(alpha: 0.3),
            ],
          ),
        );
      case 'item_11': // Space Theme
        return BoxDecoration(
          gradient: RadialGradient(
            center: Alignment.center,
            colors: [
              Colors.indigo.withValues(alpha: 0.4),
              Colors.black.withValues(alpha: 0.8),
            ],
          ),
        );
      case 'item_12': // Ocean Theme
        return BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Colors.lightBlue.withValues(alpha: 0.3),
              Colors.blue.withValues(alpha: 0.5),
              Colors.indigo.withValues(alpha: 0.7),
            ],
          ),
        );
      case 'item_13': // Forest Theme
        return BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Colors.lightGreen.withValues(alpha: 0.3),
              Colors.green.withValues(alpha: 0.5),
              Colors.brown.withValues(alpha: 0.3),
            ],
          ),
        );
      default:
        return null;
    }
  }
}

class GameBallCustomization extends ConsumerWidget {
  final Widget ballWidget;
  final double size;

  const GameBallCustomization({
    super.key,
    required this.ballWidget,
    this.size = 20.0,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final equippedSkin = ref.watch(equippedSkinProvider);
    final equippedEffect = ref.watch(equippedEffectProvider);

    return equippedSkin.when(
      data: (skinId) {
        Widget customizedBall = _buildCustomizedBall(skinId);
        
        // Add effects if equipped
        return equippedEffect.when(
          data: (effectId) {
            if (effectId != null) {
              return _addEffects(customizedBall, effectId);
            }
            return customizedBall;
          },
          loading: () => customizedBall,
          error: (_, __) => customizedBall,
        );
      },
      loading: () => ballWidget,
      error: (_, __) => ballWidget,
    );
  }

  Widget _buildCustomizedBall(String? skinId) {
    if (skinId == null) return ballWidget;

    // Map skin IDs to customized ball widgets
    switch (skinId) {
      case 'item_1': // Golden Ball
        return Container(
          width: size,
          height: size,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            gradient: RadialGradient(
              colors: [
                Colors.yellow.shade300,
                Colors.amber.shade600,
                Colors.orange.shade800,
              ],
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.yellow.withValues(alpha: 0.5),
                blurRadius: 8,
                spreadRadius: 2,
              ),
            ],
          ),
        );
      case 'item_2': // Rainbow Ball
        return Container(
          width: size,
          height: size,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            gradient: SweepGradient(
              colors: [
                Colors.red,
                Colors.orange,
                Colors.yellow,
                Colors.green,
                Colors.blue,
                Colors.indigo,
                Colors.purple,
                Colors.red,
              ],
            ),
          ),
        );
      case 'item_3': // Crystal Ball
        return Container(
          width: size,
          height: size,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            gradient: RadialGradient(
              colors: [
                Colors.white.withValues(alpha: 0.8),
                Colors.cyan.withValues(alpha: 0.6),
                Colors.blue.withValues(alpha: 0.4),
              ],
            ),
            border: Border.all(
              color: Colors.cyan.withValues(alpha: 0.8),
              width: 2,
            ),
          ),
        );
      case 'item_4': // Fire Ball
        return Container(
          width: size,
          height: size,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            gradient: RadialGradient(
              colors: [
                Colors.yellow,
                Colors.orange,
                Colors.red.shade800,
              ],
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.red.withValues(alpha: 0.6),
                blurRadius: 10,
                spreadRadius: 3,
              ),
            ],
          ),
        );
      case 'item_5': // Ice Ball
        return Container(
          width: size,
          height: size,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            gradient: RadialGradient(
              colors: [
                Colors.white,
                Colors.lightBlue.shade200,
                Colors.blue.shade400,
              ],
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.blue.withValues(alpha: 0.4),
                blurRadius: 8,
                spreadRadius: 2,
              ),
            ],
          ),
        );
      default:
        return ballWidget;
    }
  }

  Widget _addEffects(Widget ball, String effectId) {
    // Add particle effects or animations based on effect ID
    switch (effectId) {
      case 'item_6': // Sparkle Trail
        return Stack(
          alignment: Alignment.center,
          children: [
            ball,
            // Add sparkle effect overlay
            Container(
              width: size * 1.5,
              height: size * 1.5,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                gradient: RadialGradient(
                  colors: [
                    Colors.transparent,
                    Colors.yellow.withValues(alpha: 0.3),
                    Colors.transparent,
                  ],
                ),
              ),
            ),
          ],
        );
      case 'item_7': // Lightning Strike
        return Stack(
          alignment: Alignment.center,
          children: [
            ball,
            // Add lightning effect
            Container(
              width: size * 1.3,
              height: size * 1.3,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(
                  color: Colors.cyan.withValues(alpha: 0.8),
                  width: 2,
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.cyan.withValues(alpha: 0.5),
                    blurRadius: 6,
                  ),
                ],
              ),
            ),
          ],
        );
      case 'item_9': // Magic Aura
        return Stack(
          alignment: Alignment.center,
          children: [
            ball,
            // Add magic aura effect
            Container(
              width: size * 1.4,
              height: size * 1.4,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                gradient: RadialGradient(
                  colors: [
                    Colors.transparent,
                    Colors.purple.withValues(alpha: 0.4),
                    Colors.transparent,
                  ],
                ),
              ),
            ),
          ],
        );
      default:
        return ball;
    }
  }
}
