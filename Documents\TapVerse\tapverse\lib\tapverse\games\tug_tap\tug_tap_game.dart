import 'dart:math' as math;
import 'package:flame/components.dart';
import 'package:flutter/material.dart';
import '../../core/mini_game_base.dart';
import '../../core/hud.dart';
import '../../core/effects.dart';
import '../../core/analytics.dart';

/// TugTap - Rhythm tug-of-war with timing
/// Controls: Tap in rhythm to pull rope; miss timing = opponent gains
/// Scoring: +1 per successful tap; +10 per rhythm streak >=10
class TugTapGame extends MiniGameBase {
  TugTapGame(int seed) : super(modeId: 'tug_tap', sessionSeed: seed) {
    rng = math.Random(seed);
  }

  late math.Random rng;
  late ScoreText scoreText;
  late ProgressBar tugBar;
  
  // Game state
  int score = 0;
  double tugPosition = 0; // -100 to 100, 0 is center
  int rhythmStreak = 0;
  
  // Rhythm system
  double bpm = 120;
  double beatTimer = 0;
  double beatInterval = 0;
  bool isOnBeat = false;
  double beatWindow = 0.2; // 200ms window
  
  // Visual elements
  late RhythmIndicator rhythmIndicator;
  late TugRope tugRope;

  @override
  Future<void> loadAssets() async {}

  @override
  void setupWorld() {
    camera.viewfinder.visibleGameSize = Vector2(720, 1280);
    
    beatInterval = 60.0 / bpm; // seconds per beat
    
    _createGameEntities();
  }

  void _createGameEntities() {
    // Create rhythm indicator
    rhythmIndicator = RhythmIndicator(
      position: Vector2(360, 200),
      beatInterval: beatInterval,
    );
    add(rhythmIndicator);

    // Create tug rope
    tugRope = TugRope(
      position: Vector2(360, 640),
      tugPosition: tugPosition,
    );
    add(tugRope);
  }

  @override
  void setupHUD() {
    scoreText = ScoreText(position: Vector2(16, 16));
    add(scoreText);

    tugBar = ProgressBar(
      position: Vector2(160, 50),
      size: Vector2(400, 20),
      backgroundColor: Colors.red,
      foregroundColor: Colors.blue,
    );
    add(tugBar);
  }

  @override
  void onStart() {
    score = 0;
    tugPosition = 0;
    rhythmStreak = 0;
    beatTimer = 0;
    
    startGame();
    
    Analytics.log('tug_tap_start', {'session_seed': sessionSeed, 'bpm': bpm});
  }

  @override
  void onGameUpdate(double dt) {
    // Update beat timer
    beatTimer += dt;
    
    // Check if we're on beat
    final beatProgress = (beatTimer % beatInterval) / beatInterval;
    isOnBeat = beatProgress < (beatWindow / beatInterval) || 
               beatProgress > (1 - beatWindow / beatInterval);
    
    // Update rhythm indicator
    rhythmIndicator.updateBeat(beatProgress, isOnBeat);
    
    // Opponent pulls gradually
    tugPosition -= 15 * dt; // Opponent strength
    
    // Update tug bar (convert -100 to 100 range to 0 to 1)
    tugBar.updateProgress((tugPosition + 100) / 200);
    
    // Update rope visual
    tugRope.updateTugPosition(tugPosition);
    
    // Check win/lose conditions
    if (tugPosition >= 100) {
      _playerWins();
    } else if (tugPosition <= -100) {
      _playerLoses();
    }
  }

  // TODO: Implement input handling
  void handleTapDown(Vector2 position) {
    if (isGameEnded) return;

    if (isOnBeat) {
      _successfulTap();
    } else {
      _missedTap();
    }
  }

  void _successfulTap() {
    rhythmStreak++;
    score += 1;
    addScore(1);
    scoreText.updateScore(score);
    
    // Pull rope toward player
    tugPosition += 8;
    tugPosition = tugPosition.clamp(-100, 100);
    
    // Rhythm streak bonus
    if (rhythmStreak >= 10 && rhythmStreak % 10 == 0) {
      score += 10;
      addScore(10);
      scoreText.updateScore(score);
      
      add(Effects.textPopup(
        Vector2(360, 400),
        'RHYTHM STREAK +10',
        color: Colors.amber,
      ));
    }
    
    // Visual effects
    add(Effects.burst(Vector2(360, 640), color: Colors.green));
    
    Analytics.log('successful_tap', {
      'rhythm_streak': rhythmStreak,
      'tug_position': tugPosition,
    });
  }

  void _missedTap() {
    rhythmStreak = 0;
    
    // Opponent gains advantage
    tugPosition -= 5;
    tugPosition = tugPosition.clamp(-100, 100);
    
    // Visual effects
    add(Effects.burst(Vector2(360, 640), color: Colors.red));
    
    Analytics.log('missed_tap', {
      'tug_position': tugPosition,
    });
  }

  void _playerWins() {
    endGame(success: true, score: score);
    
    Analytics.log('player_wins', {
      'final_score': score,
      'max_rhythm_streak': rhythmStreak,
    });
  }

  void _playerLoses() {
    endGame(success: false, score: score);
    
    Analytics.log('player_loses', {
      'final_score': score,
      'max_rhythm_streak': rhythmStreak,
    });
  }


  @override
  void reportScore(int score) {}

  @override
  void awardTokens(int tokens) {}
}

class RhythmIndicator extends PositionComponent {
  RhythmIndicator({
    required super.position,
    required this.beatInterval,
  }) : super(size: Vector2(200, 50));

  final double beatInterval;
  double beatProgress = 0;
  bool isOnBeat = false;

  @override
  void render(Canvas canvas) {
    // Draw background
    final bgPaint = Paint()..color = Colors.grey.withOpacity(0.3);
    canvas.drawRect(size.toRect(), bgPaint);
    
    // Draw beat indicator
    final indicatorX = beatProgress * size.x;
    final indicatorPaint = Paint()..color = isOnBeat ? Colors.green : Colors.white;
    canvas.drawRect(
      Rect.fromLTWH(indicatorX - 2, 0, 4, size.y),
      indicatorPaint,
    );
    
    // Draw beat zones
    final zonePaint = Paint()..color = Colors.green.withOpacity(0.3);
    final zoneWidth = size.x * 0.1; // 10% of total width
    
    // Left zone (start of beat)
    canvas.drawRect(
      Rect.fromLTWH(0, 0, zoneWidth, size.y),
      zonePaint,
    );
    
    // Right zone (end of beat)
    canvas.drawRect(
      Rect.fromLTWH(size.x - zoneWidth, 0, zoneWidth, size.y),
      zonePaint,
    );
  }

  void updateBeat(double progress, bool onBeat) {
    beatProgress = progress;
    isOnBeat = onBeat;
  }
}

class TugRope extends PositionComponent {
  TugRope({
    required super.position,
    required this.tugPosition,
  }) : super(size: Vector2(400, 20));

  double tugPosition;

  @override
  void render(Canvas canvas) {
    // Draw rope
    final ropePaint = Paint()
      ..color = Colors.brown
      ..strokeWidth = 8;
    
    canvas.drawLine(
      Offset(0, size.y / 2),
      Offset(size.x, size.y / 2),
      ropePaint,
    );
    
    // Draw center marker
    final centerPaint = Paint()..color = Colors.red;
    canvas.drawRect(
      Rect.fromCenter(
        center: Offset(size.x / 2, size.y / 2),
        width: 4,
        height: size.y,
      ),
      centerPaint,
    );
    
    // Draw tug indicator
    final tugX = (size.x / 2) + (tugPosition / 100) * (size.x / 4);
    final tugPaint = Paint()..color = Colors.yellow;
    canvas.drawCircle(
      Offset(tugX, size.y / 2),
      8,
      tugPaint,
    );
  }

  void updateTugPosition(double newPosition) {
    tugPosition = newPosition;
  }
}
