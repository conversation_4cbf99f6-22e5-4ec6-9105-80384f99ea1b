import 'dart:math';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../../../core/models/game_state.dart';
import '../../../core/providers/game_providers.dart';
import '../../../core/services/game_feedback_service.dart';
import '../../../shared/widgets/base_game_widget.dart';

class DartDashGame extends BaseGameWidget {
  static const GameConfig gameConfig = GameConfig(
    gameId: 'dart_dash',
    gameName: 'DartDash',
    scoreMultiplier: 2,
    hasLives: false,
    hasLevels: false,
    hasTimer: false,
  );

  const DartDashGame({
    super.key,
    super.onGameComplete,
    super.onGameExit,
  }) : super(config: gameConfig);

  @override
  Widget buildGameContent(BuildContext context, WidgetRef ref, GameState gameState) {
    return const _DartDashGameContent();
  }
}

class Dart {
  Offset position;
  Offset velocity;
  bool isFlying;
  bool hasHit;

  Dart({
    required this.position,
    required this.velocity,
    this.isFlying = false,
    this.hasHit = false,
  });
}

class _DartDashGameContent extends ConsumerStatefulWidget {
  const _DartDashGameContent();

  @override
  ConsumerState<_DartDashGameContent> createState() => _DartDashGameContentState();
}

class _DartDashGameContentState extends ConsumerState<_DartDashGameContent>
    with TickerProviderStateMixin {
  
  // Game state
  late AnimationController _throwController;
  late AnimationController _hitController;
  late AnimationController _targetController;
  
  // Dart state
  Dart? _dart;
  Offset? _aimStart;
  Offset? _aimEnd;
  bool _isAiming = false;
  
  // Target state
  final Offset _targetCenter = const Offset(200, 150);
  final double _targetRadius = 80;
  final List<double> _ringRadii = [15, 30, 45, 60, 75];
  final List<int> _ringScores = [100, 80, 60, 40, 20];
  final List<Color> _ringColors = [
    Colors.red,
    Colors.white,
    Colors.blue,
    Colors.white,
    Colors.green,
  ];
  
  // Game mechanics
  int _dartsThrown = 0;
  final int _maxDarts = 10;
  bool _showHitEffect = false;
  Offset? _hitPosition;
  int _lastScore = 0;
  
  // Constants
  static const double _gameWidth = 400;
  static const double _gameHeight = 600;
  static const double _dartSize = 20;
  static const double _throwLineY = 500;

  @override
  void initState() {
    super.initState();
    
    _throwController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    
    _hitController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );
    
    _targetController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );
    
    // Start target animation
    _targetController.repeat();
  }

  @override
  void dispose() {
    _throwController.dispose();
    _hitController.dispose();
    _targetController.dispose();
    super.dispose();
  }

  void _onPanStart(DragStartDetails details) {
    final gameState = ref.read(gameStateProvider(DartDashGame.gameConfig));
    if (!gameState.isPlaying || _dart?.isFlying == true || _dartsThrown >= _maxDarts) return;
    
    setState(() {
      _aimStart = details.localPosition;
      _isAiming = true;
    });
  }

  void _onPanUpdate(DragUpdateDetails details) {
    if (!_isAiming) return;
    
    setState(() {
      _aimEnd = details.localPosition;
    });
  }

  void _onPanEnd(DragEndDetails details) {
    if (!_isAiming || _aimStart == null || _aimEnd == null) return;
    
    final gameState = ref.read(gameStateProvider(DartDashGame.gameConfig));
    if (!gameState.isPlaying) return;
    
    _throwDart();
  }

  void _throwDart() {
    if (_aimStart == null || _aimEnd == null) return;
    
    // Calculate throw velocity based on aim direction and distance
    final aimVector = _aimEnd! - _aimStart!;
    final throwForce = aimVector.distance.clamp(50, 300);
    final direction = aimVector / aimVector.distance;
    
    setState(() {
      _dart = Dart(
        position: Offset(_gameWidth / 2, _throwLineY),
        velocity: direction * throwForce.toDouble() * -1, // Negative to throw upward
        isFlying: true,
      );
      _isAiming = false;
      _aimStart = null;
      _aimEnd = null;
      _dartsThrown++;
    });
    
    // Trigger feedback
    ref.read(gameFeedbackServiceProvider).triggerFeedback(GameEvent.buttonPress);
    
    // Play throw animation
    _throwController.forward().then((_) => _throwController.reset());
    
    // Start dart physics
    _startDartPhysics();
  }

  void _startDartPhysics() {
    const frameRate = 60;
    const frameDuration = Duration(milliseconds: 1000 ~/ frameRate);
    const deltaTime = 1.0 / frameRate;
    const gravity = 300; // Pixels per second squared
    
    void updateDart() {
      if (_dart == null || !_dart!.isFlying || !mounted) return;
      
      setState(() {
        // Apply gravity
        _dart!.velocity = Offset(
          _dart!.velocity.dx,
          _dart!.velocity.dy + gravity * deltaTime,
        );
        
        // Update position
        _dart!.position = Offset(
          _dart!.position.dx + _dart!.velocity.dx * deltaTime,
          _dart!.position.dy + _dart!.velocity.dy * deltaTime,
        );
        
        // Check target collision
        final distanceToTarget = (_dart!.position - _targetCenter).distance;
        if (distanceToTarget <= _targetRadius + _dartSize / 2 && !_dart!.hasHit) {
          _hitTarget();
          return;
        }
        
        // Check if dart went off screen or hit ground
        if (_dart!.position.dy > _gameHeight ||
            _dart!.position.dx < 0 ||
            _dart!.position.dx > _gameWidth) {
          _dart!.isFlying = false;
          _checkGameEnd();
          return;
        }
      });
      
      if (_dart!.isFlying) {
        Future.delayed(frameDuration, updateDart);
      }
    }
    
    updateDart();
  }

  void _hitTarget() {
    if (_dart == null) return;
    
    setState(() {
      _dart!.isFlying = false;
      _dart!.hasHit = true;
      _hitPosition = _dart!.position;
      _showHitEffect = true;
    });
    
    // Calculate score based on distance from center
    final distanceFromCenter = (_dart!.position - _targetCenter).distance;
    _lastScore = _calculateScore(distanceFromCenter);
    
    // Add score
    final gameNotifier = ref.read(gameStateProvider(DartDashGame.gameConfig).notifier);
    gameNotifier.addScore(_lastScore);
    
    // Trigger feedback based on score
    if (_lastScore >= 80) {
      ref.read(gameFeedbackServiceProvider).triggerComboFeedback(3);
    } else {
      ref.read(gameFeedbackServiceProvider).triggerFeedback(GameEvent.scoreIncrease);
    }
    
    // Play hit animation
    _hitController.forward().then((_) {
      _hitController.reset();
      setState(() {
        _showHitEffect = false;
      });
      _checkGameEnd();
    });
  }

  int _calculateScore(double distanceFromCenter) {
    for (int i = 0; i < _ringRadii.length; i++) {
      if (distanceFromCenter <= _ringRadii[i]) {
        return _ringScores[i];
      }
    }
    return 0; // Outside all rings
  }

  void _checkGameEnd() {
    if (_dartsThrown >= _maxDarts) {
      final gameNotifier = ref.read(gameStateProvider(DartDashGame.gameConfig).notifier);
      gameNotifier.endGame(reason: 'All darts thrown');
    }
  }

  @override
  Widget build(BuildContext context) {
    final gameState = ref.watch(gameStateProvider(DartDashGame.gameConfig));
    
    return Container(
      width: _gameWidth,
      height: _gameHeight,
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [Color(0xFF2c3e50), Color(0xFF34495e)],
        ),
      ),
      child: GestureDetector(
        onPanStart: _onPanStart,
        onPanUpdate: _onPanUpdate,
        onPanEnd: _onPanEnd,
        child: Stack(
          children: [
            // Background
            _buildBackground(),
            
            // Target
            _buildTarget(),
            
            // Dart
            if (_dart != null) _buildDart(),
            
            // Aim line
            if (_isAiming && _aimStart != null && _aimEnd != null) _buildAimLine(),
            
            // Hit effect
            if (_showHitEffect && _hitPosition != null) _buildHitEffect(),
            
            // Throw line
            _buildThrowLine(),
            
            // Game info
            _buildGameInfo(gameState),
            
            // Instructions
            if (!gameState.isPlaying) _buildInstructions(),
          ],
        ),
      ),
    );
  }

  Widget _buildBackground() {
    return Positioned.fill(
      child: CustomPaint(
        painter: _BackgroundPainter(),
      ),
    );
  }

  Widget _buildTarget() {
    return Positioned(
      left: _targetCenter.dx - _targetRadius,
      top: _targetCenter.dy - _targetRadius,
      child: RotationTransition(
        turns: _targetController,
        child: SizedBox(
          width: _targetRadius * 2,
          height: _targetRadius * 2,
          child: CustomPaint(
            painter: _TargetPainter(_ringRadii, _ringColors),
          ),
        ),
      ),
    );
  }

  Widget _buildDart() {
    final angle = atan2(_dart!.velocity.dy, _dart!.velocity.dx);
    
    return Positioned(
      left: _dart!.position.dx - _dartSize / 2,
      top: _dart!.position.dy - _dartSize / 2,
      child: Transform.rotate(
        angle: angle,
        child: Container(
          width: _dartSize,
          height: _dartSize,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [Colors.grey[300]!, Colors.grey[600]!],
            ),
            borderRadius: BorderRadius.circular(2),
          ),
          child: const Center(
            child: Text(
              '🎯',
              style: TextStyle(fontSize: 12),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildAimLine() {
    return CustomPaint(
      painter: _AimLinePainter(_aimStart!, _aimEnd!),
    );
  }

  Widget _buildHitEffect() {
    return Positioned(
      left: _hitPosition!.dx - 30,
      top: _hitPosition!.dy - 30,
      child: ScaleTransition(
        scale: _hitController,
        child: Container(
          width: 60,
          height: 60,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            gradient: RadialGradient(
              colors: [Colors.yellow, Colors.orange, Colors.transparent],
              stops: const [0.0, 0.5, 1.0],
            ),
          ),
          child: Center(
            child: Text(
              '+$_lastScore',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildThrowLine() {
    return Positioned(
      bottom: _gameHeight - _throwLineY,
      left: 0,
      right: 0,
      child: Container(
        height: 3,
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.5),
          boxShadow: [
            BoxShadow(
              color: Colors.white.withOpacity(0.3),
              blurRadius: 4,
              spreadRadius: 1,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildGameInfo(GameState gameState) {
    return Positioned(
      top: 30,
      left: 20,
      right: 20,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.black54,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              children: [
                const Text(
                  'Score',
                  style: TextStyle(color: Colors.white, fontSize: 12),
                ),
                Text(
                  '${gameState.score}',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.black54,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              children: [
                const Text(
                  'Darts',
                  style: TextStyle(color: Colors.white, fontSize: 12),
                ),
                Text(
                  '${_maxDarts - _dartsThrown}',
                  style: TextStyle(
                    color: _dartsThrown >= _maxDarts - 2 ? Colors.red : Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInstructions() {
    return Positioned(
      bottom: 50,
      left: 0,
      right: 0,
      child: Center(
        child: Container(
          padding: EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.black54,
            borderRadius: BorderRadius.all(Radius.circular(12)),
          ),
          child: Text(
            'Drag to aim and release to throw darts!\nHit the center for maximum points.\nCloser to center = higher score!',
            textAlign: TextAlign.center,
            style: TextStyle(
              color: Colors.white,
              fontSize: 16,
            ),
          ),
        ),
      ),
    );
  }
}

class _BackgroundPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    // Draw dartboard wall pattern
    final paint = Paint()
      ..color = Colors.brown[800]!
      ..style = PaintingStyle.fill;
    
    // Draw wood planks
    for (double y = 0; y < size.height; y += 40) {
      canvas.drawRect(
        Rect.fromLTWH(0, y, size.width, 35),
        paint,
      );
      
      // Draw wood grain lines
      final grainPaint = Paint()
        ..color = Colors.brown[900]!
        ..strokeWidth = 1;
      
      for (double x = 0; x < size.width; x += 20) {
        canvas.drawLine(
          Offset(x, y),
          Offset(x, y + 35),
          grainPaint,
        );
      }
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class _TargetPainter extends CustomPainter {
  final List<double> ringRadii;
  final List<Color> ringColors;

  _TargetPainter(this.ringRadii, this.ringColors);

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    
    // Draw rings from outside to inside
    for (int i = ringRadii.length - 1; i >= 0; i--) {
      final paint = Paint()
        ..color = ringColors[i]
        ..style = PaintingStyle.fill;
      
      canvas.drawCircle(center, ringRadii[i], paint);
      
      // Draw ring border
      final borderPaint = Paint()
        ..color = Colors.black
        ..style = PaintingStyle.stroke
        ..strokeWidth = 2;
      
      canvas.drawCircle(center, ringRadii[i], borderPaint);
    }
    
    // Draw center dot
    final centerPaint = Paint()
      ..color = Colors.black
      ..style = PaintingStyle.fill;
    
    canvas.drawCircle(center, 3, centerPaint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class _AimLinePainter extends CustomPainter {
  final Offset start;
  final Offset end;

  _AimLinePainter(this.start, this.end);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.red
      ..strokeWidth = 3
      ..style = PaintingStyle.stroke;
    
    // Draw aim line
    canvas.drawLine(start, end, paint);
    
    // Draw arrow at end
    final direction = (end - start).direction;
    final arrowLength = 15;
    final arrowAngle = pi / 6;
    
    final arrowPoint1 = end + Offset(
      cos(direction + pi - arrowAngle) * arrowLength,
      sin(direction + pi - arrowAngle) * arrowLength,
    );
    
    final arrowPoint2 = end + Offset(
      cos(direction + pi + arrowAngle) * arrowLength,
      sin(direction + pi + arrowAngle) * arrowLength,
    );
    
    canvas.drawLine(end, arrowPoint1, paint);
    canvas.drawLine(end, arrowPoint2, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
