import 'dart:math';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/models/game_state.dart';
import '../../../core/providers/game_providers.dart';
import '../../../core/services/game_feedback_service.dart';
import '../../../shared/widgets/base_game_widget.dart';

class SmashWallGame extends TapVerseBaseGame {
  static const GameConfig gameConfig = GameConfig(
    gameId: 'smash_wall',
    gameName: 'SmashWall',
    scoreMultiplier: 2,
    hasLives: false,
    hasLevels: false,
    hasTimer: false,
  );

  const SmashWallGame({
    super.key,
    super.onGameComplete,
    super.onGameExit,
  }) : super(config: gameConfig);

  @override
  Widget buildGameContentWithWalls(BuildContext context, WidgetRef ref, GameState gameState) {
    return const _SmashWallGameContent();
  }

  @override
  void startGame(WidgetRef ref) {
    // Game-specific startup logic will be handled by _SmashWallGameContent
  }

  @override
  void resetGame(WidgetRef ref) {
    // Game-specific reset logic will be handled by _SmashWallGameContent
  }

  @override
  void disposeGame(WidgetRef ref) {
    // Clean up any game-specific resources
  }
}

class Brick {
  final int row;
  final int col;
  bool isDestroyed;
  final Color color;
  final int points;

  Brick({
    required this.row,
    required this.col,
    this.isDestroyed = false,
    required this.color,
    required this.points,
  });
}

class _SmashWallGameContent extends ConsumerStatefulWidget {
  const _SmashWallGameContent();

  @override
  ConsumerState<_SmashWallGameContent> createState() => _SmashWallGameContentState();
}

class _SmashWallGameContentState extends ConsumerState<_SmashWallGameContent>
    with TickerProviderStateMixin {
  
  // Game state
  late AnimationController _ballController;
  late AnimationController _paddleController;
  late AnimationController _explosionController;
  
  // Ball physics
  Offset _ballPosition = const Offset(200, 400);
  Offset _ballVelocity = const Offset(150, -200);
  final double _ballSize = 16;
  
  // Paddle state
  double _paddleX = 150;
  final double _paddleY = 520;
  final double _paddleWidth = 100;
  final double _paddleHeight = 15;
  
  // Bricks
  List<List<Brick?>> _bricks = [];
  final int _brickRows = 8;
  final int _brickCols = 10;
  final double _brickWidth = 36;
  final double _brickHeight = 20;
  
  // Game mechanics
  Offset? _explosionPosition;
  bool _showExplosion = false;
  
  // Constants
  static const double _gameWidth = 400;
  static const double _gameHeight = 600;

  @override
  void initState() {
    super.initState();
    
    _ballController = AnimationController(
      duration: const Duration(milliseconds: 100),
      vsync: this,
    );
    
    _paddleController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );
    
    _explosionController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );
    
    _initializeBricks();
    _startPhysicsLoop();
  }

  @override
  void dispose() {
    _ballController.dispose();
    _paddleController.dispose();
    _explosionController.dispose();
    super.dispose();
  }

  void _initializeBricks() {
    _bricks = List.generate(_brickRows, (row) {
      return List.generate(_brickCols, (col) {
        final colors = [
          Colors.red,
          Colors.orange,
          Colors.yellow,
          Colors.green,
          Colors.blue,
          Colors.purple,
        ];
        final color = colors[row % colors.length];
        final points = (_brickRows - row) * 10; // Higher rows worth more points
        
        return Brick(
          row: row,
          col: col,
          color: color,
          points: points,
        );
      });
    });
  }

  void _startPhysicsLoop() {
    const frameRate = 60;
    const frameDuration = Duration(milliseconds: 1000 ~/ frameRate);
    
    void updatePhysics() {
      if (!mounted) return;
      
      final gameState = ref.read(gameStateProvider(SmashWallGame.gameConfig));
      if (!gameState.isPlaying) {
        Future.delayed(frameDuration, updatePhysics);
        return;
      }
      
      setState(() {
        // Update ball position
        _ballPosition = Offset(
          _ballPosition.dx + _ballVelocity.dx / frameRate,
          _ballPosition.dy + _ballVelocity.dy / frameRate,
        );
        
        // Check collisions
        _checkWallCollisions();
        _checkPaddleCollision();
        _checkBrickCollisions();
        
        // Check win condition
        if (_allBricksDestroyed()) {
          final gameNotifier = ref.read(gameStateProvider(SmashWallGame.gameConfig).notifier);
          gameNotifier.endGame(reason: 'All bricks destroyed - You Win!');
        }
        
        // Check lose condition
        if (_ballPosition.dy > _gameHeight + 50) {
          final gameNotifier = ref.read(gameStateProvider(SmashWallGame.gameConfig).notifier);
          gameNotifier.endGame(reason: 'Ball fell off screen');
        }
      });
      
      Future.delayed(frameDuration, updatePhysics);
    }
    
    updatePhysics();
  }

  void _checkWallCollisions() {
    final collisionResult = TapVerseWallCollision.checkCircularCollision(
      position: _ballPosition,
      velocity: _ballVelocity,
      radius: _ballSize / 2,
      gameArea: Size(_gameWidth, _gameHeight),
    );

    if (collisionResult.collided) {
      _ballPosition = collisionResult.position;
      _ballVelocity = collisionResult.velocity;
      _triggerBounceEffect();
    }
  }

  void _checkPaddleCollision() {
    final ballLeft = _ballPosition.dx - _ballSize / 2;
    final ballRight = _ballPosition.dx + _ballSize / 2;
    final ballTop = _ballPosition.dy - _ballSize / 2;
    final ballBottom = _ballPosition.dy + _ballSize / 2;
    
    final paddleLeft = _paddleX;
    final paddleRight = _paddleX + _paddleWidth;
    final paddleTop = _paddleY;
    final paddleBottom = _paddleY + _paddleHeight;
    
    if (ballRight >= paddleLeft &&
        ballLeft <= paddleRight &&
        ballBottom >= paddleTop &&
        ballTop <= paddleBottom &&
        _ballVelocity.dy > 0) {
      
      // Calculate hit position for angle
      final hitPosition = (_ballPosition.dx - _paddleX) / _paddleWidth;
      final angle = (hitPosition - 0.5) * pi / 3;
      
      final speed = sqrt(_ballVelocity.dx * _ballVelocity.dx + _ballVelocity.dy * _ballVelocity.dy);
      _ballVelocity = Offset(
        sin(angle) * speed,
        -cos(angle) * speed,
      );
      
      _ballPosition = Offset(_ballPosition.dx, paddleTop - _ballSize / 2);
      
      ref.read(gameFeedbackServiceProvider).triggerFeedback(GameEvent.targetHit);
      _paddleController.forward().then((_) => _paddleController.reset());
    }
  }

  void _checkBrickCollisions() {
    final ballLeft = _ballPosition.dx - _ballSize / 2;
    final ballRight = _ballPosition.dx + _ballSize / 2;
    final ballTop = _ballPosition.dy - _ballSize / 2;
    final ballBottom = _ballPosition.dy + _ballSize / 2;
    
    for (int row = 0; row < _brickRows; row++) {
      for (int col = 0; col < _brickCols; col++) {
        final brick = _bricks[row][col];
        if (brick == null || brick.isDestroyed) continue;
        
        final playableOffset = TapVerseWallCollision.getPlayableAreaOffset();
        final brickLeft = playableOffset.dx + col * _brickWidth;
        final brickRight = brickLeft + _brickWidth;
        final brickTop = playableOffset.dy + 50 + row * _brickHeight;
        final brickBottom = brickTop + _brickHeight;
        
        if (ballRight >= brickLeft &&
            ballLeft <= brickRight &&
            ballBottom >= brickTop &&
            ballTop <= brickBottom) {
          
          // Destroy brick
          brick.isDestroyed = true;
          
          // Add score
          final gameNotifier = ref.read(gameStateProvider(SmashWallGame.gameConfig).notifier);
          gameNotifier.addScore(brick.points);
          
          // Determine bounce direction
          final ballCenterX = _ballPosition.dx;
          final ballCenterY = _ballPosition.dy;
          final brickCenterX = brickLeft + _brickWidth / 2;
          final brickCenterY = brickTop + _brickHeight / 2;
          
          final dx = ballCenterX - brickCenterX;
          final dy = ballCenterY - brickCenterY;
          
          if (dx.abs() > dy.abs()) {
            // Hit from side
            _ballVelocity = Offset(-_ballVelocity.dx, _ballVelocity.dy);
          } else {
            // Hit from top/bottom
            _ballVelocity = Offset(_ballVelocity.dx, -_ballVelocity.dy);
          }
          
          // Show explosion effect
          _explosionPosition = Offset(brickCenterX, brickCenterY);
          _showExplosion = true;
          _explosionController.forward().then((_) {
            _explosionController.reset();
            setState(() {
              _showExplosion = false;
            });
          });
          
          ref.read(gameFeedbackServiceProvider).triggerFeedback(GameEvent.scoreIncrease);
          _ballController.forward().then((_) => _ballController.reset());
          
          return; // Only hit one brick per frame
        }
      }
    }
  }

  bool _allBricksDestroyed() {
    for (final row in _bricks) {
      for (final brick in row) {
        if (brick != null && !brick.isDestroyed) {
          return false;
        }
      }
    }
    return true;
  }

  void _triggerBounceEffect() {
    ref.read(gameFeedbackServiceProvider).triggerFeedback(GameEvent.targetHit);
    _ballController.forward().then((_) => _ballController.reset());
  }

  void _onPanUpdate(DragUpdateDetails details) {
    final gameState = ref.read(gameStateProvider(SmashWallGame.gameConfig));
    if (!gameState.isPlaying) return;
    
    setState(() {
      final constrainedPosition = TapVerseWallCollision.constrainRectangularObject(
        position: Offset(details.localPosition.dx - _paddleWidth / 2, _paddleY),
        objectSize: Size(_paddleWidth, _paddleHeight),
        gameArea: Size(_gameWidth, _gameHeight),
      );
      _paddleX = constrainedPosition.dx;
    });
  }

  @override
  Widget build(BuildContext context) {
    final gameState = ref.watch(gameStateProvider(SmashWallGame.gameConfig));
    
    return Container(
      width: _gameWidth,
      height: _gameHeight,
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [Color(0xFF0f0f23), Color(0xFF1a1a2e)],
        ),
      ),
      child: GestureDetector(
        onPanUpdate: _onPanUpdate,
        child: Stack(
          children: [
            // Walls
            _buildWalls(),
            
            // Bricks
            _buildBricks(),
            
            // Ball
            _buildBall(),
            
            // Paddle
            _buildPaddle(),
            
            // Explosion effect
            if (_showExplosion && _explosionPosition != null) _buildExplosion(),
            
            // Score display
            _buildScoreDisplay(gameState),
            
            // Instructions
            if (!gameState.isPlaying) _buildInstructions(),
          ],
        ),
      ),
    );
  }

  Widget _buildWalls() {
    return Positioned.fill(
      child: CustomPaint(
        painter: _WallsPainter(),
      ),
    );
  }

  Widget _buildBricks() {
    return Stack(
      children: [
        for (int row = 0; row < _brickRows; row++)
          for (int col = 0; col < _brickCols; col++)
            if (_bricks[row][col] != null && !_bricks[row][col]!.isDestroyed)
              Positioned(
                left: TapVerseWallCollision.getPlayableAreaOffset().dx + col * _brickWidth,
                top: TapVerseWallCollision.getPlayableAreaOffset().dy + 50 + row * _brickHeight,
                child: Container(
                  width: _brickWidth - 2,
                  height: _brickHeight - 2,
                  decoration: BoxDecoration(
                    color: _bricks[row][col]!.color,
                    borderRadius: BorderRadius.circular(4),
                    border: Border.all(color: Colors.white24),
                    boxShadow: [
                      BoxShadow(
                        color: _bricks[row][col]!.color.withOpacity(0.5),
                        blurRadius: 2,
                        spreadRadius: 1,
                      ),
                    ],
                  ),
                ),
              ),
      ],
    );
  }

  Widget _buildBall() {
    return Positioned(
      left: _ballPosition.dx - _ballSize / 2,
      top: _ballPosition.dy - _ballSize / 2,
      child: ScaleTransition(
        scale: Tween<double>(begin: 1.0, end: 1.3).animate(_ballController),
        child: Container(
          width: _ballSize,
          height: _ballSize,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            gradient: RadialGradient(
              colors: [Colors.white, Colors.cyan[400]!],
              stops: const [0.3, 1.0],
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.cyan.withOpacity(0.6),
                blurRadius: 6,
                spreadRadius: 2,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPaddle() {
    return Positioned(
      left: _paddleX,
      top: _paddleY,
      child: ScaleTransition(
        scale: Tween<double>(begin: 1.0, end: 1.1).animate(_paddleController),
        child: Container(
          width: _paddleWidth,
          height: _paddleHeight,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [Colors.grey[300]!, Colors.grey[400]!],
            ),
            borderRadius: BorderRadius.circular(8),
            boxShadow: [
              BoxShadow(
                color: Colors.white.withOpacity(0.3),
                blurRadius: 4,
                spreadRadius: 1,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildExplosion() {
    return Positioned(
      left: _explosionPosition!.dx - 30,
      top: _explosionPosition!.dy - 30,
      child: ScaleTransition(
        scale: _explosionController,
        child: Container(
          width: 60,
          height: 60,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            gradient: RadialGradient(
              colors: [Colors.yellow, Colors.orange, Colors.red],
              stops: const [0.0, 0.5, 1.0],
            ),
          ),
          child: const Center(
            child: Text(
              '💥',
              style: TextStyle(fontSize: 30),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildScoreDisplay(GameState gameState) {
    return Positioned(
      top: 20,
      left: 20,
      child: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: Colors.black54,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Score',
              style: TextStyle(color: Colors.white, fontSize: 12),
            ),
            Text(
              '${gameState.score}',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInstructions() {
    return Positioned(
      bottom: 100,
      left: 0,
      right: 0,
      child: Center(
        child: Container(
          padding: EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.black54,
            borderRadius: BorderRadius.all(Radius.circular(12)),
          ),
          child: Text(
            'Drag the paddle to bounce the ball!\nBreak all the bricks to win.\nHigher bricks are worth more points.',
            textAlign: TextAlign.center,
            style: TextStyle(
              color: Colors.white,
              fontSize: 16,
            ),
          ),
        ),
      ),
    );
  }
}

class _WallsPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final wallPaint = Paint()
      ..color = Colors.grey[700]!
      ..style = PaintingStyle.fill;
    
    // Left wall
    canvas.drawRect(Rect.fromLTWH(0, 0, 10, size.height), wallPaint);
    
    // Right wall
    canvas.drawRect(Rect.fromLTWH(size.width - 10, 0, 10, size.height), wallPaint);
    
    // Top wall
    canvas.drawRect(Rect.fromLTWH(0, 0, size.width, 10), wallPaint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
