import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../services/game_integration_service.dart';
import '../providers/app_providers.dart';

/// Example of how to integrate the new token economy system with a game
/// This shows the complete flow from game start to completion with rewards
class GameIntegrationExample extends ConsumerStatefulWidget {
  final String gameId;
  
  const GameIntegrationExample({
    super.key,
    required this.gameId,
  });

  @override
  ConsumerState<GameIntegrationExample> createState() => _GameIntegrationExampleState();
}

class _GameIntegrationExampleState extends ConsumerState<GameIntegrationExample> {
  bool _isGameActive = false;
  DateTime? _gameStartTime;
  int _currentScore = 0;
  Map<String, dynamic>? _gameInfo;
  Map<String, dynamic>? _accessCheck;
  String? _statusMessage;

  @override
  void initState() {
    super.initState();
    _checkGameAccess();
  }

  /// Step 1: Check if user can access the game
  Future<void> _checkGameAccess() async {
    final gameIntegrationService = ref.read(gameIntegrationServiceProvider);
    
    try {
      final accessResult = await gameIntegrationService.checkGameAccess(widget.gameId);
      final gameInfo = await gameIntegrationService.getGameInfo(widget.gameId);
      
      setState(() {
        _accessCheck = accessResult;
        _gameInfo = gameInfo;
        _statusMessage = accessResult['canPlay'] 
            ? 'Ready to play!' 
            : accessResult['reason'];
      });
    } catch (e) {
      setState(() {
        _statusMessage = 'Error checking game access: $e';
      });
    }
  }

  /// Step 2: Start the game session (deduct tokens if needed)
  Future<void> _startGame() async {
    final gameIntegrationService = ref.read(gameIntegrationServiceProvider);
    
    try {
      final success = await gameIntegrationService.startGameSession(widget.gameId);
      
      if (success) {
        setState(() {
          _isGameActive = true;
          _gameStartTime = DateTime.now();
          _currentScore = 0;
          _statusMessage = 'Game started! Tokens deducted if required.';
        });
        
        // Refresh access info after token deduction
        await _checkGameAccess();
      } else {
        setState(() {
          _statusMessage = 'Failed to start game session';
        });
      }
    } catch (e) {
      setState(() {
        _statusMessage = 'Error starting game: $e';
      });
    }
  }

  /// Step 3: Simulate game play (in real game, this would be actual gameplay)
  void _simulateGameplay() {
    if (!_isGameActive) return;
    
    setState(() {
      _currentScore += 10;
    });
  }

  /// Step 4: End the game and award tokens
  Future<void> _endGame({required bool success}) async {
    if (!_isGameActive || _gameStartTime == null) return;
    
    final gameIntegrationService = ref.read(gameIntegrationServiceProvider);
    final duration = DateTime.now().difference(_gameStartTime!);
    
    try {
      final result = await gameIntegrationService.completeGameSession(
        gameId: widget.gameId,
        score: _currentScore,
        durationSec: duration.inSeconds,
        success: success,
      );
      
      setState(() {
        _isGameActive = false;
        _gameStartTime = null;
        
        if (result.wasBlocked) {
          _statusMessage = 'Game blocked: ${result.blockReason}';
        } else {
          final tokens = result.rewardResult?.finalTokens ?? 0;
          _statusMessage = 'Game completed! Earned $tokens tokens. ${result.rewardResult?.breakdown ?? ''}';
        }
      });
      
      // Refresh access info after game completion
      await _checkGameAccess();
    } catch (e) {
      setState(() {
        _statusMessage = 'Error ending game: $e';
        _isGameActive = false;
      });
    }
  }

  /// End gaming session and get session bonuses
  Future<void> _endSession() async {
    final gameIntegrationService = ref.read(gameIntegrationServiceProvider);
    
    try {
      final result = await gameIntegrationService.endGamingSession();
      final bonusTokens = result['bonusTokensAwarded'] ?? 0;
      
      setState(() {
        _statusMessage = bonusTokens > 0 
            ? 'Session ended! Earned $bonusTokens bonus tokens!'
            : 'Session ended. No bonus tokens this time.';
      });
    } catch (e) {
      setState(() {
        _statusMessage = 'Error ending session: $e';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Token Economy Integration - ${widget.gameId}'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Game Info Section
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('Game Information', style: Theme.of(context).textTheme.headlineSmall),
                    const SizedBox(height: 8),
                    if (_gameInfo != null) ...[
                      Text('Access Tier: ${_gameInfo!['accessTier']}'),
                      Text('Token Cost: ${_gameInfo!['tokenCost']}'),
                      Text('Payout Multiplier: ${_gameInfo!['payoutMultiplier']}x'),
                      if (_gameInfo!['remainingFreePlays'] != null)
                        Text('Remaining Free Plays: ${_gameInfo!['remainingFreePlays']}'),
                    ],
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Access Check Section
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('Access Status', style: Theme.of(context).textTheme.headlineSmall),
                    const SizedBox(height: 8),
                    if (_accessCheck != null) ...[
                      Row(
                        children: [
                          Icon(
                            _accessCheck!['canPlay'] ? Icons.check_circle : Icons.cancel,
                            color: _accessCheck!['canPlay'] ? Colors.green : Colors.red,
                          ),
                          const SizedBox(width: 8),
                          Expanded(child: Text(_accessCheck!['reason'])),
                        ],
                      ),
                      if (_accessCheck!['cost'] > 0)
                        Text('Cost: ${_accessCheck!['cost']} tokens'),
                    ],
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Game Controls Section
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('Game Controls', style: Theme.of(context).textTheme.headlineSmall),
                    const SizedBox(height: 8),
                    
                    if (!_isGameActive) ...[
                      ElevatedButton(
                        onPressed: _accessCheck?['canPlay'] == true ? _startGame : null,
                        child: const Text('Start Game'),
                      ),
                    ] else ...[
                      Text('Score: $_currentScore'),
                      Text('Duration: ${_gameStartTime != null ? DateTime.now().difference(_gameStartTime!).inSeconds : 0}s'),
                      const SizedBox(height: 8),
                      Row(
                        children: [
                          ElevatedButton(
                            onPressed: _simulateGameplay,
                            child: const Text('Add Score (+10)'),
                          ),
                          const SizedBox(width: 8),
                          ElevatedButton(
                            onPressed: () => _endGame(success: true),
                            child: const Text('Win Game'),
                          ),
                          const SizedBox(width: 8),
                          ElevatedButton(
                            onPressed: () => _endGame(success: false),
                            child: const Text('Lose Game'),
                          ),
                        ],
                      ),
                    ],
                    
                    const SizedBox(height: 16),
                    
                    ElevatedButton(
                      onPressed: _endSession,
                      child: const Text('End Gaming Session'),
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Status Section
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('Status', style: Theme.of(context).textTheme.headlineSmall),
                    const SizedBox(height: 8),
                    Text(_statusMessage ?? 'Loading...'),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Refresh Button
            ElevatedButton(
              onPressed: _checkGameAccess,
              child: const Text('Refresh Game Info'),
            ),
          ],
        ),
      ),
    );
  }
}

/// Example usage in a game screen
class ExampleGameScreen extends ConsumerWidget {
  final String gameId;
  
  const ExampleGameScreen({super.key, required this.gameId});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return GameIntegrationExample(gameId: gameId);
  }
}

/// How to integrate with existing game widgets
mixin TokenEconomyGameMixin<T extends ConsumerStatefulWidget> on ConsumerState<T> {
  late final GameIntegrationService _gameIntegrationService;
  String get gameId;
  
  @override
  void initState() {
    super.initState();
    _gameIntegrationService = ref.read(gameIntegrationServiceProvider);
  }
  
  /// Call this before starting the actual game
  Future<bool> startTokenEconomySession() async {
    return await _gameIntegrationService.startGameSession(gameId);
  }
  
  /// Call this when the game ends
  Future<void> endTokenEconomySession({
    required int score,
    required int durationSec,
    required bool success,
  }) async {
    await _gameIntegrationService.completeGameSession(
      gameId: gameId,
      score: score,
      durationSec: durationSec,
      success: success,
    );
  }
  
  /// Check if user can play the game
  Future<bool> canPlayGame() async {
    final result = await _gameIntegrationService.checkGameAccess(gameId);
    return result['canPlay'] ?? false;
  }
}
