import 'dart:math' as math;
import 'package:flame/components.dart';
import 'package:flutter/material.dart';
import '../../core/mini_game_base.dart';
import '../../core/hud.dart';
import '../../core/effects.dart';
import '../../core/analytics.dart';
import '../../core/difficulty.dart';

/// ZapGrid - Circuit completion puzzle with energy flow
/// Controls: Tap to rotate circuit pieces; complete paths from source to sink
/// Scoring: +5 per connection; +25 per circuit complete; efficiency bonus
class ZapGridGame extends MiniGameBase {
  ZapGridGame(int seed) : super(modeId: 'zap_grid', sessionSeed: seed) {
    rng = math.Random(seed);
  }

  late math.Random rng;
  late ScoreText scoreText;
  late TextComponent levelText;
  
  // Game state
  int score = 0;
  int level = 1;
  int circuitsCompleted = 0;
  
  // Grid system
  static const int gridSize = 6;
  List<List<CircuitPiece>> grid = [];
  List<Vector2> sources = [];
  List<Vector2> sinks = [];
  
  // Difficulty curves
  final complexityCurve = const CurveParam(start: 2, max: 5, perMinute: 1.5);
  
  late DifficultyManager difficultyManager;
  
  // Visual elements
  late CircuitGrid circuitGrid;

  @override
  Future<void> loadAssets() async {}

  @override
  void setupWorld() {
    camera.viewfinder.visibleGameSize = Vector2(720, 1280);
    
    difficultyManager = DifficultyManager();
    difficultyManager.addParameter('complexity', complexityCurve);
    
    _initializeGrid();
    _createGameEntities();
  }

  void _initializeGrid() {
    grid = List.generate(gridSize, (i) => 
        List.generate(gridSize, (j) => CircuitPiece()));
    
    sources = [];
    sinks = [];
    
    _generateLevel();
  }

  void _generateLevel() {
    final complexity = difficultyManager.getValue('complexity').round();
    
    // Clear grid
    for (int i = 0; i < gridSize; i++) {
      for (int j = 0; j < gridSize; j++) {
        grid[i][j] = CircuitPiece();
      }
    }
    
    sources.clear();
    sinks.clear();
    
    // Place sources and sinks
    for (int i = 0; i < complexity; i++) {
      _placeSource();
      _placeSink();
    }
    
    // Generate circuit pieces
    _generateCircuitPieces();
  }

  void _placeSource() {
    Vector2 pos;
    do {
      pos = Vector2(rng.nextInt(gridSize).toDouble(), rng.nextInt(gridSize).toDouble());
    } while (_isPositionOccupied(pos));
    
    sources.add(pos);
    grid[pos.y.toInt()][pos.x.toInt()] = CircuitPiece(type: PieceType.source);
  }

  void _placeSink() {
    Vector2 pos;
    do {
      pos = Vector2(rng.nextInt(gridSize).toDouble(), rng.nextInt(gridSize).toDouble());
    } while (_isPositionOccupied(pos));
    
    sinks.add(pos);
    grid[pos.y.toInt()][pos.x.toInt()] = CircuitPiece(type: PieceType.sink);
  }

  bool _isPositionOccupied(Vector2 pos) {
    return sources.any((s) => s == pos) || sinks.any((s) => s == pos);
  }

  void _generateCircuitPieces() {
    for (int i = 0; i < gridSize; i++) {
      for (int j = 0; j < gridSize; j++) {
        if (grid[i][j].type == PieceType.empty) {
          // Randomly place different circuit pieces
          final pieceTypes = [
            PieceType.straight,
            PieceType.corner,
            PieceType.tJunction,
            PieceType.cross,
          ];
          
          final randomType = pieceTypes[rng.nextInt(pieceTypes.length)];
          grid[i][j] = CircuitPiece(
            type: randomType,
            rotation: rng.nextInt(4),
          );
        }
      }
    }
  }

  void _createGameEntities() {
    // Create circuit grid
    circuitGrid = CircuitGrid(
      position: Vector2(60, 300),
      grid: grid,
      onPieceTapped: _handlePieceTapped,
    );
    add(circuitGrid);
  }

  @override
  void setupHUD() {
    scoreText = ScoreText(position: Vector2(16, 16));
    add(scoreText);

    levelText = TextComponent(
      text: 'Level: $level',
      position: Vector2(16, 50),
      textRenderer: TextPaint(
        style: const TextStyle(
          color: Colors.white,
          fontSize: 20,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
    add(levelText);
  }

  @override
  void onStart() {
    score = 0;
    level = 1;
    circuitsCompleted = 0;
    
    difficultyManager.start();
    startGame();
    
    Analytics.log('zap_grid_start', {'session_seed': sessionSeed});
  }

  @override
  void onGameUpdate(double dt) {
    levelText.text = 'Level: $level';
    
    // Check for completed circuits
    _checkCircuitCompletion();
  }

  void _handlePieceTapped(int row, int col) {
    if (grid[row][col].type != PieceType.source && 
        grid[row][col].type != PieceType.sink) {
      
      // Rotate piece
      grid[row][col].rotation = (grid[row][col].rotation + 1) % 4;
      circuitGrid.updateGrid(grid);
      
      Analytics.log('piece_rotated', {
        'row': row,
        'col': col,
        'new_rotation': grid[row][col].rotation,
      });
    }
  }

  void _checkCircuitCompletion() {
    int completedCircuits = 0;
    
    for (final source in sources) {
      if (_isCircuitComplete(source)) {
        completedCircuits++;
      }
    }
    
    if (completedCircuits > circuitsCompleted) {
      final newCompletions = completedCircuits - circuitsCompleted;
      circuitsCompleted = completedCircuits;
      
      // Award points for new completions
      score += newCompletions * 25;
      addScore(newCompletions * 25);
      scoreText.updateScore(score);
      
      // Visual effects
      add(Effects.sparkle(Vector2(360, 600), colors: [Colors.blue, Colors.cyan]));
      add(Effects.textPopup(
        Vector2(360, 550),
        'CIRCUIT COMPLETE +25',
        color: Colors.blue,
      ));
      
      Analytics.log('circuit_completed', {
        'circuits_completed': circuitsCompleted,
        'level': level,
      });
    }
    
    // Check if all circuits are complete
    if (circuitsCompleted == sources.length) {
      _levelComplete();
    }
  }

  bool _isCircuitComplete(Vector2 source) {
    // Use flood fill to check if source connects to any sink
    final visited = <String>{};
    final queue = <Vector2>[source];
    
    while (queue.isNotEmpty) {
      final current = queue.removeAt(0);
      final key = '${current.x},${current.y}';
      
      if (visited.contains(key)) continue;
      visited.add(key);
      
      // Check if we reached a sink
      if (sinks.any((sink) => sink == current && sink != source)) {
        return true;
      }
      
      // Add connected neighbors
      final neighbors = _getConnectedNeighbors(current);
      for (final neighbor in neighbors) {
        if (!visited.contains('${neighbor.x},${neighbor.y}')) {
          queue.add(neighbor);
        }
      }
    }
    
    return false;
  }

  List<Vector2> _getConnectedNeighbors(Vector2 pos) {
    final neighbors = <Vector2>[];
    final row = pos.y.toInt();
    final col = pos.x.toInt();
    
    if (row < 0 || row >= gridSize || col < 0 || col >= gridSize) {
      return neighbors;
    }
    
    final piece = grid[row][col];
    final connections = piece.getConnections();
    
    // Check each direction
    final directions = [
      Vector2(0, -1), // Up
      Vector2(1, 0),  // Right
      Vector2(0, 1),  // Down
      Vector2(-1, 0), // Left
    ];
    
    for (int i = 0; i < directions.length; i++) {
      if (connections[i]) {
        final newPos = pos + directions[i];
        if (_isValidPosition(newPos) && _canConnect(pos, newPos, i)) {
          neighbors.add(newPos);
        }
      }
    }
    
    return neighbors;
  }

  bool _isValidPosition(Vector2 pos) {
    return pos.x >= 0 && pos.x < gridSize && pos.y >= 0 && pos.y < gridSize;
  }

  bool _canConnect(Vector2 from, Vector2 to, int direction) {
    final toRow = to.y.toInt();
    final toCol = to.x.toInt();
    final toPiece = grid[toRow][toCol];
    
    // Check if the target piece has a connection back to us
    final oppositeDirection = (direction + 2) % 4;
    return toPiece.getConnections()[oppositeDirection];
  }

  void _levelComplete() {
    level++;
    score += 50; // Level completion bonus
    addScore(50);
    scoreText.updateScore(score);
    
    // Efficiency bonus (fewer rotations = higher bonus)
    // This is simplified - in a real implementation, you'd track rotations
    final efficiencyBonus = 20;
    score += efficiencyBonus;
    addScore(efficiencyBonus);
    scoreText.updateScore(score);
    
    // Visual effects
    add(Effects.explosion(Vector2(360, 600), colors: [Colors.amber, Colors.yellow]));
    add(Effects.textPopup(
      Vector2(360, 500),
      'LEVEL COMPLETE +${50 + efficiencyBonus}',
      color: Colors.amber,
    ));
    
    // Generate next level
    _generateLevel();
    circuitGrid.updateGrid(grid);
    circuitsCompleted = 0;
    
    Analytics.log('level_complete', {
      'level': level - 1,
      'efficiency_bonus': efficiencyBonus,
    });
  }


  @override
  void reportScore(int score) {}

  @override
  void awardTokens(int tokens) {}
}

enum PieceType { empty, source, sink, straight, corner, tJunction, cross }

class CircuitPiece {
  CircuitPiece({
    this.type = PieceType.empty,
    this.rotation = 0,
  });

  final PieceType type;
  int rotation;

  List<bool> getConnections() {
    // Returns [up, right, down, left] connections
    switch (type) {
      case PieceType.source:
      case PieceType.sink:
        return [true, true, true, true]; // Connect in all directions
      case PieceType.straight:
        // Horizontal or vertical line
        final base = [true, false, true, false]; // Vertical
        return _rotateConnections(base, rotation);
      case PieceType.corner:
        // L-shaped piece
        final base = [true, true, false, false]; // Up-right corner
        return _rotateConnections(base, rotation);
      case PieceType.tJunction:
        // T-shaped piece
        final base = [true, true, true, false]; // T pointing down
        return _rotateConnections(base, rotation);
      case PieceType.cross:
        return [true, true, true, true]; // Connect in all directions
      case PieceType.empty:
      default:
        return [false, false, false, false];
    }
  }

  List<bool> _rotateConnections(List<bool> base, int rotations) {
    final result = List<bool>.from(base);
    for (int i = 0; i < rotations; i++) {
      final temp = result[0];
      result[0] = result[3];
      result[3] = result[2];
      result[2] = result[1];
      result[1] = temp;
    }
    return result;
  }
}

class CircuitGrid extends PositionComponent {
  CircuitGrid({
    required super.position,
    required this.grid,
    required this.onPieceTapped,
  }) : super(size: Vector2(600, 600));

  List<List<CircuitPiece>> grid;
  final Function(int, int) onPieceTapped;
  
  static const double pieceSize = 90;
  static const double pieceSpacing = 10;

  @override
  void render(Canvas canvas) {
    for (int i = 0; i < grid.length; i++) {
      for (int j = 0; j < grid[i].length; j++) {
        final pieceX = j * (pieceSize + pieceSpacing);
        final pieceY = i * (pieceSize + pieceSpacing);
        
        _drawPiece(canvas, grid[i][j], pieceX, pieceY);
      }
    }
  }

  void _drawPiece(Canvas canvas, CircuitPiece piece, double x, double y) {
    // Draw background
    final bgPaint = Paint()..color = Colors.grey[800]!;
    canvas.drawRect(
      Rect.fromLTWH(x, y, pieceSize, pieceSize),
      bgPaint,
    );
    
    // Draw piece based on type
    final piecePaint = Paint()
      ..color = _getPieceColor(piece.type)
      ..strokeWidth = 4;
    
    final center = Offset(x + pieceSize / 2, y + pieceSize / 2);
    final connections = piece.getConnections();
    
    // Draw connections
    if (connections[0]) { // Up
      canvas.drawLine(center, Offset(center.dx, y), piecePaint);
    }
    if (connections[1]) { // Right
      canvas.drawLine(center, Offset(x + pieceSize, center.dy), piecePaint);
    }
    if (connections[2]) { // Down
      canvas.drawLine(center, Offset(center.dx, y + pieceSize), piecePaint);
    }
    if (connections[3]) { // Left
      canvas.drawLine(center, Offset(x, center.dy), piecePaint);
    }
    
    // Draw center node
    canvas.drawCircle(center, 8, piecePaint);
  }

  Color _getPieceColor(PieceType type) {
    switch (type) {
      case PieceType.source:
        return Colors.green;
      case PieceType.sink:
        return Colors.red;
      default:
        return Colors.blue;
    }
  }

  // TODO: Implement input handling
  void handleTapDown(Vector2 position) {
    final col = (position.x / (pieceSize + pieceSpacing)).floor();
    final row = (position.y / (pieceSize + pieceSpacing)).floor();

    if (row >= 0 && row < grid.length && col >= 0 && col < grid[0].length) {
      onPieceTapped(row, col);
    }
  }

  void updateGrid(List<List<CircuitPiece>> newGrid) {
    grid = newGrid;
  }
}
