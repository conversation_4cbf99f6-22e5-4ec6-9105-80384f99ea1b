import 'dart:math';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/models/game_state.dart';
import '../../../core/providers/game_providers.dart';
import '../../../core/services/game_feedback_service.dart';
import '../../../shared/widgets/base_game_widget.dart';

class TappyFootyGame extends BaseGameWidget {
  static const GameConfig gameConfig = GameConfig(
    gameId: 'tappy_footy',
    gameName: 'TappyFooty',
    scoreMultiplier: 1,
    hasLives: false,
    hasLevels: false,
    hasTimer: false,
  );

  const TappyFootyGame({
    super.key,
    super.onGameComplete,
    super.onGameExit,
  }) : super(config: gameConfig);

  @override
  Widget buildGameContent(BuildContext context, WidgetRef ref, GameState gameState) {
    return const _TappyFootyGameContent();
  }
}

class _TappyFootyGameContent extends ConsumerStatefulWidget {
  const _TappyFootyGameContent();

  @override
  ConsumerState<_TappyFootyGameContent> createState() => _TappyFootyGameContentState();
}

class _TappyFootyGameContentState extends ConsumerState<_TappyFootyGameContent>
    with TickerProviderStateMixin {
  
  // Game state
  late AnimationController _ballController;
  late AnimationController _kickController;
  
  // Ball physics
  double _ballY = 400;
  double _ballVelocityY = 0;
  bool _ballInMotion = false;
  
  // Game mechanics
  final double _ballSize = 40;
  final double _ballX = 200; // Fixed X position
  final double _groundY = 500;
  final double _gravity = 1200; // Pixels per second squared
  final double _kickForce = -400; // Upward force when tapped
  
  // Animation and visual effects
  bool _showKickEffect = false;
  
  // Constants
  static const double _fieldWidth = 400;
  static const double _fieldHeight = 600;

  @override
  void initState() {
    super.initState();

    _ballController = AnimationController(
      duration: const Duration(milliseconds: 100),
      vsync: this,
    );

    _kickController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );

    _resetBall();
    _startPhysicsLoop();

    // Listen for game state changes to reset when needed
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.listen(gameStateProvider(TappyFootyGame.gameConfig), (previous, next) {
        if (previous?.status != next.status && next.status == GameStatus.notStarted) {
          _resetBall();
        }
      });
    });
  }

  @override
  void dispose() {
    _ballController.dispose();
    _kickController.dispose();
    super.dispose();
  }

  void _resetBall() {
    setState(() {
      _ballY = _groundY - _ballSize - 50; // Start ball 50 pixels above ground
      _ballVelocityY = 0;
      _ballInMotion = false;
    });
  }

  void _startPhysicsLoop() {
    const frameRate = 60;
    const frameDuration = Duration(milliseconds: 1000 ~/ frameRate);
    
    void updatePhysics() {
      if (!mounted) return;
      
      final gameState = ref.read(gameStateProvider(TappyFootyGame.gameConfig));
      if (!gameState.isPlaying) {
        // Don't apply physics when not playing, but keep the loop running
        Future.delayed(frameDuration, updatePhysics);
        return;
      }
      
      setState(() {
        // Apply gravity
        _ballVelocityY += _gravity / frameRate;
        
        // Update ball position
        _ballY += _ballVelocityY / frameRate;
        
        // Check ground collision
        if (_ballY >= _groundY - _ballSize) {
          _ballY = _groundY - _ballSize;
          _ballVelocityY = 0;
          _ballInMotion = false;
          
          // Game over - ball hit the ground
          final gameNotifier = ref.read(gameStateProvider(TappyFootyGame.gameConfig).notifier);
          gameNotifier.endGame(reason: 'Ball hit the ground');
        }
        
        // Check if ball is moving
        _ballInMotion = _ballVelocityY != 0;
      });
      
      Future.delayed(frameDuration, updatePhysics);
    }
    
    updatePhysics();
  }

  void _onTap() {
    final gameState = ref.read(gameStateProvider(TappyFootyGame.gameConfig));
    if (!gameState.isPlaying) return;
    
    // Only allow tapping when ball is falling or stationary
    if (_ballVelocityY >= -50) {
      setState(() {
        _ballVelocityY = _kickForce;
        _ballInMotion = true;
        _showKickEffect = true;
      });
      
      // Add score for each tap
      final gameNotifier = ref.read(gameStateProvider(TappyFootyGame.gameConfig).notifier);
      gameNotifier.addScore(1);
      
      // Trigger feedback
      ref.read(gameFeedbackServiceProvider).triggerFeedback(GameEvent.scoreIncrease);
      
      // Play kick animation
      _kickController.forward().then((_) {
        _kickController.reset();
        setState(() {
          _showKickEffect = false;
        });
      });
      
      // Ball bounce animation
      _ballController.forward().then((_) {
        _ballController.reset();
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final gameState = ref.watch(gameStateProvider(TappyFootyGame.gameConfig));
    
    return Container(
      width: _fieldWidth,
      height: _fieldHeight,
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [Color(0xFF87CEEB), Color(0xFF98FB98)],
        ),
      ),
      child: GestureDetector(
        onTap: _onTap,
        child: Stack(
          children: [
            // Field markings
            _buildField(),
            
            // Soccer ball
            _buildBall(),
            
            // Kick effect
            if (_showKickEffect) _buildKickEffect(),
            
            // Instructions
            if (!gameState.isPlaying) _buildInstructions(),
            
            // Score display
            _buildScoreDisplay(gameState),
          ],
        ),
      ),
    );
  }

  Widget _buildField() {
    return Positioned.fill(
      child: CustomPaint(
        painter: _FieldPainter(),
      ),
    );
  }

  Widget _buildBall() {
    return Positioned(
      left: _ballX - _ballSize / 2,
      top: _ballY - _ballSize / 2,
      child: ScaleTransition(
        scale: Tween<double>(begin: 1.0, end: 1.2).animate(_ballController),
        child: Container(
          width: _ballSize,
          height: _ballSize,
          decoration: const BoxDecoration(
            shape: BoxShape.circle,
            gradient: RadialGradient(
              colors: [Colors.white, Colors.black87],
              stops: [0.3, 1.0],
            ),
          ),
          child: CustomPaint(
            painter: _SoccerBallPainter(),
          ),
        ),
      ),
    );
  }

  Widget _buildKickEffect() {
    return Positioned(
      left: _ballX - 30,
      top: _ballY + 10,
      child: ScaleTransition(
        scale: _kickController,
        child: Container(
          width: 60,
          height: 20,
          decoration: BoxDecoration(
            color: Colors.yellow.withValues(alpha: 0.7),
            borderRadius: BorderRadius.circular(30),
          ),
          child: const Center(
            child: Text(
              'KICK!',
              style: TextStyle(
                color: Colors.white,
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildInstructions() {
    return Positioned(
      bottom: 100,
      left: 0,
      right: 0,
      child: Center(
        child: Container(
          padding: EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.black54,
            borderRadius: BorderRadius.all(Radius.circular(12)),
          ),
          child: Text(
            'Tap to keep the ball in the air!\nEach tap scores 1 point.\nDon\'t let it hit the ground!',
            textAlign: TextAlign.center,
            style: TextStyle(
              color: Colors.white,
              fontSize: 16,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildScoreDisplay(GameState gameState) {
    return Positioned(
      top: 50,
      left: 20,
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.black54,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Taps',
              style: TextStyle(color: Colors.white, fontSize: 12),
            ),
            Text(
              '${gameState.score}',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _FieldPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.white
      ..strokeWidth = 2
      ..style = PaintingStyle.stroke;
    
    // Draw field boundaries
    canvas.drawRect(
      Rect.fromLTWH(20, 20, size.width - 40, size.height - 40),
      paint,
    );
    
    // Draw center circle
    canvas.drawCircle(
      Offset(size.width / 2, size.height / 2),
      50,
      paint,
    );
    
    // Draw center line
    canvas.drawLine(
      Offset(20, size.height / 2),
      Offset(size.width - 20, size.height / 2),
      paint,
    );
    
    // Draw ground line
    final groundPaint = Paint()
      ..color = Colors.green[800]!
      ..strokeWidth = 4;
    
    canvas.drawLine(
      Offset(0, size.height - 100),
      Offset(size.width, size.height - 100),
      groundPaint,
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class _SoccerBallPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.black
      ..strokeWidth = 2
      ..style = PaintingStyle.stroke;
    
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width / 2;
    
    // Draw pentagon pattern
    for (int i = 0; i < 5; i++) {
      final angle = (i * 2 * pi / 5) - pi / 2;
      final x = center.dx + cos(angle) * radius * 0.3;
      final y = center.dy + sin(angle) * radius * 0.3;
      
      canvas.drawLine(center, Offset(x, y), paint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
