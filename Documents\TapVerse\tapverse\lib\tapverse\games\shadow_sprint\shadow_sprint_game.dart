import 'dart:math' as math;
import 'package:flame/components.dart';
import 'package:flame/collisions.dart';
import 'package:flutter/material.dart';
import '../../core/mini_game_base.dart';
import '../../core/hud.dart';
import '../../core/effects.dart';
import '../../core/analytics.dart';
import '../../core/difficulty.dart';

/// ShadowSprint - Stealth runner with light/shadow mechanics
/// Controls: Tap to dash; hold to hide in shadows; avoid light beams
/// Scoring: +1 per 50px distance; +10 per guard avoided; +20 per perfect stealth section
class ShadowSprintGame extends MiniGameBase {
  ShadowSprintGame(int seed) : super(modeId: 'shadow_sprint', sessionSeed: seed) {
    rng = math.Random(seed);
  }

  late math.Random rng;
  late ScoreText scoreText;
  late TextComponent stealthText;
  
  // Game state
  int score = 0;
  double distance = 0;
  double lastScoredDistance = 0;
  bool isInShadow = false;
  int guardsAvoided = 0;
  
  // Game entities
  late ShadowRunner runner;
  late List<ShadowZone> shadowZones;
  late List<LightBeam> lightBeams;
  late List<Guard> guards;
  
  // Difficulty curves (from planning document)
  final runSpeedCurve = const CurveParam(start: 180, max: 320, perMinute: 70);
  final guardCountCurve = const CurveParam(start: 1, max: 4, perMinute: 1.5);
  
  // Difficulty manager
  late DifficultyManager difficultyManager;
  
  // Level generation
  double nextShadowX = 300;
  double nextLightX = 500;
  double nextGuardX = 800;

  @override
  Future<void> loadAssets() async {
    // Load any required assets here
  }

  @override
  void setupWorld() {
    camera.viewfinder.visibleGameSize = Vector2(720, 1280);
    
    // Initialize difficulty manager
    difficultyManager = DifficultyManager();
    difficultyManager.addParameter('run_speed', runSpeedCurve);
    difficultyManager.addParameter('guard_count', guardCountCurve);
    
    // Initialize collections
    shadowZones = [];
    lightBeams = [];
    guards = [];
    
    // Create game entities
    _createGameEntities();
  }

  void _createGameEntities() {
    // Create runner
    runner = ShadowRunner(
      position: Vector2(100, 1000),
      onLightDetected: _onLightDetected,
      onGuardDetected: _onGuardDetected,
      onShadowEntered: _onShadowEntered,
      onShadowExited: _onShadowExited,
    );
    add(runner);

    // Generate initial level
    _generateInitialLevel();
  }

  void _generateInitialLevel() {
    // Generate starting shadow zones
    for (int i = 0; i < 5; i++) {
      _generateShadowZone();
    }
    
    // Generate starting light beams
    for (int i = 0; i < 3; i++) {
      _generateLightBeam();
    }
    
    // Generate starting guards
    for (int i = 0; i < 2; i++) {
      _generateGuard();
    }
  }

  void _generateShadowZone() {
    final width = 80 + rng.nextDouble() * 120;
    final height = 200 + rng.nextDouble() * 300;
    final y = 800 + rng.nextDouble() * 400;
    
    final shadowZone = ShadowZone(
      position: Vector2(nextShadowX, y),
      width: width,
      height: height,
    );
    
    shadowZones.add(shadowZone);
    add(shadowZone);
    
    nextShadowX += 200 + rng.nextDouble() * 300;
  }

  void _generateLightBeam() {
    final beamType = rng.nextInt(3); // 0: static, 1: rotating, 2: sweeping
    
    final lightBeam = LightBeam(
      position: Vector2(nextLightX, 600 + rng.nextDouble() * 400),
      type: LightBeamType.values[beamType],
      width: 40 + rng.nextDouble() * 40,
      length: 200 + rng.nextDouble() * 200,
      rotationSpeed: beamType > 0 ? 1 + rng.nextDouble() * 2 : 0,
    );
    
    lightBeams.add(lightBeam);
    add(lightBeam);
    
    nextLightX += 300 + rng.nextDouble() * 200;
  }

  void _generateGuard() {
    final guardCount = difficultyManager.getValue('guard_count').round();
    
    for (int i = 0; i < guardCount; i++) {
      final guard = Guard(
        position: Vector2(nextGuardX + i * 100, 1000 + rng.nextDouble() * 200),
        patrolDistance: 100 + rng.nextDouble() * 100,
        speed: 50 + rng.nextDouble() * 50,
      );
      
      guards.add(guard);
      add(guard);
    }
    
    nextGuardX += 400 + rng.nextDouble() * 300;
  }

  @override
  void setupHUD() {
    // Score display
    scoreText = ScoreText(
      position: Vector2(16, 16),
      textStyle: const TextStyle(
        color: Colors.white,
        fontSize: 24,
        fontWeight: FontWeight.bold,
      ),
    );
    add(scoreText);

    // Stealth status
    stealthText = TextComponent(
      text: 'EXPOSED',
      position: Vector2(16, 50),
      textRenderer: TextPaint(
        style: const TextStyle(
          color: Colors.red,
          fontSize: 20,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
    add(stealthText);
  }

  @override
  void onStart() {
    score = 0;
    distance = 0;
    lastScoredDistance = 0;
    isInShadow = false;
    guardsAvoided = 0;
    
    // Start difficulty progression
    difficultyManager.start();
    
    // Start the game
    startGame();
    
    Analytics.log('shadow_sprint_start', {'session_seed': sessionSeed});
  }

  @override
  void onGameUpdate(double dt) {
    // Update run speed based on difficulty
    final runSpeed = difficultyManager.getValue('run_speed');
    runner.updateRunSpeed(runSpeed);
    
    // Update distance
    distance += runSpeed * dt;
    
    // Score for distance progress
    if (distance - lastScoredDistance >= 50) {
      lastScoredDistance = distance;
      score += 1;
      addScore(1);
      scoreText.updateScore(score);
    }
    
    // Generate new level elements
    if (runner.position.x > nextShadowX - 1000) {
      _generateShadowZone();
    }
    
    if (runner.position.x > nextLightX - 800) {
      _generateLightBeam();
    }
    
    if (runner.position.x > nextGuardX - 1000) {
      _generateGuard();
    }
    
    // Clean up old entities
    _cleanupOldEntities();
    
    // Update camera to follow runner
    camera.viewfinder.position = Vector2(runner.position.x + 200, 640);
    
    // Update stealth status display
    _updateStealthDisplay();
  }

  void _updateStealthDisplay() {
    if (isInShadow) {
      stealthText.text = 'HIDDEN';
      stealthText.textRenderer = TextPaint(
        style: const TextStyle(
          color: Colors.green,
          fontSize: 20,
          fontWeight: FontWeight.bold,
        ),
      );
    } else {
      stealthText.text = 'EXPOSED';
      stealthText.textRenderer = TextPaint(
        style: const TextStyle(
          color: Colors.red,
          fontSize: 20,
          fontWeight: FontWeight.bold,
        ),
      );
    }
  }

  void _cleanupOldEntities() {
    // Remove entities far behind runner
    shadowZones.removeWhere((zone) {
      if (zone.position.x < runner.position.x - 500) {
        zone.removeFromParent();
        return true;
      }
      return false;
    });
    
    lightBeams.removeWhere((beam) {
      if (beam.position.x < runner.position.x - 500) {
        beam.removeFromParent();
        return true;
      }
      return false;
    });
    
    guards.removeWhere((guard) {
      if (guard.position.x < runner.position.x - 500) {
        guard.removeFromParent();
        return true;
      }
      return false;
    });
  }

  // TODO: Implement input handling
  void handleTapDown(Vector2 position) {
    if (isGameEnded) return;

    runner.dash();
  }

  void handlePanStart(Vector2 position) {
    if (isGameEnded) return;

    runner.startHiding();
  }

  void handlePanEnd(Vector2 position) {
    if (isGameEnded) return;

    runner.stopHiding();
  }

  void _onLightDetected() {
    // Runner detected by light - game over
    endGame(success: false, score: score);
    
    Analytics.log('light_detected', {
      'final_score': score,
      'distance': distance,
      'guards_avoided': guardsAvoided,
    });
  }

  void _onGuardDetected() {
    // Runner detected by guard - game over
    endGame(success: false, score: score);
    
    Analytics.log('guard_detected', {
      'final_score': score,
      'distance': distance,
      'guards_avoided': guardsAvoided,
    });
  }

  void _onShadowEntered() {
    isInShadow = true;
  }

  void _onShadowExited() {
    isInShadow = false;
  }

  void _onGuardAvoided() {
    guardsAvoided++;
    score += 10;
    addScore(10);
    scoreText.updateScore(score);
    
    // Visual effects
    add(Effects.textPopup(
      runner.position + Vector2(0, -30),
      'GUARD AVOIDED +10',
      color: Colors.green,
    ));
    
    Analytics.log('guard_avoided', {
      'guards_avoided': guardsAvoided,
      'score': score,
    });
  }


  @override
  void reportScore(int score) {
    // Integration hook for TapVerse UI
  }

  @override
  void awardTokens(int tokens) {
    // Integration hook for TapVerse token system
  }
}

/// Shadow runner component with stealth mechanics
class ShadowRunner extends RectangleComponent with HasCollisionDetection, CollisionCallbacks {
  ShadowRunner({
    required super.position,
    required this.onLightDetected,
    required this.onGuardDetected,
    required this.onShadowEntered,
    required this.onShadowExited,
  }) : super(size: Vector2(30, 30));

  final VoidCallback onLightDetected;
  final VoidCallback onGuardDetected;
  final VoidCallback onShadowEntered;
  final VoidCallback onShadowExited;

  Vector2 velocity = Vector2.zero();
  double runSpeed = 180;
  bool isHiding = false;
  bool isDashing = false;
  double dashTimer = 0;
  bool isInShadow = false;
  
  static const double dashSpeed = 500;
  static const double dashTime = 0.3;
  static const double hiddenSpeed = 50;

  @override
  Future<void> onLoad() async {
    add(RectangleHitbox());
    paint = Paint()..color = Colors.blue;
  }

  @override
  void update(double dt) {
    super.update(dt);
    
    // Handle dashing
    if (isDashing) {
      dashTimer -= dt;
      velocity.x = dashSpeed;
      
      if (dashTimer <= 0) {
        isDashing = false;
      }
    } else if (isHiding) {
      velocity.x = hiddenSpeed;
    } else {
      velocity.x = runSpeed;
    }
    
    // Update position
    position += velocity * dt;
    
    // Update appearance based on state
    _updateAppearance();
  }

  void _updateAppearance() {
    if (isHiding && isInShadow) {
      paint = Paint()..color = Colors.black.withOpacity(0.3); // Nearly invisible
    } else if (isInShadow) {
      paint = Paint()..color = Colors.grey; // Shadowed
    } else if (isDashing) {
      paint = Paint()..color = Colors.yellow; // Dashing
    } else {
      paint = Paint()..color = Colors.blue; // Normal
    }
  }

  void dash() {
    if (!isDashing) {
      isDashing = true;
      dashTimer = dashTime;
    }
  }

  void startHiding() {
    isHiding = true;
  }

  void stopHiding() {
    isHiding = false;
  }

  void updateRunSpeed(double newRunSpeed) {
    runSpeed = newRunSpeed;
  }

  @override
  bool onCollisionStart(Set<Vector2> intersectionPoints, PositionComponent other) {
    if (other is ShadowZone) {
      if (!isInShadow) {
        isInShadow = true;
        onShadowEntered();
      }
      return true;
    } else if (other is LightBeam && other.isActive) {
      if (!isInShadow || !isHiding) {
        onLightDetected();
      }
      return true;
    } else if (other is Guard) {
      if (!isInShadow || !isHiding) {
        onGuardDetected();
      }
      return true;
    }
    return false;
  }

  @override
  bool onCollisionEnd(PositionComponent other) {
    if (other is ShadowZone) {
      isInShadow = false;
      onShadowExited();
      return true;
    }
    return false;
  }
}

/// Shadow zone for hiding
class ShadowZone extends RectangleComponent with HasCollisionDetection {
  ShadowZone({
    required super.position,
    required double width,
    required double height,
  }) : super(size: Vector2(width, height));

  @override
  Future<void> onLoad() async {
    add(RectangleHitbox());
    paint = Paint()..color = Colors.black.withOpacity(0.5);
  }
}

/// Light beam types
enum LightBeamType { static, rotating, sweeping }

/// Light beam component
class LightBeam extends RectangleComponent with HasCollisionDetection {
  LightBeam({
    required super.position,
    required this.type,
    required double width,
    required double length,
    this.rotationSpeed = 0,
  }) : super(size: Vector2(width, length));

  final LightBeamType type;
  final double rotationSpeed;
  bool isActive = true;
  double timer = 0;

  @override
  Future<void> onLoad() async {
    add(RectangleHitbox());
    paint = Paint()..color = Colors.yellow.withOpacity(0.7);
  }

  @override
  void update(double dt) {
    super.update(dt);
    
    timer += dt;
    
    switch (type) {
      case LightBeamType.rotating:
        angle += rotationSpeed * dt;
        break;
      case LightBeamType.sweeping:
        angle = math.sin(timer * rotationSpeed) * math.pi / 4; // ±45 degrees
        break;
      case LightBeamType.static:
        // No movement
        break;
    }
  }
}

/// Guard component with patrol behavior
class Guard extends CircleComponent with HasCollisionDetection {
  Guard({
    required super.position,
    required this.patrolDistance,
    required this.speed,
  }) : super(radius: 20);

  final double patrolDistance;
  final double speed;
  
  double startX = 0;
  double direction = 1;
  bool hasDetectionRadius = true;

  @override
  Future<void> onLoad() async {
    add(CircleHitbox());
    paint = Paint()..color = Colors.red;
    startX = position.x;
  }

  @override
  void update(double dt) {
    super.update(dt);
    
    // Patrol movement
    position.x += direction * speed * dt;
    
    // Reverse direction at patrol limits
    if (position.x >= startX + patrolDistance || position.x <= startX) {
      direction *= -1;
    }
  }

  @override
  void render(Canvas canvas) {
    super.render(canvas);
    
    // Draw detection radius
    if (hasDetectionRadius) {
      final detectionPaint = Paint()
        ..color = Colors.red.withOpacity(0.2)
        ..style = PaintingStyle.fill;
      
      canvas.drawCircle(
        Offset.zero,
        60, // Detection radius
        detectionPaint,
      );
    }
  }
}
