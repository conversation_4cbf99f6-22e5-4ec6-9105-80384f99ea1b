import 'package:flutter/material.dart';
import '../../../core/models/store_item.dart';

class InventoryItemCard extends StatelessWidget {
  final StoreItem item;
  final VoidCallback? onEquip;
  final VoidCallback? onUnequip;
  final VoidCallback? onViewDetails;
  final bool isEquipped;

  const InventoryItemCard({
    super.key,
    required this.item,
    this.onEquip,
    this.onUnequip,
    this.onViewDetails,
    this.isEquipped = false,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 4,
      clipBehavior: Clip.antiAlias,
      child: InkWell(
        onTap: onViewDetails,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Item preview/image
            Expanded(
              flex: 3,
              child: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      _getTypeColor().withOpacity(0.3),
                      _getTypeColor().withOpacity(0.1),
                    ],
                  ),
                ),
                child: Stack(
                  children: [
                    // Preview content
                    Center(
                      child: _buildPreview(),
                    ),
                    
                    // Type badge
                    Positioned(
                      top: 8,
                      left: 8,
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: _getTypeColor(),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          item.typeDisplayName,
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 10,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                    
                    // Equipped indicator
                    if (isEquipped)
                      Positioned(
                        top: 8,
                        right: 8,
                        child: Container(
                          padding: const EdgeInsets.all(4),
                          decoration: const BoxDecoration(
                            color: Colors.green,
                            shape: BoxShape.circle,
                          ),
                          child: const Icon(
                            Icons.check_circle,
                            color: Colors.white,
                            size: 16,
                          ),
                        ),
                      ),
                  ],
                ),
              ),
            ),
            
            // Item details
            Expanded(
              flex: 2,
              child: Padding(
                padding: const EdgeInsets.all(12),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Item name
                    Text(
                      item.name,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    
                    const SizedBox(height: 4),
                    
                    // Item description
                    if (item.description != null)
                      Expanded(
                        child: Text(
                          item.description!,
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Colors.grey[600],
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    
                    const SizedBox(height: 8),
                    
                    // Action buttons
                    Row(
                      children: [
                        // Equip/Unequip button
                        Expanded(
                          child: _buildActionButton(context),
                        ),
                        
                        const SizedBox(width: 8),
                        
                        // Details button
                        SizedBox(
                          width: 36,
                          height: 28,
                          child: IconButton(
                            onPressed: onViewDetails,
                            icon: const Icon(Icons.info_outline, size: 16),
                            style: IconButton.styleFrom(
                              backgroundColor: Theme.of(context).colorScheme.surfaceContainerHighest,
                              padding: EdgeInsets.zero,
                            ),
                            tooltip: 'View Details',
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPreview() {
    // For now, show an icon based on the item type
    // In the future, this could load actual asset previews
    IconData iconData;
    switch (item.type) {
      case StoreItemType.cosmetic:
        iconData = Icons.style;
        break;
      case StoreItemType.gameUnlock:
        iconData = Icons.lock_open;
        break;
      case StoreItemType.flair:
        iconData = Icons.star;
        break;
      case StoreItemType.mysteryDraw:
        iconData = Icons.card_giftcard;
        break;
      case StoreItemType.tokenBoost:
        iconData = Icons.trending_up;
        break;
      case StoreItemType.skin:
        iconData = Icons.palette;
        break;
      case StoreItemType.effect:
        iconData = Icons.auto_awesome;
        break;
      case StoreItemType.powerup:
        iconData = Icons.flash_on;
        break;
      case StoreItemType.theme:
        iconData = Icons.color_lens;
        break;
      case StoreItemType.sound:
        iconData = Icons.volume_up;
        break;
    }

    return Icon(
      iconData,
      size: 48,
      color: _getTypeColor(),
    );
  }

  Color _getTypeColor() {
    switch (item.type) {
      case StoreItemType.cosmetic:
        return Colors.teal;
      case StoreItemType.gameUnlock:
        return Colors.amber;
      case StoreItemType.flair:
        return Colors.pink;
      case StoreItemType.mysteryDraw:
        return Colors.deepPurple;
      case StoreItemType.tokenBoost:
        return Colors.indigo;
      case StoreItemType.skin:
        return Colors.purple;
      case StoreItemType.effect:
        return Colors.orange;
      case StoreItemType.powerup:
        return Colors.red;
      case StoreItemType.theme:
        return Colors.blue;
      case StoreItemType.sound:
        return Colors.green;
    }
  }

  Widget _buildActionButton(BuildContext context) {
    if (isEquipped) {
      return SizedBox(
        height: 28,
        child: ElevatedButton(
          onPressed: onUnequip,
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.orange,
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(horizontal: 8),
            minimumSize: Size.zero,
            tapTargetSize: MaterialTapTargetSize.shrinkWrap,
          ),
          child: const Text(
            'UNEQUIP',
            style: TextStyle(
              fontSize: 10,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      );
    }

    return SizedBox(
      height: 28,
      child: ElevatedButton(
        onPressed: onEquip,
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.green,
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(horizontal: 8),
          minimumSize: Size.zero,
          tapTargetSize: MaterialTapTargetSize.shrinkWrap,
        ),
        child: const Text(
          'EQUIP',
          style: TextStyle(
            fontSize: 10,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }
}
